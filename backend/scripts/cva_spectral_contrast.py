
from scipy.signal import butter, lfilter
import librosa
from scipy.signal import savgol_filter
from scipy import ndimage
from scipy.ndimage import maximum_filter
from skimage.filters import threshold_otsu
import soundfile as sf
import numpy as np
import glob
import os
import warnings
import itertools
import sys
from silero_vad import read_audio, get_speech_timestamps, load_silero_vad
warnings.simplefilter('ignore')

model = load_silero_vad()


# CONSTANTS
SAMPLE_RATE = 16000  # used everywhere in project
FRAME_LENGTH = 1486  # corresponds to 93 ms at sample rate - recommended by librosa
SNR_THRESHOLD = 2
ENVEL_AVGOL_FILTER_POLYORDER = 0

# AUXILIARY FUNCTIONS


def interpolate_to_size(arr: np.array, size: int) -> np.array:
    return np.interp(
        np.arange(size)/SAMPLE_RATE,  # at frames
        np.linspace(0, size/SAMPLE_RATE, arr.shape[0]),  # available x
        arr  # values
    )


def normalize(arr, amin=-1, amax=1):
    return amin + (amax-amin) * (arr - arr.min()) / (arr.max() - arr.min())


def remove_short_segments(thresholded_level, min_duration):
    DURATION_THRESHOLD = int(min_duration * 16000)
    final_array = []
    for value, grp in itertools.groupby(thresholded_level):
        segment = list(grp)
        nseg = len(segment)
        if len(segment) < DURATION_THRESHOLD:
            level = final_array[-1] if len(final_array) else 0
        else:
            level = segment[-1]
        final_array.extend([level] * nseg)

    return np.array(final_array)


def get_thresholded_level(array):
    threshold = threshold_otsu(array)
    labels = array >= threshold
    final_level = labels.astype(int)
    return final_level


def estimate_envelope(
    x: np.ndarray,
    maximum_filter_window_size: int,
    savgol_filter_window_size: int,
    savgol_filter_polyorder: int,
) -> np.ndarray:

    if maximum_filter_window_size % 2 == 0:
        maximum_filter_window_size += 1

    if savgol_filter_window_size % 2 == 0:
        savgol_filter_window_size += 1

    envelope = np.abs(x)
    envelope = maximum_filter(envelope, size=maximum_filter_window_size)
    return savgol_filter(
        envelope,
        window_length=savgol_filter_window_size,
        polyorder=savgol_filter_polyorder,
    )


def normalize_around_zero(sound_arr: np.array) -> np.array:
    """
    Function to normalize sound wave to bounds about -1,1 keeping zero line intact
    """
    return sound_arr/max(sound_arr.max(), -sound_arr.min())


def get_snr(full_original_audio, voiced_flag_interpolated, Stft):
    S, phase = librosa.magphase(Stft)
    rms = interpolate_to_size(librosa.feature.rms(S=S, frame_length=FRAME_LENGTH)[0], full_original_audio.size)
    silence_flag = ~voiced_flag_interpolated
    SNR = rms[voiced_flag_interpolated].mean()/rms[silence_flag].mean()
    return SNR


def get_offset_snr(full_original_audio, Stft, speech_end, offset):
    S, phase = librosa.magphase(Stft)
    rms = interpolate_to_size(librosa.feature.rms(S=S, frame_length=FRAME_LENGTH)[0], full_original_audio.size)
    SNR = rms[speech_end - SAMPLE_RATE//2:speech_end].mean()/rms[offset:offset+SAMPLE_RATE//2].mean()
    return SNR


def response_begin_calculation(
        start_frame: int,
        CHARACTERISTIC_TIMESCALE_FRAMES: int,
        CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE: float,
        RESPONSE_ENVELOP_THRESHOLD: float,
        ONSET_ENV_METRICS_THRESHOLD: float,
        onset_env_metrics: np.array,
        contrast_voice_feature: np.array,
        envelope: np.array
) -> dict:

    frame_feature = np.sign((envelope[start_frame:] > RESPONSE_ENVELOP_THRESHOLD).astype(int) + (onset_env_metrics[start_frame:] > ONSET_ENV_METRICS_THRESHOLD).astype(int))
    response_anchor_frame = start_frame + np.argmax((contrast_voice_feature[start_frame:] == 1) * (frame_feature))

    if start_frame >= response_anchor_frame:
        response_anchor_frame = start_frame + CHARACTERISTIC_TIMESCALE_FRAMES
    response_min_envelope_value = envelope[start_frame:response_anchor_frame].min()
    response_envelop_correction_frame = response_anchor_frame - np.argmax(envelope[start_frame:response_anchor_frame][::-1] < CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE * response_min_envelope_value)

    # and this is for case jump was too far
    try:
        response_envelop_correction_frame += np.where(np.diff(np.sign(np.diff(envelope[response_envelop_correction_frame:response_anchor_frame]))) == 2)[0][-1] + 1
        # this where->diff->sign->diff finds local miinimum
    except IndexError:
        pass

    response_begin_time = response_envelop_correction_frame/SAMPLE_RATE
    return {'response_begin_time': response_begin_time, 'response_anchor_frame': response_anchor_frame, 'response_anchor_frame': response_anchor_frame, 'response_envelop_correction_frame': response_envelop_correction_frame}


def local_response_correction(metrics, magnitude, offset, contrast_voice_feature):
    response_begin_segment = offset + np.argmax(metrics[offset:] > 0)
    response_end_segment = response_begin_segment + np.argmax(metrics[response_begin_segment:] < 1)

    local_metrics = magnitude[offset:response_end_segment]
    try:
        local_level = get_thresholded_level(local_metrics)
        contrast_voice_feature = remove_short_segments(contrast_voice_feature, min_duration=0.05)
        # local_correction_one = offset + np.argmax(local_level > 0)
        simple_threshold = np.max(magnitude[offset:(response_begin_segment + offset)//2]) * 1.2
        local_correction_one = offset + np.argmax(magnitude[offset:response_end_segment] > simple_threshold)
        local_correction_two = local_correction_one + np.argmax(contrast_voice_feature[local_correction_one:] == 1)
    except (ValueError, IndexError):
        local_correction_two = offset + np.argmax(remove_short_segments(contrast_voice_feature[offset:], min_duration=0.05) == 1)
    return local_correction_two

# MAIN FUNCTION find_awake_time()


def add_plots(**kwargs):
    return
    # DO NOT PLOT IN PRODUCTION
    import plotly.graph_objects as go
    data = []
    for key, value in kwargs.items():
        if not isinstance(value, dict):
            continue
        if value.get("type") == 'plot':
            data.append(
                go.Scatter(
                    x=value["x"], y=value["y"], name=key
                )
            )
    fig = go.Figure(
        data=data
    )
    for t_key, t_value in kwargs["timings"].items():
        fig.add_shape(
            type='line',
            x0=t_value.get('x'),
            x1=t_value.get('x'),
            y0=-1, y1=1,
            line=dict(width=t_value.get('width'), dash=t_value.get("dash"), color=t_value.get("color"))
        )
    fig.update_layout(title=kwargs["filename_full"])
    fig.show()


def big_noise_missed_voice_correction(offset, local_correction_two, metrics):
    if metrics[local_correction_two:local_correction_two+SAMPLE_RATE//4].sum() == 0:
        metrics_voice_present = offset + np.argmax(metrics[offset:] > 0)
        return metrics_voice_present / SAMPLE_RATE
    else:
        return local_correction_two / SAMPLE_RATE


def find_awake_time(filename_full,
                    # CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH=3.18,
                    CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE=4.46,
                    RESPONSE_ENVELOP_THRESHOLD=0.45,
                    ONSET_ENV_METRICS_THRESHOLD=0.007,
                    ENVEL_MAXIMUM_FILTER_WINDOW_SIZE=629,
                    ENVEL_SAVGOL_FILTER_WINDOW_SIZE=9):

    # LOAD SOUND AND CALCULATE OFFSETS

    full_original_audio, _ = librosa.load(filename_full, sr=SAMPLE_RATE)

    path_mp3 = list(glob.glob(f'{os.path.dirname(filename_full)}/*mp3'))[0]
    y_speech, _ = librosa.load(path_mp3, sr=None)
    correlations = np.correlate(normalize_around_zero(full_original_audio[:y_speech.size*2]), normalize_around_zero(y_speech[:]), mode='valid')
    offset = np.argmax(correlations) + y_speech[:].size
    speech_end = offset - np.argmax(np.abs(y_speech)[::-1] > 0.01)
    speech_end_time = speech_end / SAMPLE_RATE

    filename_part = filename_full.replace('_full.wav', '.wav')
    response_wav = full_original_audio[offset:]

    sf.write(file=filename_part, data=response_wav, samplerate=SAMPLE_RATE, format='WAV', subtype='PCM_16')

    vad_response_candidates = []
    silero_wav = read_audio(filename_full)[offset:]
    speech_timestamps = get_speech_timestamps(silero_wav,
                                              model,
                                              threshold=0.22,
                                              min_speech_duration_ms=96,
                                              min_silence_duration_ms=96,  # 192 为唤醒相应时间, 160 刚改成96 - 2025.1.22
                                              speech_pad_ms=0,
                                              sampling_rate=SAMPLE_RATE,
                                              return_seconds=False,  # Return speech timestamps in seconds (default is samples)
                                              )
    for ts in speech_timestamps:
        vad_response_candidates.append(offset + ts["start"])

    # NORMALIZATION
    # full_original_audio[:offset] = normalize_around_zero(full_original_audio[:offset])
    # full_original_audio[offset:] = normalize_around_zero(full_original_audio[offset:])

    # CALCULATE REQUIRED VALUES

    f0, voiced_flag, voicing_probability = librosa.pyin(y=full_original_audio, sr=SAMPLE_RATE, frame_length=FRAME_LENGTH, fmin=65, fmax=2094)
    voiced_flag_interpolated = interpolate_to_size(voiced_flag, full_original_audio.size).astype(bool)
    # if voiced_flag_interpolated[offset:].sum() == 0:
    #     print(-1)
    #     return speech_end_time, -1, 0
    voicing_probability_interp = interpolate_to_size(voicing_probability, full_original_audio.size)
    envelope = estimate_envelope(
        x=np.abs(full_original_audio),
        maximum_filter_window_size=ENVEL_MAXIMUM_FILTER_WINDOW_SIZE,  # from experimenting a bit
        savgol_filter_window_size=ENVEL_SAVGOL_FILTER_WINDOW_SIZE,  # from experimenting a bit
        savgol_filter_polyorder=ENVEL_AVGOL_FILTER_POLYORDER,  # from experimenting a bit
    )
    Stft = librosa.stft(full_original_audio, n_fft=FRAME_LENGTH)
    S = np.abs(Stft)
    contrast = librosa.feature.spectral_contrast(S=S, sr=SAMPLE_RATE, n_fft=FRAME_LENGTH)
    feature_contrast = contrast.sum(axis=0)
    threshold = threshold_otsu(feature_contrast)
    voice_labels = feature_contrast >= threshold
    voice = voice_labels.astype(int)
    voice = np.hstack((voice[:-1] * voice[1:], voice[-1]))  # remove too short segments
    contrast_voice_feature = interpolate_to_size(voice, full_original_audio.size)

    if remove_short_segments(contrast_voice_feature[offset:], min_duration=0.05).sum() == 0:
        if len(vad_response_candidates) > 0:
            add_plots(
                filename_full=filename_full,
                full_original_audio={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': full_original_audio, 'type': 'plot'},
                contrast_voice_feature={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': normalize(contrast_voice_feature, 0, 1), 'type': 'plot'},
                timings={
                    'start_frame': {
                        'x': offset/SAMPLE_RATE,
                        'width': 2,
                        'dash': 'solid',
                        'color': 'black'
                    },
                    'speech_end_time': {
                        'x': speech_end_time,
                        'width': 2,
                        'dash': 'solid',
                        'color': 'red'
                    },
                    'response_begin_time': {
                        'x': vad_response_candidates[0]/SAMPLE_RATE,
                        'width': 2,
                        'dash': 'solid',
                        'color': 'blue'
                    }
                }
            )
            print(f'{vad_response_candidates[0]/SAMPLE_RATE - speech_end_time:.4f}')
            return speech_end_time, vad_response_candidates[0]/SAMPLE_RATE, 0
        else:
            print(-1)
            add_plots(
                filename_full=filename_full,
                full_original_audio={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': full_original_audio, 'type': 'plot'},
                contrast_voice_feature={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': normalize(contrast_voice_feature, 0, 1), 'type': 'plot'},
                timings={
                    'start_frame': {
                        'x': offset/SAMPLE_RATE,
                        'width': 2,
                        'dash': 'solid',
                        'color': 'black'
                    },
                    'speech_end_time': {
                        'x': speech_end_time,
                        'width': 2,
                        'dash': 'solid',
                        'color': 'red'
                    }
                }
            )
            return speech_end_time, -1, 0

    # SIGNAL_NOISE_RATIO TO DECIDE WHICH ALGO
    # snr = get_snr(full_original_audio, voiced_flag_interpolated, Stft)
    snr = get_offset_snr(full_original_audio, Stft, speech_end, offset)
    if snr > SNR_THRESHOLD:
        onset_env = librosa.onset.onset_strength(y=full_original_audio, sr=SAMPLE_RATE,
                                                 aggregate=np.median,
                                                 fmax=4000, n_mels=256)
        onset_env = normalize(interpolate_to_size(onset_env, full_original_audio.size), 0, 1)
        onset_env_metrics = normalize(voicing_probability_interp * onset_env, 0, 1)
        response_begin_time_data = response_begin_calculation(offset, FRAME_LENGTH, CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE,
                                                              RESPONSE_ENVELOP_THRESHOLD, ONSET_ENV_METRICS_THRESHOLD, onset_env_metrics, contrast_voice_feature, envelope)
        response_begin_time = response_begin_time_data["response_begin_time"]
        if len(vad_response_candidates) > 0:
            vad_response_time_candidates = [x/SAMPLE_RATE for x in vad_response_candidates]
            middle_point = (response_begin_time + speech_end_time) * 0.5
            acceptable_vad_correction = list(filter(lambda x: x > middle_point, vad_response_time_candidates))
            if len(acceptable_vad_correction) > 0:
                vad_response_time = acceptable_vad_correction[0]
                response_begin_time = min(response_begin_time, vad_response_time)

        add_plots(
            filename_full=filename_full,
            full_original_audio={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': full_original_audio, 'type': 'plot'},
            contrast_voice_feature={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': normalize(contrast_voice_feature, 0, 1), 'type': 'plot'},
            timings={
                'start_frame': {
                    'x': offset/SAMPLE_RATE,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'black'
                },
                'speech_end_time': {
                    'x': speech_end_time,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'red'
                },
                'response_begin_time': {
                    'x': response_begin_time,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'blue'
                }
            }
        )

    else:
        # HIGH NOISE ALGO
        # mfccs = librosa.feature.mfcc(y=full_original_audio, sr=16000, n_fft=256, hop_length=64, n_mfcc=13)
        # metrics = interpolate_to_size(mfccs[0, :], full_original_audio.size)
        # metrics_no_short = remove_short_segments(get_thresholded_level(metrics))
        # response_begin = offset + np.argmax(metrics_no_short[offset:] == 1)
        # response_begin_time = response_begin / SAMPLE_RATE
        S = librosa.feature.melspectrogram(y=full_original_audio, sr=SAMPLE_RATE, n_mels=128, fmax=8000)
        S_dB = librosa.power_to_db(S, ref=np.max)
        sobel_h = ndimage.sobel(S_dB, 0)
        sobel_v = ndimage.sobel(S_dB, 1)
        magnitude = interpolate_to_size(np.sqrt(sobel_h**4 * sobel_v**2).sum(axis=0), full_original_audio.size)
        magnitude *= 0.5 / np.max(magnitude)
        magnitude = estimate_envelope(magnitude, maximum_filter_window_size=512, savgol_filter_window_size=10, savgol_filter_polyorder=0)
        metrics = get_thresholded_level(magnitude)
        local_correction_two = local_response_correction(metrics, magnitude, offset, contrast_voice_feature)
        response_begin_time = local_correction_two / SAMPLE_RATE  # 0.5 * (local_correction_one + local_correction_two) / SAMPLE_RATE
        response_begin_time = big_noise_missed_voice_correction(offset, local_correction_two, metrics)
        add_plots(
            filename_full=filename_full,
            full_original_audio={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': full_original_audio, 'type': 'plot'},
            metrics={'x': np.arange(full_original_audio.size)/SAMPLE_RATE, 'y': normalize(metrics, 0, 1), 'type': 'plot'},
            timings={
                'start_frame': {
                    'x': offset/SAMPLE_RATE,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'black'
                },
                'speech_end_time': {
                    'x': speech_end_time,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'red'
                },
                'response_begin_time': {
                    'x': response_begin_time,
                    'width': 2,
                    'dash': 'solid',
                    'color': 'blue'
                }
            }
        )

    print(f'{response_begin_time - speech_end_time:.4f}')
    return speech_end_time, response_begin_time


if __name__ == '__main__':
    find_awake_time(sys.argv[1],
                    # CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH=3.18,
                    CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE=4.46,
                    RESPONSE_ENVELOP_THRESHOLD=0.45,
                    ONSET_ENV_METRICS_THRESHOLD=0.007,
                    ENVEL_MAXIMUM_FILTER_WINDOW_SIZE=629,
                    ENVEL_SAVGOL_FILTER_WINDOW_SIZE=9)
