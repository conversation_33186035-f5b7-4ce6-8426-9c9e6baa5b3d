import os.path
import subprocess
from os.path import basename
from production_paddleocr import *

import pandas as pd

EXCEL_PATH = r"C:\Users\<USER>\Desktop\副本奕派_测试结果汇总.xlsx"

def execl_test(result_list=None):
    df = pd.read_excel(EXCEL_PATH, sheet_name='Sheet1')
    def extract_number(file_name):
        match = re.search(r'-(\d+)\.jpg$', file_name)
        if match:
            return int(match.group(1))
        return 0

    with open('ocr.txt', 'w', encoding="utf-8") as file:
        for index, row in df.iterrows():
            if index < 720:
                continue
            if result_list:
                if row["本次结果id"] not in result_list:
                    continue
            text = row["语料文本"]
            expect_result = row["预期结果"]
            if isinstance(expect_result, float):
                expect_result = ""
            asr_text = row["车机响应识别"]
            image1 = row["pre_image"]
            image2 = row["post_image"]
            ocr_dir = os.path.dirname(image1)
            files_with_dash = [f for f in os.listdir(ocr_dir) if '-' in f]
            global_path = os.path.join(ocr_dir, files_with_dash[0].replace("-0", "-*"))
            print(global_path)
            source = sorted(glob.glob(global_path), key=extract_number)
            ocr = Recognition(source, text)
            ocr.run()
            file.writelines(f"text: {text}\n")
            file.writelines(f"result:  {ocr.result}\n")
            file.writelines(f"path:  {ocr_dir}\n")
            file.writelines("------------------------------------\n")
            file.flush()

execl_test()
