import pandas as pd
import re

# 读取文本文件
with open('example2.txt', 'r', encoding='utf-8') as file:
    text = file.read()


# 定义一个函数来解析每一段文本
def parse_text(text):
    # 按照每一段分割文本
    blocks = text.split('------------------------------------')

    parsed_data = []

    for block in blocks:
        # 初始化每一段的数据
        data = {}

        # 使用正则表达式提取相关信息
        data['语料文本'] = re.search(r'语料文本:\s*(.*)', block).group(1).strip() if re.search(r'语料文本:\s*(.*)',
                                                                                               block) else ""
        data['预期结果'] = re.search(r'预期结果:\s*(.*)', block).group(1).strip() if re.search(r'预期结果:\s*(.*)',
                                                                                               block) else ""
        data['ASR'] = re.search(r'ASR:\s*(.*)', block).group(1).strip() if re.search(r'ASR:\s*(.*)', block) else ""
        data['结果'] = re.search(r'结果：\s*(.*)', block).group(1).strip() if re.search(r'结果：\s*(.*)', block) else ""
        data['reasoning'] = re.search(r'reasoning：\s*(.*)', block).group(1).strip() if re.search(r'reasoning：\s*(.*)',
                                                                                                 block) else ""
        data['voice_confidence'] = re.search(r'voice_confidence：\s*(\d+)', block).group(1).strip() if re.search(
            r'voice_confidence：\s*(\d+)', block) else ""
        data['ui_confidence'] = re.search(r'ui_confidence：\s*(\d+)', block).group(1).strip() if re.search(
            r'ui_confidence：\s*(\d+)', block) else ""

        # 将解析后的数据加入到列表中
        parsed_data.append(data)

    return parsed_data


# 解析文本
data = parse_text(text)

# 将数据转化为 DataFrame
df = pd.DataFrame(data)

# 将 DataFrame 写入 Excel 文件
df.to_excel('output_repeat.xlsx', index=False, engine='openpyxl')

print("数据已成功写入 Excel 文件！")