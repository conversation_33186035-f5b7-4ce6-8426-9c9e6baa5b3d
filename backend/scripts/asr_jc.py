import time
import os
import pandas as pd
import dspy
from openai import OpenAI

class TestSignature(dspy.Signature):
    """
    你是一名车机交互领域的文本纠错专家，请按以下规则修正ASR转换文本中的同音/近音错误：
    【核心原则】
    1. 仅替换同音/近音字，禁止增删字符（即使存在其他错误）
    2. 修正后语义和字数必须与原句完全一致
    3. 无法修正时输出原句

    【优先级】
    1️.指令状态词（已/正在/请/将/未）
       ▶ 新增状态词替换表：
          {以→已, 情→请, 正再→正在, 卫→未, zai→在}
       ▶ 强制检测：当原句含有关闭/打开/设置等动词时，必须检测前导状态词
    2. 车机控制指令（空调/导航/音乐/座椅/除雾/除霜/雨刮/制冷/制热/近光/远光等）
       ▶ 示例：空条→空调 | 除双→除霜 | 雨刷→雨刮
    3️. 数字/单位/方位词
       ▶ 示例：二挡→二档 | 十风量→十档风量（需保持字数故不修正）
    4️. 保留口语化特征（嗯/啊/哦等）

    修正步骤：

    1. 【字数基准计算】
        - 统计原句纯文本字数（去除所有标点符号后的中文字符数量）
        - 示例：原句"温度，调高！" → 实际字数4（"温度调高"）

    2. 【错误检测优先级】
       (1) 指令状态词：已/正在/请/将/未（强制优先检测）
       (2) 车控核心名词：空调/除霜/雨刮等（按当前指令类型加权）
       (3) 数值单位：档/度/公里等
       (4) 方位词：左前/右后等

    3. 【替换可行性判断】
       - 建立同音字替换对照表：
         空调相关：{条:调, 冻:动, 双:霜...}
         导航相关：{撸:路, 像:向...}
       - 检查是否存在可替换且不改变字数的方案：
         ▶ 合格替换："三档"→"三档"（同字数）
         ▶ 无效替换："二挡"→"二档"（需保持原字数）

    4. 【安全替换执行】
       for 每个候选错误词:
         if 替换后字数相同 AND 符合当前指令场景优先级:
           执行替换并记录修改点
         else:
           保留原词
       完成所有可能替换后，重新计算纯文本字数

    5. 【最终校验】
       if 新字数 == 原基准字数 AND 至少修正一个有效错误：
         输出修正句
       else：
         维持原句
    """
    # user_question: str = dspy.InputField(prefix="指令", desc="用户指令")
    asr_text: str = dspy.InputField(prefix="原句", desc="通过ASR转换得到的文本")
    text: str = dspy.OutputField(prefix="修正", desc="纠正后的文本")


def init_llm():
    llm_client = OpenAI(
        api_key="403230cf3413434fbad5c7563005b73b",
        base_url="http://183.66.251.10:19000/v1",
    )
    models = llm_client.models.list()
    model = models.data[0].id

    lm = dspy.LM(
        f"openai/deepseek-r1:14b",
        cache=False,
        api_key="403230cf3413434fbad5c7563005b73b",
        api_base="http://183.66.251.10:19000/v1",
        seed=200,
    )
    dspy.disable_logging()
    dspy.configure(lm=lm)

EXCEL_PATH = r"D:\testdata\东风语料Test_第1轮_单次对话.xlsx"


def execl_test(result_list=None):
    init_llm()
    df = pd.read_excel(EXCEL_PATH, sheet_name='Sheet1')
    func = dspy.Predict(TestSignature)
    with open('asr.txt', 'w', encoding="utf-8") as file:
        for index, row in df.iterrows():
            if index == 0:
                continue
            if result_list:
                if row["本次结果id"] not in result_list:
                    continue
            text = row["语料文本"]
            expect_result = row["预期结果"]
            if isinstance(expect_result, float):
                expect_result = ""
            asr_text = row["车机响应识别"]
            print("语料文本：", text)
            file.writelines(f"语料文本:  {text}\n")
            print("ASR: ", asr_text)
            file.writelines(f"ASR:  {asr_text}\n")

            result = func(
                asr_text=asr_text,
                user_question=text)
            print(result)
            file.writelines(f"结果： {result.text}\n")
            print("------------------------------------")
            file.writelines("------------------------------------\n")
            file.flush()
    return

# init_llm()
# print(time.time())
# func = dspy.Predict(TestSignature)
# result = func(asr_text="唐宋八大家是哪几位?正在查阅资料,内容即将生成。唐宋八大家,指唐太的韩玉,柳宗元,宋代的欧阳修,苏巡,苏氏,苏哲,王安石,曾公等八位散文家。",
#               user_question="唐宋八大家是哪几位")
# print(result)
# print(time.time())
if __name__ == "__main__":
    execl_test()
