import os
import pandas as pd
from openai import OpenAI
import dspy
from typing import Literal, Optional
from minio import Minio

client = Minio(
    endpoint="183.66.251.10:39000",
    access_key="qHGNbLRWtJcfJsDkl3LK",
    secret_key="qmkA57ujunxw2cEpmPCVfsHKtXdbRAfv0SYvtdQC",
    secure=False,
)
LLM_JUDGE_HAS_IMAGE = True


class EvaluateSmartCockpitSignature(dspy.Signature):
    """
        您是中国汽车技术研究中心的首席技术官，请作为一个公正的裁判，评估智能车机系统对用户提问或指令的回答质量。您应根据**裁决规则**，和给定的**预期结果**评估系统是否通过有效且高效的互动提供了正确的回答或响应。
        请遵循以下认知步骤，系统地评估智能车机系统对用户提问或指令的回答质量。在进入每个新步骤之前，请提供详细的推理和解释。
        ## 认知操作步骤
        1. 目标明确: 清晰定义目标或预期的常识性结果
        2. 问题分解: 将问题分解为关键组成部分和相关因素
        3. 信息过滤: 分析文本和图片,专注于最重要的常识要素,忽略无关细节
        4. 重新组织: 重新安排事实和视角以澄清冲突或模糊之处
        5. 模式识别: 识别与其他常识场景或先例的相似之处
        6. 抽象提取: 提取可应用于此情况的更广泛常识原则
        7. 归纳推广: 将识别的原则应用于当前场景和潜在的未来案例
        8. 整合总结: 将所有视角和原则整合为最终的常识性决定,并由中文输出总结结果。
        9. 判断评分：根据输入的信息和输出的结果给出整个判断的置信度，置信度越高代表判断对错的准确度越高。
        ## 裁决规则
        按照以下规则进行裁决：
        1. 避免位置偏差：独立评估每个回答，确保回答顺序不影响判断。
        2. 质量优先原则：注重回答的相关性和质量，不以回答长度为标准。一条有效回答应准确满足用户需求，而非单纯详尽。例如，系统回复“好的 ...”、“OK”等，应倾向于认为它已正确响应并在积极推动用户需求的实现，可视为合格响应。
        3. 容错与纠错：确认系统是否准确回应提问或正确执行指令。如有轻微误差，考察系统是否能通过后续互动快速弥补，例如在初次误解后及时调整，则视为合格。
        4. 情境适应性：判断系统是否能因应具体情境调整响应，尤其在复杂或多重指令下，确保具备合理应变能力。
        5. 公平审慎：对智驾系统的回应保持公平，避免草率或苛刻评价；在系统提供合理的引导时，倾向认为其已正确响应并在推动用户需求的实现。
        6. 图片分析准则：
            - **准确性**: 系统能否准确描述图片中的内容及其变化。
            - **一致性**: 描述的内容是否与图片一致，无明显错误。
            - **详细程度**: 系统提供的描述是否足够详细，能够帮助理解图片的变化。
            - **相关性**: 描述的内容是否与用户的问题或指令直接相关。
        7. 情感分析：当用户问题无明确指令，为情感表达时，若车机前后截图无明显变化，为正常现象，结合情感语境给予积极回复即可倾向于车机已正确响应
        8. 独立分析：**系统语音分析结果和系统语音分析置信度**由用户问题、预期结果、车机回答分析得出。**系统界面分析结果和系统界面分析置信度**由用户问题、指令前屏幕截图、指令后屏幕截图分析得出。语音和界面独立分析，不互相依赖,分别得出结论.系统的语音响应由 ASR（自动语音识别）生成，可能会出现同音字或其他细微偏差。分析时，应忽略这些偏差，并基于最佳解释原则进行推断，以准确评估系统的实际行为与预期行为的一致性。
        9. 置信度评分：给出分析结果的置信度评分，范围在0-10分，0-3分表示裁决结果不可信，4-7分表示裁决结果不一定准确，8-10分代表裁决结果的准确度很高
        10.语言分析：根据本地化需求进行分析，确保在不同语言和地区下准确评估, 并使用中文回复原因。
    """

    user_question: str = dspy.InputField(prefix="用户问题", desc="用户提问或指令或闲聊")
    language: str = dspy.InputField(prefix="测试语言", desc="用户语言、车机语音语言和屏幕显示语言")

    expected_behavior: str = dspy.InputField(
        prefix="预期结果", desc="预期的车机系统行为或响应"
    )

    asr_response: str = dspy.InputField(
        prefix="车机回答", desc="语音识别的智能驾驶系统语音回复"
    )

    pre_interaction_screenshot: Optional[dspy.Image] = dspy.InputField(
        prefix="指令前屏幕截图", desc="指令前屏幕截图"
    )
    post_interaction_screenshot: Optional[dspy.Image] = dspy.InputField(
        prefix="指令后屏幕截图", desc="指令前屏幕截图"
    )

    voice_analysis: str = dspy.OutputField(
        prefix="系统语音分析过程", desc="系统语音分析过程")

    voice_result: Literal["Correct", "Incorrect"] = dspy.OutputField(
        prefix="系统语音分析结果", desc="系统语音分析结果，由用户问题、预期结果、车机回答分析得出。"
    )
    global LLM_JUDGE_HAS_IMAGE
    if LLM_JUDGE_HAS_IMAGE:
        ui_analysis: str = dspy.OutputField(
            prefix="系统界面分析过程", desc="系统界面分析过程")

        ui_result: Literal["Correct", "Incorrect"] = dspy.OutputField(
            prefix="系统界面分析结果", desc="系统界面分析结果，由用户问题、指令前屏幕截图、指令后屏幕截图分析得出。"
        )

        ui_confidence: int = dspy.OutputField(
            prefix="系统界面分析置信度", desc="由用户问题、预期结果、指令前屏幕截图、指令后屏幕截图、系统界面分析过程、系统界面分析结果给出置信度"
        )

    voice_confidence: int = dspy.OutputField(
        prefix="系统语音分析置信度", desc="由用户问题、车机回答、预期结果、系统语音分析过程、系统语音分析结果给出置信度"
    )


class EvaluateSmartCockpit(dspy.Module):
    def __init__(self):
        super().__init__()
        self.judgment = dspy.ChainOfThought(EvaluateSmartCockpitSignature)

    def forward(
            self,
            user_question,
            expected_behavior,
            asr_response,
            pre_interaction_screenshot=None,
            post_interaction_screenshot=None,
    ):
        global LLM_JUDGE_HAS_IMAGE
        wu_flag = False
        if not expected_behavior:
            expected_behavior = ""
        else:
            lines = expected_behavior.split(" --- ")
            for line in lines:
                if "屏幕显示" in line and line.strip().endswith("无"):
                    wu_flag = True
                    LLM_JUDGE_HAS_IMAGE = False
                    if not asr_response:
                        pre_result = dspy.Prediction(judgment="Incorrect",
                                                     reasoning="没有语音结果并且不参考屏幕显示结果，判断为不通过",
                                                     voice_confidence=10, ui_confidence=10)
                        return pre_result
        if not pre_interaction_screenshot or not post_interaction_screenshot:
            wu_flag = True
            LLM_JUDGE_HAS_IMAGE = False

        u_result, u_analysis = "", ""
        ui_confidence = None

        judgement = self.judgment(
            user_question=user_question,
            language="russian",
            expected_behavior=expected_behavior,
            asr_response=asr_response,
            pre_interaction_screenshot=pre_interaction_screenshot,
            post_interaction_screenshot=post_interaction_screenshot,
        )
        v_result, v_analysis = judgement.voice_result, judgement.voice_analysis
        if not wu_flag:
            u_result, u_analysis = judgement.ui_result, judgement.ui_analysis
            ui_confidence = judgement.ui_confidence

        final_result = v_result
        if u_result:
            final_result = (
                "Incorrect"
                if judgement.voice_result == "Incorrect"
                   and judgement.ui_result == "Incorrect"
                else "Correct"
            )
        final_reasoning = v_analysis
        if u_analysis:
            final_reasoning += u_analysis

        pre_result = dspy.Prediction(judgment=final_result, reasoning=final_reasoning,
                                     voice_confidence=judgement.voice_confidence, ui_confidence=ui_confidence)
        LLM_JUDGE_HAS_IMAGE = True
        return pre_result


def init_llm():
    try:
        llm_client = OpenAI(
            api_key="403230cf3413434fbad5c7563005b73b",
            base_url="http://183.66.251.10:38000/v1",
        )
        models = llm_client.models.list()
        model = models.data[0].id

        lm = dspy.LM(
            f"openai/{model}",
            cache=False,
            api_key="403230cf3413434fbad5c7563005b73b",
            api_base="http://183.66.251.10:38000/v1",
            seed=200,
        )
        dspy.configure(lm=lm)

        evaluate_program = EvaluateSmartCockpit()
        return evaluate_program
    except Exception as e:
        return None


EXCEL_PATH = r"D:\测试数据\奇瑞-003_第2轮_单次对话.xlsx"


def execl_test(result_list=None):
    evaluate_program = init_llm()
    df = pd.read_excel(EXCEL_PATH, sheet_name='Sheet1')
    for index, row in df.iterrows():
        if index == 0:
            continue
        if result_list:
            if row["本次结果id"] not in result_list:
                continue
        text = row["语料文本"]
        expect_result = row["预期结果"]
        if isinstance(expect_result, float):
            expect_result = ""
        asr_text = row["车机响应识别"]
        if isinstance(asr_text, float):
            asr_text = ""
        image1 = row["pre_image"]
        image2 = row["post_image"]
        image1_data = ""
        image2_data = ""
        if os.path.exists(image1):
            image1_data = dspy.Image.from_file(image1)
        if os.path.exists(image2):
            image2_data = dspy.Image.from_file(image2)
        print("语料文本：", text)
        print("预期结果: ", expect_result)
        print("ASR: ", asr_text)
        judgment = evaluate_program.forward(
            user_question=text,
            expected_behavior=expect_result,
            asr_response=asr_text,
            pre_interaction_screenshot=image1_data,
            post_interaction_screenshot=image2_data
        )

        # with open("output.txt", "w", encoding="utf-8") as f:
        #     with redirect_stdout(f):
        print(judgment)
        print("------------------------------------")
    return


# evaluate_program = init_llm()
# text = "我马上要升职加薪了"
# expect_result = "###检查点1:语音回复 系统能够识别指令并给出回复 ###检查点 2:屏幕显示 无"
# asr_text = ""
# image1 = r"D:\project\OCR\微信图片_20250107152502.jpg"
# image2 = r"D:\project\OCR\微信图片_20250107152513.jpg"
# judgment = evaluate_program.forward(
#     user_question=text,
#     expected_behavior=expect_result,
#     asr_response=asr_text,
#     pre_interaction_screenshot="",
#     post_interaction_screenshot="",
# )
# print(judgment)
# dspy.inspect_history(n=1)
result = [
    "test_result_162",
    "test_result_153",
    "test_result_154",
]
execl_test()
