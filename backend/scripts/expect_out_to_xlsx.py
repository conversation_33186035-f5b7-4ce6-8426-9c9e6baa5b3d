import dspy
from openai import OpenAI
import pandas as pd
import time
from datetime import datetime
import openpyxl
from typing import Literal
import dspy

def log_time(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    print(f"[{timestamp}] {message}")


class EvaluateArabicTextRecognition(dspy.Signature):
    """Create well-defined checkpoints to evaluate feedback from a car machine given a user's command.

    ### Steps:
    - Understand the user command.
    - Define what logical and expected response the car machine should give for this command, considering the voice response and screen display components.
    - Ensure the checkpoints cover how the voice response should be structured and what information should be displayed on the screen if necessary.

    ### Output Format:
    The output should be a JSON object containing two key checkpoints - "voice_response" and "screen_display" - detailing the expected voice feedback and any changes to the screen display. Utilize descriptive sentences for each key, ensuring the checkpoints are concise and accurate.

    ### Examples:
    ```
    example 1:
    "user_command":"导航到春熙路"
    output:
    {
    "voice_response": "车机回复已导航相关的路线或提示用户选择具体地点",
    "screen_display": "车机屏幕内容发生变化，导航搜索春熙路相关的地点"。
    }

    example 2:
    "user_command":"打开主驾车窗"
    output:
    {
    "voice_response": "车机回复已打开车窗或提示用户注意安全事项",
    "screen_display": "车机屏幕内容无变化"。
    }

    example 3:
    "user_command":"请帮我挑选一部小朋友可以看懂的电影"
    output:
    {
    "voice_response": "车机回复搜索到的电影信息，可能包括具体推荐的电影名称、简介和推荐的理由",
    "screen_display": "屏幕出现一些电影信息供用户选择"。
    }
    ```

    ### Notes:
    - Use Chinese for the JSON fields "voice_response" and "screen_display".
    - Do not forget to include specific and relevant details to each command and its response.
    - The checkpoints must precisely describe the anticipated outcome to offer clear guidance.
    """
    user_command: str = dspy.InputField(desc="User's voice command or query")
    checkpoints: dict[Literal["voice_response", "screen_display"], str] = (
        dspy.OutputField(
            desc="Predicted car machine response checkpoints, including voice response and screen display."
        )
    )


class EvaluateArabicTextRecognitionModule(dspy.Module):
    def __init__(self):
        super().__init__()
        self.judgment = dspy.ChainOfThought(EvaluateArabicTextRecognition)

    def forward(self, user_command):
        try:
            start_time = time.time()
            result = self.judgment(user_command=user_command)
            end_time = time.time()
            # log_time(f"处理耗时: {end_time - start_time:.2f}秒")
            return result
        except Exception as e:
            log_time(f"错误：{e}")
            return None


def process_xlsx(input_file, output_file):
    """处理xlsx文件并保存结果"""
    try:
        log_time("开始读取xlsx文件")
        # 读取xlsx文件
        workbook = openpyxl.load_workbook(input_file)
        ws = workbook.active
        sheet = workbook.worksheets[0]
        # log_time(f"成功读取xlsx文件，共 {len(df)} 条数据")

        # 配置OpenAI客户端
        log_time("开始配置OpenAI客户端")
        OPENAI_API_KEY = "403230cf3413434fbad5c7563005b73b"
        OPENAI_API_BASE = "http://183.66.251.10:38000/v1"
        client = OpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)
        models = client.models.list()
        model = models.data[0].id
        log_time("OpenAI客户端配置完成")

        # 配置dspy模型
        log_time("开始配置dspy模型")
        lm = dspy.LM(
            f"openai/{model}",
            cache=False,
            api_key=OPENAI_API_KEY,
            api_base=OPENAI_API_BASE,
            seed=200,
        )
        dspy.configure(lm=lm)
        log_time("dspy模型配置完成")
        print(" ")

        # 创建评估模块
        evaluate_program = EvaluateArabicTextRecognitionModule()

        # 存储结果的列表
        results = []

        # 处理每一行数据
        total_start_time = time.time()
        index = 0
        for row in range(2, sheet.max_row + 1):
            try:
                val = sheet[f"A{row}"].value
                log_time(f"开始处理第 {index + 1} 条数据: {val}")
                result = evaluate_program.forward(user_command=val)

                if result:
                    value = "###检查点1：语音回复\n" + result.checkpoints["voice_response"] + "\n" \
                        + "###检查点2：屏幕显示\n" + result.checkpoints["screen_display"]
                    # for row, value in enumerate(data, start=1):
                    ws.cell(row=row, column=3, value=value)
                    print(f"预期结果：\n{value}\n")
                    # log_time(f"第 {index + 1} 条数据处理成功")
                else:
                    # results.append("处理失败")
                    ws.cell(row=row, column=3, value="处理失败")
                    log_time(f"第 {index + 1} 条数据处理失败")

                # 添加短暂延迟以避免可能的速率限制
                time.sleep(1)
                index += 1

            except Exception as e:
                log_time(f"处理第 {index + 1} 条数据时出错: {e}")
                results.append("处理出错")

        total_end_time = time.time()
        log_time(f"所有数据处理完成，总耗时: {total_end_time - total_start_time:.2f}秒")


        # 保存结果到新的xlsx文件
        workbook.save(output_file)
        log_time(f"处理完成，结果已保存到: {output_file}")

    except Exception as e:
        log_time(f"处理xlsx文件时出错: {e}")


if __name__ == "__main__":
    input_file = "expect_result.xlsx"  # 输入xlsx文件路径
    output_file = "expect_result_out.xlsx"  # 输出xlsx文件路径

    log_time("程序启动")
    process_xlsx(input_file, output_file)
    log_time("程序结束")