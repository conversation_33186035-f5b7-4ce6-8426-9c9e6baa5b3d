import os
import time
import site
import re
from mutagen.wave import WAVE

os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

from faster_whisper import WhisperModel

ASR_BLACKLISTS = [
    "请不吝点赞.*",
    "感谢观看*",
    "Multiple Translation*",
    "请把这段音频转为简体中文*"
]
HOT_WORDS = ("导航,途径点,主驾,副驾,座椅,加热,按摩,除雾,除霜,通风,设置,空调,前排,后排,左后,右后,外循环,内循环,制冷,制热,左前,右前,近光,"
             "远光,示廓灯,后雾灯,前雾灯,搞定,明白,清楚,调高,调低,调亮,调暗,调节,湿滑,全车,车窗")


def check_blacklist(text):
    for re_str in ASR_BLACKLISTS:
        if re.match(re_str, text):
            return True
    return False


def delete_blacklist(text):
    for re_str in ASR_BLACKLISTS:
        if re.match(re_str, text):
            return re.sub(re_str, "", text)
    return text


class FasterWhisperASR():
    model_size = "large-v3"
    cublas_path = ""
    cudnn_path = ""
    # initial_prompt = "音频为车机响应,不要胡乱输出和发散,有合适的标点符号."
    initial_prompt = "you need to recognize a piece of Chinese audio from the car's response and convert it to Simplified Chinese."

    def __init__(self, gpu=False):
        if len(site.getsitepackages()) > 1 and gpu:
            site_path = site.getsitepackages()[1]
            self.cublas_path = os.path.join(site_path, r"nvidia\cublas\bin")
            self.cudnn_path = os.path.join(site_path, r"nvidia\cudnn\bin")
            env_path = os.environ["PATH"]
            if self.cublas_path not in env_path or self.cudnn_path not in env_path:
                os.environ["PATH"] = f"{self.cublas_path};{self.cudnn_path};{env_path}"
        # Run on GPU with FP16
        if gpu:
            self.model = WhisperModel(self.model_size, device="cuda", compute_type="float16")
        else:
            self.model = WhisperModel(self.model_size, device="cpu", compute_type="int8")
        print(f"FasterWhisperASR model load. use_gpu={gpu}")
        return

    def identify(self, audio_path, chinese_text="") -> str:
        if not os.path.exists(audio_path):
            return ""
        audio = WAVE(audio_path)
        duration = audio.info.length
        start_time = time.time()
        vad_param = {
            "threshold": 0.2,
            "min_speech_duration_ms": 200,
            "max_speech_duration_s": 30,
            "min_silence_duration_ms": 1000,
            "speech_pad_ms": 400,
        }
        chinese_prompt = f"这是一段车机关于指令响应的音频，指令为{chinese_text}，请把这段音频转为简体中文。"
        segments, info = self.model.transcribe(audio_path, beam_size=5, language="zh",
                                               initial_prompt=chinese_prompt, vad_filter=True,
                                               vad_parameters=vad_param, hotwords=HOT_WORDS)
        text = ""
        for segment in segments:
            if segment.end > duration + 1:
                continue
            text += segment.text
        print(f"Recognition: {time.time() - start_time} s, text: {text}")
        # 如果text转出来是空，并且音频时长大于 xx s，再转化一次
        if (not text and duration > 8) or check_blacklist(text):
            text = self.identify_rouse(audio_path)
        text = delete_blacklist(text)
        return text

    def identify_rouse(self, audio_path) -> str:
        if not os.path.exists(audio_path):
            return ""
        vad_param = {
            "threshold": 0.35,  # 敏感度提升，捕捉轻声/气音
            "min_speech_duration_ms": 400,  # 中文停顿较短，适当缩短静音判定
            "max_speech_duration_s": 250,  # 避免短促呼吸声误判
            "min_silence_duration_ms": 512,  # 精细检测窗口（需与采样率匹配）
            "speech_pad_ms": 300  # 语音段前后填充防截断
        }
        start_time = time.time()
        audio = WAVE(audio_path)
        duration = audio.info.length
        segments, info = self.model.transcribe(audio_path, beam_size=5, language="zh", vad_filter=True,
                                               vad_parameters=vad_param, condition_on_previous_text=False)
        text = ""
        for segment in segments:
            if segment.end > duration + 1:
                continue
            text += segment.text
        print(f"Recognition: {time.time() - start_time} s, text: {text}")
        return text


asr = FasterWhisperASR(gpu=True)
asr.identify_rouse(r"D:\testdata\ASR识别问题语料\ASR识别问题语料\project_12\1\plan_23\testcorpus_2435\testcorpus_2435_mic_0.wav")
