import glob
import os
import re
import xiangsi as xs

from paddleocr import PaddleOCR
import jieba
from argparse import ArgumentParser
import cv2
import matplotlib.pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


FULL_MATCH_MAP = {}

def remove_punctuation_by_re(text):
    # 定义一个正则表达式，匹配所有标点符号
    pattern = r'[^\w]'
    # 使用re.sub()替换掉标点符号
    return re.sub(pattern, '', text)

def arabic_to_chinese(s):
    # 定义数字和中文数字的映射
    arabic_numerals = "0123456789"
    chinese_numerals = "零一二三四五六七八九"

    # 创建一个字典来映射阿拉伯数字到中文数字
    num_dict = {arabic_numerals[i]: chinese_numerals[i] for i in range(10)}

    # 结果字符串
    result = []

    # 遍历输入字符串中的每个字符
    for char in s:
        if char in num_dict:
            result.append(num_dict[char])
        else:
            result.append(char)

    # 将结果列表合并为字符串并返回
    return ''.join(result)


def calculate_similarity(text1, text2):
    tmp1 = text1.lower()
    tmp2 = text2.lower()
    if not tmp1 or not tmp2:
        return 0.0

    def chinese_tokenizer(text):
        return jieba.lcut(text, cut_all=True)

    # 创建 TfidfVectorizer 对象
    vectorizer = TfidfVectorizer(tokenizer=chinese_tokenizer)
    # 将两行文字转换为 TF-IDF 矩阵
    tfidf_matrix = vectorizer.fit_transform([tmp1, tmp2])
    # 计算余弦相似度
    similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
    return round(similarity[0][0], 2)


def get_ocr_wdt_hgt(item):
    # [[1356, 174], [1592, 174], [1592, 222], [1356, 222]], ...
    # there we need 1592 - 1356 and 222 - 174
    return item[0][2][0] - item[0][0][0], item[0][2][1] - item[0][0][1]


def remove_duplicates_and_merge(ocr_output):
    seen = set()  # 用于存储已经出现过的字符
    result = []

    for text in ocr_output:
        for char in text:
            if char not in seen:
                result.append(char)
                seen.add(char)

    return ''.join(result)


class Recognition:
    def __init__(self, source, match) -> None:
        # self.reader = easyocr.Reader(['ch_sim'])
        self.ocr = PaddleOCR(use_angle_cls=True, lang="ch", debug=False)
        self.source = source
        self.minimum_width = 200
        self.minimum_height = 40
        self.min_quality = 0.8
        self.confidence = 0.5
        self.target_area = None
        self.match = match
        self.result = ''
        self.candidate = ''
        self.char_confidence_map = {}
        self.chinese_char_map = {}
        self.char_confidence_list = []
        self.str_confidence = 0
        # 禁止选择的区域 ['text', x, y]
        self.blacklist = None
        self.false_drop_list = []

    def use_confidence_map(self):
        return len(self.char_confidence_map) != 0

    def select_char(self, diff1, diff2, str1_start, str2_start, map_key):
        diff_str = ""
        min_length = min(len(diff1), len(diff2))
        for idx in range(min_length):
            if diff1[idx] == diff2[idx]:
                diff_str += diff1[idx]
            else:
                if len(self.char_confidence_map) != 0:
                    if len(self.char_confidence_list) <= str1_start + idx:
                        print(f"select_char index out of range. {diff1 = } {str1_start = } {idx = } "
                              f"length:{self.char_confidence_list}")
                    char1_confidence = self.char_confidence_list[str1_start + idx]
                    char2_confidence = self.char_confidence_map[map_key][str2_start + idx]
                    print(f"char:{diff1[idx]}:{char1_confidence} char2:{diff2[idx]}:{char2_confidence}")
                    if char2_confidence > 0.9 or char2_confidence > char1_confidence:
                        diff_str += diff2[idx]
                        self.char_confidence_list[str1_start + idx] = self.char_confidence_map[map_key][
                            str2_start + idx]
                    else:
                        diff_str += diff1[idx]
                else:
                    diff_str += diff2[idx]
        return diff_str

    def get_diff_str(self, diff1, diff2, str1_start, str2_start, map_key):
        idx = 0
        diff2_offset = 0
        for i in range(len(diff2)-3):
            i = len(diff2) - i - 1
            if diff2[:i] in diff1:
                idx = diff1.find(diff2[:i])
                diff2_offset = len(diff2) - i - 1
                break

        diff_str = self.select_char(diff1[idx:], diff2, str1_start + idx, str2_start, map_key)
        if idx > 0:
            diff_str = diff1 + diff2[diff2_offset:]
            print("fsdfsfeeee", idx, diff_str)
            for j in range(diff2_offset, len(diff2)):
                self.char_confidence_list.append(self.char_confidence_map[map_key][str2_start + j])
        if len(diff2) > len(diff1) + idx:
            for idx in range(len(diff1) + idx, len(diff2)):
                diff_str += diff2[idx]
                if len(self.char_confidence_map) != 0:
                    self.char_confidence_list.append(self.char_confidence_map[map_key][str2_start + idx])

        return diff_str, diff2_offset

    def reconstruct_affix(self, str1, str2, str1_start, str2_start, map_key, is_pre=True):
        diff_str = ""
        # if len(str1) < 2 or len(str2) < 2:
        #     return
        if len(str1) == len(str2):
            diff_str = self.select_char(str1, str2, str1_start, str2_start, map_key)
        elif len(str1) > len(str2):
            flag = False
            idx = 0
            for i in range(len(str2) - 2):
                if str2[:len(str2) - i] in str1:
                    idx = str1.find(str2[:len(str2) - i])
                    if idx > 0:
                        flag = True
                    break
            if not flag:
                idx = len(str1) - len(str2)
                # idx = len(str1) - 1
            print(f"{idx=} ", str1[idx:], str2)
            diff_str = self.select_char(str1[idx:], str2, str1_start + idx, str2_start, map_key)
            diff_str = str1[:idx] + diff_str
        elif len(str1) < len(str2):
            flag = False
            idx = 0
            for i in range(len(str1) - 2):
                if str1[:len(str1) - i] in str2:
                    idx = str2.find(str1[:len(str1) - i])
                    if idx > 0:
                        flag = True
                    break
            if not flag:
                idx = 0
            diff_str = self.select_char(str1, str2[idx:], str1_start, str2_start + idx, map_key)
            diff_str = str2[:idx] + diff_str + str2[idx + len(str1):]
            self.char_confidence_list = self.char_confidence_list[:str1_start + len(str1)]
            for i in range(str2_start + idx + len(str1), len(self.char_confidence_map[map_key])):
                self.char_confidence_list.append(self.char_confidence_map[map_key][i])
        return diff_str

    def is_sim(self, initial_text, chinese_text):
        initial_length, chinese_length = len(initial_text), len(chinese_text)
        if chinese_length >= initial_length + 3:
            return False, 0.0
        sim = xs.cossim(arabic_to_chinese(initial_text), arabic_to_chinese(chinese_text))
        if chinese_length >= 4 and initial_text.startswith(chinese_text):
            return True, sim
        # 最低相似度因随着文本长度而变化
        if initial_length <= 10:
            real_sim =  0.4
        elif 10 < initial_length < 20:
            # 线性插值公式：y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
            real_sim = 0.4 + (initial_length - 10) * (0.15 - 0.4) / (20 - 10)
        else:
            real_sim = 0.15

        # 长文本识别
        if initial_length >= 10:
            initial_length = 10
        if chinese_length >= 10:
            chinese_length = 10
        length_weight = abs(chinese_length - initial_length) / initial_length
        # 引入长度作为权重
        sim = sim / (1 + length_weight)
        return sim >= real_sim, sim

    def delete_result_char_confidence(self, result, pattern, line, idx, i):
        if len(result[0][0][1]) == 3:
            delete_list = []
            for j, char in enumerate(line):
                if not pattern.match(char):
                    delete_list.append(j)
            for index in sorted(delete_list, reverse=True):
                del result[idx][i][1][2][index]

    def filter_print_ocr(self, result, initial_text, all_search=False):
        initial_text = initial_text.lower()
        max_sim = 0
        max_sim_area = None
        max_sim_text = ""

        def is_area(x, y, area):
            if x - 10 < area[0] < x + 10 and y - 10 < area[1]< y + 10:
                return True
            return False

        for idx in range(len(result)):
            res = result[idx]
            if not res:
                continue
            for i, line in enumerate(res):
                pattern = re.compile(r'[\u4e00-\u9fa5a-zA-Z0-9]')
                chinese_text = "".join(re.findall(pattern, line[1][0]))
                chinese_text = chinese_text.lower()
                str_confidence = line[1][1]
                if (self.blacklist and self.blacklist[0] == chinese_text and
                        is_area(int(line[0][0][0]), int(line[0][0][1]), self.blacklist[1:])):
                    continue
                if str_confidence < 0.7 and len(chinese_text) < 2:
                    continue
                if chinese_text == initial_text:
                    self.target_area = int(line[0][0][0]), int(line[0][0][1])
                    print(f'EXACT {chinese_text = } {self.target_area = }')
                    self.delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                    return chinese_text, idx, i

                sim_flag, sim = self.is_sim(initial_text, chinese_text)
                if self.target_area is None:
                    if sim_flag:
                        if all_search:
                            if sim > max_sim:
                                max_sim = max(max_sim, sim)
                                max_sim_area = int(line[0][0][0]), int(line[0][0][1])
                                max_sim_text = chinese_text
                                print(f"{max_sim_text=}")
                        else:
                            self.target_area = int(line[0][0][0]), int(line[0][0][1])
                            self.delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                            print(f'SET {chinese_text = } {self.target_area = }')
                            return chinese_text, idx, i
                    continue
                print(f"{sim=}, {chinese_text=}")
                if sim_flag:
                    self.delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                    print(f'GET {chinese_text = } {line[1][0]}')
                    return chinese_text, idx, i
            if all_search:
                print(f"all_search {max_sim_area=} {max_sim_text=} {max_sim}")
                self.target_area = max_sim_area
        return '', 0, 0

    def compare_seq2(self, str1, str2, idx):
        m = len(str1)
        n = len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        length = 0
        end_pos1 = 0
        end_pos2 = 0

        if not str1 or not str2 or str1 == str2:
            return str1

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i - 1] == str2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                    if dp[i][j] > length:
                        length = dp[i][j]
                        end_pos1 = i - 1
                        end_pos2 = j - 1
                else:
                    dp[i][j] = 0

        if length < 2 and end_pos1 != m - 1:
            self.char_confidence_list += self.char_confidence_map[idx]
            return str1 + str2

        # 特殊处理，都是数字的情况下车技可能会直接进行跳转 例子：['帮我呼叫座机023', '帮我呼叫座机023', '帮我呼叫座机023', '6623123']
        if str2.isdigit() and end_pos2 - length + 1 != 0:
            self.char_confidence_list += self.char_confidence_map[idx]
            return str1 + str2

        if end_pos2 - length + 1 == 0 and end_pos1 + 1 == m:
            if len(self.char_confidence_map) != 0:
                print(f"test {self.char_confidence_map[idx][length:]}")
                self.char_confidence_list += self.char_confidence_map[idx][length:]
            return str1 + str2[length:]
        elif end_pos2 - length + 1 > 0 and end_pos1 + 1 == m:
            diff_str2 = str2[0:end_pos2 - length + 1]
            diff_str1_start = m - length - len(diff_str2)
            diff_str1 = str1[diff_str1_start:diff_str1_start + len(diff_str2)]
            print("DIFF1 ", diff_str1, diff_str2)
            diff_str, d2_idx = self.get_diff_str(diff_str1, diff_str2, diff_str1_start, 0, idx)
            self.char_confidence_list += self.char_confidence_map[idx][end_pos2 + 1:]
            return str1[:diff_str1_start] + diff_str + str2[end_pos2 - length + 1:]
        elif end_pos2 - length + 1 == 0 and end_pos1 + 1 < m:
            diff_str1 = str1[end_pos1 + 1:]
            diff_str2 = str2[length:length + len(diff_str1)]
            print("DIFF2 ", diff_str1, diff_str2)
            diff_str, d2_idx = self.get_diff_str(diff_str1, diff_str2, end_pos1 + 1, length, idx)
            if len(self.char_confidence_map) != 0:
                self.char_confidence_list += self.char_confidence_map[idx][length + len(diff_str):]
            return str1[:end_pos1 + 1] + diff_str + str2[length + len(diff_str):]

        print("test, ", str1, str2)
        # 公共字串字串在两个字符串的中间
        common_str = str1[end_pos1 - length + 1:end_pos1 + 1]
        str1_prefix, str1_suffix = str1[0: end_pos1 - length + 1], str1[end_pos1 + 1:]
        str2_prefix, str2_suffix = str2[0: end_pos2 - length + 1], str2[end_pos2 + 1:]
        print("test2, ", str1_prefix, str1_suffix)
        print("test3, ", str2_prefix, str2_suffix)
        diff_prefix = self.reconstruct_affix(str1_prefix, str2_prefix, 0, 0, idx)
        if not diff_prefix:
            diff_prefix = ""
        diff_suffix = self.reconstruct_affix(str1_suffix, str2_suffix, end_pos1 + 1, end_pos1 + 1, idx)
        if not diff_suffix:
            diff_suffix = ""
        # self.char_confidence_list += self.char_confidence_map[idx][end_pos2 + 1:]
        return diff_prefix + common_str + diff_suffix

    def process_ocr_output(self, ocr_output):
        set_output = set(ocr_output)
        if len(set_output) <= 2:
            return
        print(f"{self.chinese_char_map =}")
        for i in range(1, len(ocr_output)):
            i = len(ocr_output) - i
            if not ocr_output[i]:
                continue
            count = self.chinese_char_map.get(ocr_output[i][0], 0)
            if count < 2:
                print("del char: ", ocr_output[i][0])
                ocr_output[i] = ocr_output[i][1:]
                del self.char_confidence_map[i][0]
                continue
            if len(ocr_output[i]) >= 3:
                sub_str = ocr_output[i][:3]
                is_delete = True
                for j in range(i):
                    j = i - j
                    idx = ocr_output[j].find(sub_str[1:])
                    if idx > 0:
                        tmp_char = ocr_output[j][idx - 1]
                        if ocr_output[i][0] == tmp_char:
                            is_delete = False
                            break
                if is_delete:
                    print("del char: ", ocr_output[i][0])
                    ocr_output[i] = ocr_output[i][1:]
                    del self.char_confidence_map[i][0]

    def run(self):
        ocr_output = []
        all_search = False
        false_drop = int(len(self.source)/2) + 1
        print(f"{self.source}. {false_drop=}")
        for i, image_file in enumerate(self.source):
            if self.target_area is not None:
                img = cv2.imread(image_file)
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                up_offset = max(0, self.target_area[1] - 40)
                left_offset = max(0, self.target_area[0] - 100)
                height, width, _ = img.shape
                cropped_image = gray[up_offset:self.target_area[1] + 100,
                                left_offset:self.target_area[0] + 600]
                result = self.ocr.ocr(cropped_image)
                plt.imsave(f'test-{i}.png', cropped_image)
            else:
                result = self.ocr.ocr(image_file)
            if not result[0]:
                continue
            ocr_item, idx, idy = self.filter_print_ocr(result, self.match, all_search)
            if all_search:
                all_search = False
            # 判断区域是否选错
            if i < false_drop and len(ocr_item) != 0:
                self.false_drop_list.append(ocr_item)
                print(f"{self.false_drop_list =}")
                if len(self.false_drop_list) == false_drop and len(set(self.false_drop_list)) == 1:
                    print("ocr_item == 3")
                    if self.target_area:
                        self.blacklist = [ocr_item, int(self.target_area[0]), int(self.target_area[1])]
                    ocr_output = []
                    self.false_drop_list = []
                    self.target_area = None
                    # 重选区域全搜索
                    all_search = True
            if ocr_item == self.match.lower():
                img = cv2.imread(image_file)
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                height, width, _ = img.shape
                print(f"{height = } {width =}")
                up_coordinate = max(0, self.target_area[1] - 40)
                down_coordinate = min(height, self.target_area[1] + 100)
                left_coordinate = max(0, self.target_area[0] - 100)
                right_coordinate = min(width, self.target_area[0] + 600)
                cropped_image = gray[up_coordinate:down_coordinate, left_coordinate:right_coordinate]
                plt.imsave(f'test-{i}.png', cropped_image)
                self.result = ocr_item
                self.str_confidence = result[idx][idy][1][1]
                return
            if self.target_area is not None and len(ocr_item) != 0:
                # print(f"self.chinese_char_map", self.chinese_char_map)
                if ocr_item:
                    ocr_output.append(ocr_item)
                    if len(result[0][0][1]) == 3:
                        self.char_confidence_map[len(ocr_output) - 1] = result[idx][idy][1][2]
                    for char in ocr_item:
                        self.chinese_char_map[char] = self.chinese_char_map.get(char, 0) + 1

        sequence = ''
        self.process_ocr_output(ocr_output)
        print(f'{ocr_output = }')
        for i, current in enumerate(ocr_output):
            print(f"{sequence = }, {current =}, {len(self.char_confidence_list)}")
            if i == 0:
                sequence = current
                if len(self.char_confidence_map) != 0:
                    self.char_confidence_list = self.char_confidence_map[0]
            else:
                sequence = self.compare_seq2(sequence, current, i)
                if sequence == self.match.lower():
                    break
        self.result = sequence
        if len(self.char_confidence_list) != 0:
            self.str_confidence = sum(self.char_confidence_list) / len(self.char_confidence_list)


def main(args):
    def extract_number(file_name):
        match = re.search(r'-(\d+)\.jpg$', file_name)
        if match:
            return int(match.group(1))
        return 0
    project_id = "project_test"
    source = sorted(glob.glob(args.glob_path), key=extract_number)
    match = remove_punctuation_by_re(args.match)
    print(f"{match=}")
    ocr = Recognition(source, match)
    ocr.run()
    print(f"result  {ocr.result}")
    print(f"str_confidence  {ocr.str_confidence}")


# python production_paddleocr.py --glob_path ""D:\project\CVAtest\backend\photo\TRm1Fh\33\UFMyWZ\FqVX31\0_FqVX31-*.jpg"" --match "请告诉我2024年gpd排名前三的国家是哪三个"
if __name__ == '__main__':
    parser = ArgumentParser()
    parser.add_argument('--glob_path', required=True)
    parser.add_argument('--match', default='我想个人静静')
    args = parser.parse_args()
    main(args)
