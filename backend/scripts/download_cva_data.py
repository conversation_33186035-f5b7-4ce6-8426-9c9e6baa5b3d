import os.path
import time
import pandas as pd
import requests
import zipfile
from pathlib import Path
from minio import Minio
from urllib.parse import urlparse, parse_qs

URL = "http://*************:61293/cva/get_datas"
"""
获取数据的筛选参数：
MAC_ADDRESS（必填）: 电脑的Mac地址，可以通过script目录下的get_mac_address.py脚本获取
PROJECT_ID： 项目id
PLAN_ID： 方案ID
TURN_ID： 第几轮
RESULT_ID： 结果ID

最后会在执行目录生产excel文件
"""
# MAC_ADDRESS 是查询的主id 组合方式为 mac地址+路径的hash字符串+时间戳,
# 格式例子：70089426771E_d0f377a70aef03a6bbc74008eef2e9b9_1741835012
# 每次服务执行会把当前的id 记录在 cva_data_id.txt 里
MAC_ADDRESS = ""
PROJECT_ID = ""
PLAN_ID = ""
TURN_ID = 0
RESULT_ID = ""
# 数据会从Minio下载到这个目录
DOWNLOAD_DIR = r"D:\testdata\test"

MINIO_URL = "http://*************:39000"
MINIO_CLIENT = Minio(
    endpoint="*************:39000",
    access_key="qHGNbLRWtJcfJsDkl3LK",
    secret_key="qmkA57ujunxw2cEpmPCVfsHKtXdbRAfv0SYvtdQC",
    secure=False,
)


def extract_zip(zip_path):
    extract_dir = os.path.dirname(zip_path)

    # 使用上下文管理器自动清理资源
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # 解压全部内容（保留目录结构）
        zip_ref.extractall(extract_dir)
        print(f"解压完成: {zip_path} -> {extract_dir}")


def underscore_str_to_path(s, is_absolute=True):
    parts = s.split("-")
    if is_absolute:
        path = Path(os.path.sep).joinpath(*parts)
    else:
        path = Path().joinpath(*parts)
    return os.path.normpath(str(path)).lstrip("\\")


def download_file(bucket_name, object_name, file_path):
    try:
        print(f"{bucket_name=}, {object_name=}, {file_path=}")
        MINIO_CLIENT.fget_object(
            bucket_name=bucket_name,  # 存储桶名称
            object_name=object_name,  # 对象名称（远程文件名）
            file_path=file_path  # 本地保存路径
        )
        print("文件下载成功")
    except Exception as e:
        print(f"下载失败：{e}")
    return


def get_file_path(url):
    parsed_url = urlparse(url)
    url_array = [part for part in parsed_url.path.split("/") if part]
    rel_url = url_array[-2]
    object_name = "/".join(url_array[1:])
    path = os.path.join(DOWNLOAD_DIR, fr"{underscore_str_to_path(rel_url)}")
    file_path = os.path.join(path, url_array[-1])
    if not os.path.exists(path):
        os.makedirs(path)
    return file_path, object_name


def download_task(json_data):
    for data in json_data:
        project_id = data.get("project_id")
        plan_id = data.get("plan_id")
        turn_id = data.get("turn_id")
        result_id = data.get("result_id")
        if data.get("pic_url"):
            photo_file_path, object_name = get_file_path(data.get("pic_url"))
            download_file("picture", object_name, photo_file_path)
            extract_zip(photo_file_path)
            os.remove(photo_file_path)
            data["photo_file_path"] = photo_file_path
        if data.get("mic_url"):
            audio_file_path, object_name = get_file_path(data.get("mic_url"))
            download_file("audio", object_name, audio_file_path)
            extract_zip(audio_file_path)
            os.remove(audio_file_path)
            data["audio_file_path"] = audio_file_path
    return


def generate_excel(json_data):
    res_temp = []
    for data in json_data:
        temp = {"项目ID": data.get("project_id"), "方案ID": data.get("plan_id"), "第几轮测试": data.get("turn_id"),
                "结果ID": data.get("result_id"), "测试场景": data.get("test_scenario"), "语料文本": data.get("text"),
                "车机识别结果": data.get("ocr_result"), "车机识别准确率": data.get("ocr_accuracy_rate"),
                "预期结果": data.get("expect_result"), "模型图片判断置信度": data.get("llm_ui_confidence"),
                "模型语音判断置信度": data.get("llm_voice_confidence"), "判断结果": data.get("result"),
                "评估理由": data.get("reason"), "判断分数": data.get("score"),
                "OCR图片判断置信度": data.get("image_confidence"), "车机响应时间(ms)": data.get("response_time"),
                "录音路径": "", "语料播出前图片": "", "语料播出后图片": ""}
        res_temp.append(temp)
        if data.get("photo_file_path"):
            dir_name = os.path.dirname(data.get("photo_file_path"))
            jpg_files = [f for f in os.listdir(dir_name) if f.lower().endswith('.jpg')]

            # 1. 找到所有带"result"的图片
            result_files = [f for f in jpg_files if '_result' in f]

            # 2. 提取result文件的前缀（以"_result"分割）
            prefixes = {rf.split('_result')[0] for rf in result_files}

            # 3. 匹配与result前缀相同的其他图片
            matched_files = []
            for prefix in prefixes:
                for f in jpg_files:
                    if f.startswith(prefix):
                        matched_files.append(f)

            # 去重并输出结果
            matched_files = list(set(matched_files))
            if result_files:
                temp["语料播出前图片"] = os.path.join(dir_name, result_files[0])
            if len(matched_files) != 0:
                temp["语料播出后图片"] = os.path.join(dir_name, matched_files[0])

        if data.get("audio_file_path"):
            dir_name = os.path.dirname(data.get("audio_file_path"))
            matched_files = [
                filename for filename in os.listdir(dir_name)
                if all([
                    "mic" in filename,  # 包含 "mic"
                    "_full" not in filename,  # 不包含 "full"
                    filename.endswith(".wav")  # 后缀为 .wav
                ])
            ]
            if len(matched_files) != 0:
                temp["录音路径"] = os.path.join(dir_name, matched_files[0])
    excel_name = f"{MAC_ADDRESS}_{int(time.time())}.xlsx"
    df = pd.DataFrame(res_temp)
    # 写入Excel文件
    with pd.ExcelWriter(excel_name, engine="xlsxwriter") as writer:
        df.to_excel(writer, index=False, sheet_name="Sheet1")
    print(f"生成excel文件 path={excel_name}")
    return


def main():
    if not MAC_ADDRESS:
        print("error: not mac_address")
        exit(1)
    data = {
        "mac_address": MAC_ADDRESS,
    }
    if PROJECT_ID:
        data["project_id"] = PROJECT_ID
    if PLAN_ID:
        data["plan_id"] = PLAN_ID
    if TURN_ID:
        data["turn_id"] = TURN_ID
    if RESULT_ID:
        data["result_id"] = RESULT_ID
    response = requests.post(URL, json=data)
    json_data = response.json()
    download_task(json_data)
    generate_excel(json_data)
    return


if __name__ == "__main__":
    main()
