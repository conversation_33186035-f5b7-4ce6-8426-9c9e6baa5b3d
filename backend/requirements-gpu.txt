﻿absl-py==2.1.0
aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
albucore==0.0.13
albumentations==1.4.10
alembic==1.14.0
aliyun-python-sdk-core==2.15.1
aliyun-python-sdk-kms==2.16.5
altgraph==0.17.4
annotated-types==0.7.0
anyio==4.6.2
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
astor==0.8.1
async-timeout==4.0.3
attrs==24.2.0
av==12.3.0
backoff==2.2.1
beautifulsoup4==4.12.3
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
contourpy==1.3.0
crcmod==1.7
cryptography==43.0.3
ctranslate2==4.5.0
cycler==0.12.1
Cython==3.0.11
dashscope==1.20.12
datasets==3.1.0
decorator==5.1.1
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dspy==2.5.29
edge-tts==6.1.15
et_xmlfile==2.0.0
exceptiongroup==1.2.2
fastapi==0.115.2
faster-whisper==1.0.3
filelock==3.16.1
fire==0.7.0
flatbuffers==24.3.25
fonttools==4.54.1
frozenlist==1.4.1
fsspec==2024.9.0
gevent==24.10.3
greenlet==3.1.1
grpcio==1.67.1
h11==0.14.0
httpcore==1.0.6
httpx==0.27.2
huggingface-hub==0.26.2
humanfriendly==10.0
idna==3.10
imageio==2.36.0
imgaug==0.4.0
importlib_metadata==8.5.0
importlib_resources==6.4.5
jieba==0.42.1
Jinja2==3.1.4
jiter==0.6.1
jmespath==0.10.0
joblib==1.4.2
json_repair==0.30.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.7
lazy_loader==0.4
Levenshtein==0.26.1
litellm==1.51.0
lmdb==1.5.1
lxml==5.3.0
magicattr==0.1.6
Mako==1.3.6
Markdown==3.7
MarkupSafe==3.0.2
matplotlib==3.9.2
minio==7.2.12
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.16
mutagen==1.47.0
networkx==3.2.1
Nuitka==2.5.3
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-nvrtc-cu12==12.5.40
nvidia-cuda-runtime-cu12==12.3.101
nvidia-cudnn-cu12==9.0.0.312
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-nvjitlink-cu12==12.5.40
onnxruntime==1.19.2
openai==1.52.2
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opt-einsum==3.3.0
optuna==4.1.0
ordered-set==4.1.0
oss2==2.19.1
packaging==24.1
paddleocr==2.9.1
paddlepaddle==2.6.2
pandas==2.2.3
pefile==2023.2.7
pillow==11.0.0
propcache==0.2.0
protobuf==3.20.2
pyarrow==18.0.0
PyAudio==0.2.14
pyclipper==1.3.0.post6
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.9.2
pydantic-settings==2.5.2
pydantic_core==2.23.4
pydub==0.25.1
pygame==2.6.1
pyinstaller==6.11.0
pyinstaller-hooks-contrib==2024.9
pyparsing==3.2.0
pypinyin==0.53.0
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-Levenshtein==0.26.1
python-multipart==0.0.12
pytz==2024.2
pywin32-ctypes==0.2.3
PyYAML==6.0.2
RapidFuzz==3.10.0
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
rpds-py==0.21.0
scikit-image==0.24.0
scikit-learn==1.5.2
scipy==1.13.1
shapely==2.0.6
silero-vad==5.1.2
six==1.16.0
sniffio==1.3.1
soundfile==0.12.1
soupsieve==2.6
SQLAlchemy==2.0.35
starlette==0.39.2
sympy==1.13.1
tenacity==9.0.0
tensorboard==2.18.0
tensorboard-data-server==0.7.2
termcolor==2.5.0
threadpoolctl==3.5.0
tifffile==2024.8.30
tiktoken==0.8.0
tokenizers==0.20.1
tomli==2.0.2
torch==2.5.1
torchaudio==2.5.1
tqdm==4.66.5
typing_extensions==4.12.2
tzdata==2024.2
ujson==5.10.0
urllib3==2.2.3
uvicorn==0.31.1
websocket-client==1.8.0
Werkzeug==3.1.3
xiangsi==4.2.3
XlsxWriter==3.2.0
xxhash==3.5.0
yarl==1.15.4
zipp==3.20.2
zope.event==5.0
zope.interface==7.1.1
zstandard==0.23.0
