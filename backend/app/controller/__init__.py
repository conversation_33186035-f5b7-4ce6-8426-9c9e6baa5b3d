from app.controller import corpus
from app.controller import project
from app.controller import synthesize_corpus
from app.controller import play_config
from app.controller import device
from app.controller import multi_corpus
from app.controller import label
from app.controller import can
from app.controller import log


# 定义路由列表
RegisterRouterList = [corpus, project, synthesize_corpus, play_config, device, multi_corpus, label, can, log]
