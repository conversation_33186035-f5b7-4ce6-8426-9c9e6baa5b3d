import json
from fastapi import APIRouter
from app.config.device_config import *
from app.proto.device_proto import DeviceConfigResponse, SetCorpusDeviceRequest, SetVideoDeviceRequest
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode
from app.middleware.log import logger as log

router = APIRouter(prefix="/device")


@router.post("/config_set")
async def config_set(req: DeviceConfigResponse):
    if req.camera_times is not None:
        deviceConfig.device_map["camera_times"] = req.camera_times
    if req.camera_interval is not None:
        deviceConfig.device_map["camera_interval"] = req.camera_interval
    if req.camera_start_wait is not None:
        deviceConfig.device_map["camera_start_wait"] = req.camera_start_wait
    if req.result_judge_thread is not None:
        deviceConfig.device_map["result_judge_thread"] = req.result_judge_thread
    if req.result_photo_interval is not None:
        deviceConfig.device_map["result_photo_interval"] = req.result_photo_interval
    if req.result_start_wait is not None:
        deviceConfig.device_map["result_start_wait"] = req.result_start_wait
    if req.result_photo_diff_rate is not None:
        deviceConfig.device_map["result_photo_diff_rate"] = req.result_photo_diff_rate
    if req.result_timeout is not None:
        deviceConfig.device_map["result_timeout"] = req.result_timeout
    if req.video_width is not None:
        deviceConfig.device_map["video_width"] = req.video_width
    if req.video_height is not None:
        deviceConfig.device_map["video_height"] = req.video_height
    if req.video_frame_rate is not None:
        deviceConfig.device_map["video_frame_rate"] = req.video_frame_rate
    if req.record_end_wait_time is not None:
        deviceConfig.device_map["record_end_wait_time"] = req.record_end_wait_time
    deviceConfig.set_all()
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/config_get")
async def config_get():
    res = DeviceConfigResponse(
        camera_times=deviceConfig.device_map["camera_times"],
        camera_interval=deviceConfig.device_map["camera_interval"],
        camera_start_wait=deviceConfig.device_map["camera_start_wait"],
        result_judge_thread=deviceConfig.device_map["result_judge_thread"],
        result_photo_interval=deviceConfig.device_map["result_photo_interval"],
        result_start_wait=deviceConfig.device_map["result_start_wait"],
        result_photo_diff_rate=deviceConfig.device_map["result_photo_diff_rate"],
        result_timeout=deviceConfig.device_map["result_timeout"],
        video_width=deviceConfig.device_map["video_width"],
        video_height=deviceConfig.device_map["video_height"],
        video_frame_rate=deviceConfig.device_map["video_frame_rate"],
        record_end_wait_time=deviceConfig.device_map["record_end_wait_time"],
    )
    return HttpResponse(status=StatusCode.OK.code, data=res)


@router.post("/get_device_list")
async def get_device_list():
    device_map = get_audio_device_map()
    device_list = [device_map[key] for key in sorted(device_map)]
    data = HttpDataListResponse(list=device_list, page_size=None, page=None, total=len(device_list))
    return HttpResponse(status=StatusCode.OK.code, data=data)


@router.post("/get_corpus_device")
async def get_corpus_device():
    test_corpus = json.loads(deviceConfig.device_map["play_test_corpus_device"])
    disturb_corpus = json.loads(deviceConfig.device_map["play_disturb_corpus_device"])
    data = {"test_corpus": test_corpus, "disturb_corpus": disturb_corpus}
    return HttpResponse(status=StatusCode.OK.code, data=data)


@router.post("/set_corpus_device")
async def set_corpus_device(req: SetCorpusDeviceRequest):
    info = deviceConfig.device_map[f"play_{req.corpus}_device"]
    json_info = json.loads(info)
    info_volume = json_info["volume"]
    device_id = json_info["id"]
    default = json_info["is_default"]
    if req.volume:
        info_volume = req.volume
    if req.id:
        device_id = req.id
    if isinstance(req.is_default, bool):
        default = req.is_default

    if req.corpus == "test_corpus":
        deviceConfig.set_test_corpus_device_id(device_id, info_volume, is_default=default)
    elif req.corpus == "disturb_corpus":
        deviceConfig.set_disturb_corpus_device_id(device_id, info_volume, is_default=default)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_video_list")
async def get_video_list():
    video_list = get_video_device_list()
    result = [NO_CAMERA_JSON]
    for device in video_list:
        result.append({"id": device[0], "name": device[1]})
    data = HttpDataListResponse(list=result, page_size=None, page=None, total=len(result))
    return HttpResponse(status=StatusCode.OK.code, data=data)


@router.post("/get_video_device")
async def get_video_device():
    device_str = deviceConfig.device_map[deviceConfig.video_device]
    device = json.loads(device_str)
    return HttpResponse(status=StatusCode.OK.code, data={"id": device["id"], "name": device["name"]})


@router.post("/set_video_device")
async def set_video_device(req: SetVideoDeviceRequest):
    deviceConfig.set_video_device_id(req.id, req.name)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_cabin_video_device")
async def get_video_device():
    device_str = deviceConfig.device_map[deviceConfig.video_cabin_device]
    device = json.loads(device_str)
    return HttpResponse(status=StatusCode.OK.code, data={"id": device["id"], "name": device["name"]})


@router.post("/set_cabin_video_device")
async def set_video_device(req: SetVideoDeviceRequest):
    deviceConfig.set_cabin_video_device_id(req.id, req.name)
    return HttpResponse(status=StatusCode.OK.code)
