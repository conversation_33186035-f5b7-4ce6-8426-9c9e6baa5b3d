import os.path

from fastapi import APIRouter, Request
from fastapi.responses import  FileResponse
from app.service.project import *
from app.service.plan import *
from app.utils.thread_manager import thread_manager
from app.proto.project_proto import *
from app.service.corpus import *
from app.service.play_config import *
from app.dao.result_dao import ResultOperateDao
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/test_project")


# 测试项目管理
@router.post("/create_test_project")
async def create_test_project(request: Request):
    data = await request.json()

    res = test_project_create(data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/update_test_project")
async def update_test_project(request: Request):
    data = await request.json()

    res = test_project_update(data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_test_project")
async def delete_test_project(request: Request):
    data = await request.json()

    ret = test_project_delete(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_test_project_list")  # 此类结构需要注意，我直接用的传过来数据的 key 作为的查询 key，所以一定要和数据库存的字段相匹配
async def get_test_project_list(request: Request):
    data = await request.json()

    data_list, total = test_project_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)


@router.post("/create_plan")
async def create_plan(request: Request):
    data = await request.json()
    create_test_plan(data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/update_plan")
async def update_plan(request: Request):
    data = await request.json()

    update_test_plan(data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_plan")
async def delete_plan(request: Request):
    data = await request.json()

    ret = fake_delete_test_plan(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_plan_list")
async def get_plan_list(request: Request):
    data = await request.json()

    data_list, total = test_plan_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)


@router.post("/get_plan_detail")
async def get_plan_detail(request: Request):
    data = await request.json()

    res = get_corpuslist_byplanid(data["plan_id"])
    return HttpResponse(status=StatusCode.OK.code, data=res)


@router.post("/save_plan_detail")
async def save_plan_detail(request: Request):
    data = await request.json()

    save_corpuslist_byplanid(data)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/create_retest_plan")
async def create_retest_plan(req: CreateRetestPlanRequest):
    res = retest_plan_create(req.project_id, req.plan_id, req.turn_id, req.retest_plan_name, req.corpus_columns)
    if res:
        return HttpResponse(status=StatusCode.OK.code)
    return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg="创建复测计划失败")

@router.post("/add_to_plan")
async def add_to_plan(req: AddRetestCorpusRequest):
    res = add_to_retestplan(req.project_id, req.plan_id, req.corpus_ids)
    if res:
        return HttpResponse(status=StatusCode.OK.code)
    return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg="创建复测计划失败")
    
@router.post("/execute_retest_plan")
async def execute_retest_plan(req: ExecuteRetestPlanRequest):
    data = {"project_id": req.project_id, "plan_id": req.plan_id, "turn_id": req.turn_id}

    ret, msg = test_execute(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=msg)
    if not msg:
        msg = "retest start"
    return HttpResponse(status=StatusCode.OK.code, data=msg)

# 项目执行 根据方案内容 开始执行测试 生成测试进度和结果列表
@router.post("/start_test")
async def start_test(request: Request):
    data = await request.json()
    ret, msg = test_execute(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=msg)
    if not msg:
        msg = "Project started"
    return HttpResponse(status=StatusCode.OK.code, data=msg)


# 查询该项目 当前（最近一次执行）的测试信息 包括测试进度 结果列表 以及日志
@router.post("/get_test_info")
async def get_test_info(req: GetTestInfoRequest):
    res, total = get_recent_testinfo(req.project_id, req.turn_id, req.plan_id, sign=req.sign,
                                     get_type=req.type, page=req.page, page_size=req.page_size, test_result=req.result)
    if res.get("status", None) == "error":
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=res.get("error_msg"))
    res["total"] = total
    res["page"] = req.page
    res["page_size"] = req.page_size
    return HttpResponse(status=StatusCode.OK.code, data=res)


@router.post("/stop_test")
async def stop_test(request: Request):
    data = await request.json()
    # id = data["project_id"]
    # thread_manager.stop_thread(id)
    test_suspend(data)
    return HttpResponse(status=StatusCode.OK.code, data={"message": f"Project {data['project_id']} termination run"})

@router.post("/pause_test")
async def pause_test(request: Request):
    data = await request.json()
    test_pause(data)

    return HttpResponse(status=StatusCode.OK.code, data={"message": f"Project {data['project_id']} pause run"})


@router.post("/result_update")
async def result_update(request: Request):
    data = await request.json()

    update_test_result(data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_turns")
async def get_turns(request: Request):
    data = await request.json()
    turn_list = get_turn_list(data)
    result = HttpDataListResponse(list=turn_list, total=len(turn_list))
    return HttpResponse(status=StatusCode.OK.code, data=result)


@router.post("/get_video_name")
async def get_video_name(request: Request):
    data = await request.json()
    project_id = data["project_id"]
    turn_id = data["turn_id"]
    name = get_review_video_name(project_id, turn_id)
    return HttpResponse(status=StatusCode.OK.code, data={"name": name})


@router.post("/export_results")
async def export_results(req: TestExportResultsRequest):
    ret, file_url = get_excel_report(req.project_id, req.turn_id, req.type, req.columns, req.order)
    if ret:
        return FileResponse(file_url, filename=os.path.basename(file_url))
    elif ret == False and file_url == "":
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg="result_check fail, No reports were found available")
    else:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"error: {file_url}")


@router.post("/generate_report")
async def generate_report(req: TestGenerateReportRequest):
    ret, file_url = get_word(req.project_id, req.report_name, req.report_number, req.testing_unit, req.turn_id)
    if ret:
        return FileResponse(file_url, filename=os.path.basename(file_url))
    elif ret == False and file_url == "":
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg="generate_report fail, No reports were found available")
    else:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"error: {file_url}")


@router.post("/result_check")
async def result_check(req: TestResultCheckRequest):
    ret = test_result_check(req.result_id, req.result)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"result_check fail, {ret}")
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/remove_sign")
async def remove_sign(req: ResultRemoveSignRequest):
    ret = result_remove_sign(req.project_id, req.plan_id, req.turn_id, req.result_id, req.sign)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"remove_sign fail, {ret}")
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_result")
async def update_result(req: TestResultUpdateRequest):
    ret = test_result_update_result(req.result_id, req.new_result, req.update_result_reason)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"update_result fail, {ret}")
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_result_ocr_accuracy_rate")
async def update_result(req: TestResultUpdateOCRRequest):
    ret = test_result_update_ocr_accuracy_rate(req.result_id, req.ocr_accuracy_rate)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"update_result fail, {ret}")
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_multi_result")
async def update_multi_result(req: MultiTestResultUpdateRequest):
    ret = multi_test_result_update(req.update_result_list, req.multi_result_list, req.multi_id, req.new_result,
                             req.update_result_reason)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"update_multi_result fail, {ret}")
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_update_result_reason")
async def get_update_result_reason(req: GetSignalData):
    msg = get_update_result_reason(req.result_id)
    return HttpResponse(status=StatusCode.OK.code, data=msg)

@router.post("/init")
async def project_init(req: ProgetInitRequest):
    rouse, long_response, long_order, exit_corpus = None, None, None, None
    plan, turn_id = None, 1
    try:
        if not req.rouse_corpus or not req.exit_corpus:
            return HttpResponse(status=StatusCode.PROJECT_INIT_NO_ROUSE.code,
                                error_msg=StatusCode.PROJECT_INIT_NO_ROUSE.errmsg)

        # 合成初始化语料
        language = "1"
        label = f"{req.project_id}_init"
        speaker = "male"
        synthesize_ids = []
        rouse = create_rousecorpus(req.rouse_corpus, "wake-up", speaker, language, label)
        ret = synthesize_rousecorpus([rouse.corpus_id], label)
        if ret:
            return HttpResponse(status=StatusCode.PROJECT_INIT_FAIL.code, error_msg=StatusCode.PROJECT_INIT_FAIL.errmsg)
        long_response = create_testcorpus(req.long_response_corpus, "speech-recognition",
                                          "speech-recognition-interaction", speaker, language,
                                          "audio-video", label, "")
        synthesize_ids.append(long_response.corpus_id)
        long_order = create_testcorpus(req.long_order_corpus, "speech-recognition", "speech-recognition-interaction",
                                       speaker, language,
                                       "audio-video", label, "")
        synthesize_ids.append(long_order.corpus_id)
        exit_corpus = create_testcorpus(req.exit_corpus, "speech-recognition", "speech-recognition-interaction",
                                        speaker, language,
                                        "audio-video", label, "")
        synthesize_ids.append(exit_corpus.corpus_id)
        ret = synthesize_testcorpus(synthesize_ids, label, False)
        if ret:
            return HttpResponse(status=StatusCode.PROJECT_INIT_FAIL.code, error_msg=StatusCode.PROJECT_INIT_FAIL.errmsg)

        # 创建配置
        configs = [{"type": "开始",
                    "config": {"circle": "1", "wakeup_time": False, "wakeup_success_rate": False,
                               "false_wakeup_times": False,
                               "interaction_success_rate": False, "word_recognition_rate": True,
                               "response_time": False}},
                   {"type": "嵌入唤醒",
                    "config": {"wakeUpWaitDifferent": 500, "frequencyDifferent": "every",
                               "frequencyIntervalDifferent": 1,
                               "wakeUpWaitRepeated": 500, "frequencyRepeated": "none"}},
                   {"type": "播放语料",
                    "config": {"gain": 0, "channel": "channel1", "repeat": 1, "wait_time": 5, "timout": 10}}]
        ret, play_config_id = play_config_create(label, "init", "interaction")
        if ret:
            return HttpResponse(status=StatusCode.PROJECT_INIT_FAIL.code, error_msg=StatusCode.PROJECT_INIT_FAIL.errmsg)
        ret = play_config_update(play_config_id, label, "init", "interaction", configs)
        if ret:
            return HttpResponse(status=StatusCode.PLAY_CONFIG_IN_USE.code, error_msg=ret)

        # 集成方案
        plan = create_test_plan({"project_id": req.project_id, "plan_name": label})
        plan_data = {
            "project_id": req.project_id,
            "plan_id": plan.plan_id,
            "testCorpusList": [long_response.corpus_id, long_order.corpus_id, exit_corpus.corpus_id],
            "rouseCorpusList": [rouse.corpus_id],
            "play_config_id": play_config_id
        }
        save_corpuslist_byplanid(plan_data)
        # 初始化前删除以前的数据
        image_url = os.path.join(globalAppSettings.photo_project_init_dir, f"{req.project_id}.jpg")
        txt_url = os.path.join(globalAppSettings.photo_project_init_dir, f"{req.project_id}.txt")
        if os.path.exists(image_url):
            os.remove(image_url)
        if os.path.exists(txt_url):
            os.remove(txt_url)
        # 开始播放语料
        data = {"project_id": req.project_id, "init": "true", "plan_id": plan.plan_id}
        # thread_manager.start_thread(req.project_id, test_execute, data)
        # thread_manager.wait_thread(req.project_id)
        test_execute(data)
        time.sleep(1)
        while req.project_id in thread_manager.threads:
            time.sleep(1)
        turn_list = get_turn_list(data)
        if len(turn_list) != 0:
            turn_id = turn_list[-1]
        audio_dir = os.path.join(globalAppSettings.mic_path, f"{turn_id}", plan.plan_id)
        audio_urls = []
        for root, dirs, files in os.walk(audio_dir):
            for file in files:
                # 判断文件后缀是否符合
                if file.endswith("full.wav"):
                    # 拼接完整路径
                    audio_urls.append(os.path.join(root, file))
        if not os.path.exists(image_url):
            image_url = ""
            return HttpResponse(status=StatusCode.PROJECT_INIT_FAIL.code, error_msg=StatusCode.PROJECT_INIT_FAIL.errmsg,
                                data={"image_url": image_url, "audio_urls": audio_urls})
        return HttpResponse(status=StatusCode.OK.code, data={"image_url": image_url, "audio_urls": audio_urls})
    except Exception as e:
        log.error(f"init fail. error:{e}")
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=f"初始化失败 error:{e}")
    finally:
        # 删除所有初始化数据
        if plan:
            delete_test_plan({"plan_id": plan.plan_id}, delete_config=True)
            ResultOperateDao.deleteManyTestResult({"plan_id": plan.plan_id})
            if turn_id:
                photo_dir = os.path.join(globalAppSettings.photo_dir, req.project_id, str(turn_id), plan.plan_id)
                if os.path.exists(photo_dir):
                    shutil.rmtree(photo_dir)
                mic_dir = os.path.join(globalAppSettings.mic_path, req.project_id, str(turn_id), plan.plan_id)
                if os.path.exists(mic_dir):
                    shutil.rmtree(mic_dir)
                video_dir = os.path.join(globalAppSettings.video_dir, f"{req.project_id}_{turn_id}.mp4")
                if os.path.exists(video_dir):
                    os.remove(video_dir)
        if rouse:
            delete_rousecorpus(rouse.corpus_id)
        if long_response:
            delete_testcorpus(long_response.corpus_id)
        if long_order:
            delete_testcorpus(long_order.corpus_id)
        if exit_corpus:
            delete_testcorpus(exit_corpus.corpus_id)


@router.post("/init_get")
async def project_init_get(req: ProgetInitRequest):
    if len(req.project_id) == 0:
        return {"status": StatusCode.PARAM_ERROR.code, "error_msg": StatusCode.PARAM_ERROR.errmsg}
    image_url = os.path.join(globalAppSettings.photo_project_init_dir, f"{req.project_id}.jpg")
    if not os.path.exists(image_url):
        image_url = ""
    return HttpResponse(status=StatusCode.OK.code, data={"image_url": image_url})

@router.post("/get_can_data")
async def get_can_data(req: GetSignalData):
    turn_list = get_signal_data(req.result_id, req.signal_name)
    result = HttpDataListResponse(list=turn_list, total=len(turn_list))
    return HttpResponse(status=StatusCode.OK.code, data=result)