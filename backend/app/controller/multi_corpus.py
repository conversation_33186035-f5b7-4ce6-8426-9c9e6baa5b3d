from fastapi import APIRouter, Request
from app.service.corpus import create_multi_corpus, update_multi_corpus, delete_mulcorpus, mul_corpus_list,\
                batch_delete_mulcorpus, synthesize_multicorpus
from app.proto.multi_corpus_proto import MultiCorpusCreateRequest, MultiCorpusUpdateRequest, CorpusDeleteRequest,\
    CorpusBatchDeleteRequest, CorpusBatchSynthRequest
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/multi-corpus")

# 测试语料
@router.post("/create")
async def create(req: MultiCorpusCreateRequest):
    ret = create_multi_corpus(req.corpus_name, req.test_type, req.test_scenario, req.speaker, req.language, req.car_function, req.label, req.corpusItems)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)
    
@router.post("/update")
async def update(req: MultiCorpusUpdateRequest):
    ret = update_multi_corpus(req.corpus_name, req.corpus_id, req.test_type, req.test_scenario, req.speaker, req.language, req.car_function, req.label, req.corpusItems)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/delete")
async def delete(req: CorpusDeleteRequest):
    ret = delete_mulcorpus(req.corpus_id)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/batchDelete")
async def batch_delete(req: CorpusBatchDeleteRequest):
    ret = batch_delete_mulcorpus(req.corpus_ids)
    if len(ret) == 0:
        return HttpResponse(status=StatusCode.OK.code)
    else:
        # result = HttpDataListResponse(list=ret, total=len(ret))
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, data=ret)

@router.post("/list")
async def multi_corpus_list(request: Request):
    data = await request.json()

    data_list, total = mul_corpus_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)

@router.post("/synthesize_multi_corpus")
async def synthesize_multi_corpus(req: CorpusBatchSynthRequest):
    ret = synthesize_multicorpus(req.corpus_ids, req.label, req.is_tone)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)