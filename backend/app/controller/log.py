from fastapi import APIRouter
from app.proto import HttpResponse, HttpDataListResponse
from app.proto.log_proto import GetLogRequest
from app.constant.code import StatusCode
from app.middleware.log import *
from datetime import datetime
import re
import os

router = APIRouter(prefix="/log")
cached_entries = []
cached_mtime = 0


def parse_log_file(file_path: str = 'logs.log'):
    """解析日志文件并返回结构化数据"""
    entries = []
    current_entry = []
    timestamp_pattern = re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - ')

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.rstrip('\n')
            if timestamp_pattern.match(line):
                if current_entry:
                    try:
                        timestamp_str = current_entry[0].split(' - ')[0]
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                    except:
                        timestamp = datetime.min
                    entries.append({
                        'timestamp': timestamp,
                        'content': '\n'.join(current_entry)
                    })
                    current_entry = []
                current_entry.append(line)
            else:
                current_entry.append(line)

        if current_entry:
            try:
                timestamp_str = current_entry[0].split(' - ')[0]
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
            except:
                timestamp = datetime.min
            entries.append({
                'timestamp': timestamp,
                'content': '\n'.join(current_entry)
            })
    return entries


def get_log_entries():
    """获取日志条目（带缓存机制）"""
    global cached_entries, cached_mtime
    file_path = os.path.join(LOGFILE_DIR, LOGFILE_NAME)

    try:
        current_mtime = os.path.getmtime(file_path)
        if current_mtime != cached_mtime:
            cached_entries = parse_log_file(file_path)
            cached_mtime = current_mtime
    except FileNotFoundError:
        cached_entries = []

    return cached_entries


@router.post("/list")
async def get_log_list(req: GetLogRequest):
    entries = get_log_entries()
    keyword = req.keyword
    page, page_size = req.page, req.page_size
    # 过滤关键字
    if keyword:
        keyword_lower = keyword.lower()
        filtered = [e for e in entries if keyword_lower in e['content'].lower()]
    else:
        filtered = entries

    # 排序
    if req.order == "desc":
        sorted_entries = sorted(filtered, key=lambda x: x['timestamp'], reverse=True)
    else:
        sorted_entries = sorted(filtered, key=lambda x: x['timestamp'])

    # 分页
    total = len(sorted_entries)
    start = (page - 1) * page_size
    end = start + page_size
    paginated = sorted_entries[start:end]
    data = HttpDataListResponse(list=paginated, total=total, page=req.page, page_size=req.page_size)
    return HttpResponse(status=StatusCode.OK.code, data=data)
