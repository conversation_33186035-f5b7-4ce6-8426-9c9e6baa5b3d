
from fastapi import APIRouter,File, UploadFile, Form, Request
from fastapi.responses import JSONResponse
from app.service.can import (save_dbc, dbc_file_delete, get_dbc_info, save_channel_info,
                             get_dbc_list, channel_list, channel_config, channel_config_save,
                             channel_enable, channel_disable)
import json
from app.proto.can_proto import *
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode

router = APIRouter(prefix="/can")


@router.post("/upload_dbc_file/")
async def upload_dbc_file(file: UploadFile = File(...), info: str = Form(...)):
    data = json.loads(info)
    file_data = await file.read()
    status, error_msg = save_dbc(file, file_data, data)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=error_msg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/get_dbc_filelist")
async def get_dbc_filelist(request: Request):
    data = await request.json()

    data_list, total  = get_dbc_list()
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return  HttpResponse(status=StatusCode.OK.code, data=result)

@router.post("/get_file_content")
async def get_file_content(req: DbcInfoRequest):
    status, ret = get_dbc_info(req.file_id)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return {
        "status": 0,
        "data": ret
    }

@router.post("/delete_dbc_file")
async def delete_dbc_file(req: DbcDeleteRequest):
    status, ret = dbc_file_delete(req.file_id)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code)

@router.post("/init_channel")
async def init_channel():
    status, ret = save_channel_info()
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code)

@router.post("/get_channel_list")
async def get_channel_list():
    data = {}

    data_list, total  = channel_list()
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return  HttpResponse(status=StatusCode.OK.code, data=result)
    
@router.post("/get_channel_config")
async def get_channel_config(req: ChannelIdRequest):
    status, ret = channel_config(req.channel_id)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code, data=ret)

@router.post("/save_channel_config")
async def save_channel_config(request: Request):
    data = await request.json()
    status, ret = channel_config_save(data)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code)

@router.post("/enable_channel")
async def enable_channel(req: ChannelIdRequest):
    status, ret = channel_enable(req.channel_id)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code)

@router.post("/disable_channel")
async def disable_channel(req: ChannelIdRequest):
    status, ret = channel_disable(req.channel_id)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return  HttpResponse(status=StatusCode.OK.code)
