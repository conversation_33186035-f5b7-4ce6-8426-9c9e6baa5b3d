import asyncio
import json
from fastapi import APIRouter, File, UploadFile, Form, Request
from app.service.corpus import *
from app.proto.corpus_proto import *
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/corpus")


@router.post("/upload_audio_file")
async def upload_audio_file(file: UploadFile = File(...), info: str = Form(...)):
    data = json.loads(info)
    file_data = await file.read()
    status, aud_id, file_name_pinyin, audio_duration = save_audio_file(file, file_data, data)
    if status:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=audio_duration)
    data = {
        "aud_id": aud_id,
        "pinyin": file_name_pinyin,
        "audio_duration": audio_duration,
    }
    return  HttpResponse(status=StatusCode.OK.code, data=data)


# 上传excel，批量生成 测试语料
@router.post("/batch_import")
async def batch_import(file: UploadFile = File(...), info: str = Form(...)):
    data = json.loads(info)
    file_data = await file.read()
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, excel_create_corpus, file, file_data, data)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/remote_batch_import")
async def remote_batch_import(file: UploadFile = File(...), info: str = Form(...)):
    data = json.loads(info)
    file_data = await file.read()
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, excel_create_corpus, file, file_data, data)
    return HttpResponse(status=StatusCode.OK.code)


# 测试语料
@router.post("/synthesize_test_corpus")
async def synthesize_test_corpus(req: CorpusSynthesize):
    ret = synthesize_testcorpus(req.corpus_ids, req.label, req.is_tone, req.is_normalize_loudness)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/create_test_corpus")
async def create_test_corpus(req: TestCorpusCreateRequest):
    ret = create_testcorpus(
        req.text,
        req.test_type,
        req.test_scenario,
        req.speaker,
        req.language,
        req.car_function,
        req.label,
        req.expect_result,
        False,
        req.target_signal,
    )
    # if ret:
    #     return JSONResponse(status_code=500, content={"error_msg": "create_test_corpus fail"})
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/update_test_corpus")
async def update_test_corpus(req: TestCorpusUpdateRequest):
    ret = update_testcorpus(
        req.corpus_id,
        req.text,
        req.test_type,
        req.test_scenario,
        req.speaker,
        req.language,
        req.car_function,
        req.label,
        req.expect_result,
        req.target_signal,
    )
    if ret:
        return HttpResponse(status=StatusCode.CORPUS_IN_USE.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_test_corpus")
async def delete_test_corpus(req: CorpusDeleteRequest):
    ret = delete_testcorpus(req.corpus_id)
    if ret:
        return HttpResponse(status=StatusCode.CORPUS_IN_USE.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/batch_delete_test_corpus")
async def batch_delete_test_corpus(req: CorpusBatchDeleteRequest):
    ret = batch_delete_testcorpus(req.corpus_ids)
    if len(ret) == 0:
        return HttpResponse(status=StatusCode.OK.code)
    else:
        result = HttpDataListResponse(list=ret, total=len(ret))
        err_str = ",".join(ret)
        err_msg = f"删除语料{err_str}失败"
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, data=result, error_msg=err_msg)


@router.post("/upload_test_corpus")
async def upload_test_corpus(req: TestCorpusUploadRequest):
    ret = upload_testcorpus(
        req.corpus_id,
        req.aud_id,
        req.audio_url,
        req.pinyin,
        req.audio_duration,
        req.text,
    )
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post(
    "/get_test_corpus_list"
)  # 此类结构需要注意，我直接用的传过来数据的 key 作为的查询 key，所以一定要和数据库存的字段相匹配
async def get_test_corpus_list(request: Request):
    data = await request.json()

    data_list, total = test_corpus_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)


# 唤醒语料
@router.post("/create_rouse_corpus")
async def create_rouse_corpus(req: RouseCorpusCreateRequest):
    ret = create_rousecorpus(
        req.text, req.test_scenario, req.speaker, req.language, req.label
    )
    # if ret:
    #     return JSONResponse(status_code=500, content={"error_msg": "create_rouse_corpus fail"})
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/synthesize_rouse_corpus")
async def synthesize_rouse_corpus(req: CorpusSynthesize):
    labels = []
    if req.label != "":
        labels.append(req.label)
    ret = synthesize_rousecorpus(req.corpus_ids, labels)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/update_rouse_corpus")
async def update_rouse_corpus(req: RouseCorpusUpdateRequest):
    ret = update_rousecorpus(
        req.corpus_id, req.text, req.test_scenario, req.speaker, req.language, req.label
    )
    if ret:
        return HttpResponse(status=StatusCode.CORPUS_IN_USE.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_rouse_corpus")
async def delete_rouse_corpus(req: CorpusDeleteRequest):
    ret = delete_rousecorpus(req.corpus_id)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/batch_delete_wake_corpus")
async def batch_delete_wake_corpus(req: CorpusBatchDeleteRequest):
    ret = batch_delete_rousecorpus(req.corpus_ids)
    if len(ret) == 0:
        return HttpResponse(status=StatusCode.OK.code)
    else:
        result = HttpDataListResponse(list=ret, total=len(ret))
        err_str = ",".join(ret)
        err_msg = f"删除语料{err_str}失败"
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, data=result, error_msg=err_msg)


@router.post("/upload_rouse_corpus")
async def upload_rouse_corpus(req: TestCorpusUploadRequest):
    ret = upload_rousecorpus(
        req.corpus_id,
        req.aud_id,
        req.audio_url,
        req.pinyin,
        req.audio_duration,
        req.text,
    )
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_rouse_corpus_list")
async def get_rouse_corpus_list(request: Request):
    data = await request.json()

    data_list, total = rouse_corpus_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)


# 干扰语料
@router.post("/create_disturb_corpus")
async def create_disturb_corpus(request: Request):
    data = await request.json()

    ret = disturb_corpus_create(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_disturb_corpus")
async def update_disturb_corpus(request: Request):
    data = await request.json()

    ret = disturb_corpus_update(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_disturb_corpus")
async def delete_disturb_corpus(request: Request):
    data = await request.json()

    ret, err_msg = disturb_corpus_delete(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=err_msg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_disturb_corpus_list")
async def get_disturb_corpus_list(request: Request):
    data = await request.json()

    res, total = disturb_corpus_list(data)
    result = HttpDataListResponse(list=res, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)


# 背景噪声
@router.post("/create_background_noise")
async def create_background_noise(request: Request):
    data = await request.json()

    ret = background_noise_create(data)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/update_background_noise")
async def update_background_noise(request: Request):
    data = await request.json()

    ret = background_noise_update(data)
    if ret:
        HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/delete_background_noise")
async def delete_background_noise(request: Request):
    data = await request.json()

    ret, err_msg = background_noise_delete(data)
    if ret:
        HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=err_msg)
    return HttpResponse(status=StatusCode.OK.code)


@router.post("/get_background_noise_list")
async def get_background_noise_list(request: Request):
    data = await request.json()

    res, total = background_noise_list(data)
    result = HttpDataListResponse(list=res, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)
