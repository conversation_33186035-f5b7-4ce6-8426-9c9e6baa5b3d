from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
from app.service.play_config import play_config_create, play_config_update, play_config_delete, play_config_list
from app.proto.play_config_proto import PlayConfigCreateRequest, PlayConfigUpdateRequest, PlayConfigDeleteRequest
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/play_config")

@router.post("/create_play_config")
async def create_play_config(req: PlayConfigCreateRequest):
    ret, _ = play_config_create(req.config_name, req.description, req.type)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_play_config")
async def update_play_config(req: PlayConfigUpdateRequest):
    ret = play_config_update(req.play_config_id, req.config_name, req.description, req.type, req.configs)
    if ret:
        return HttpResponse(status=StatusCode.PLAY_CONFIG_IN_USE.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/delete_play_config")
async def delete_play_config(req: PlayConfigDeleteRequest):
    ret = play_config_delete(req.play_config_id)
    if ret:
        return HttpResponse(status=StatusCode.PLAY_CONFIG_IN_USE.code, error_msg=ret)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/get_play_config_list")
async def get_play_config_list(request: Request):
    data = await request.json()

    data_list, total = play_config_list(data)
    result = HttpDataListResponse(list=data_list, total=total, page=data.get("page"), page_size=data.get("page_size"))
    return HttpResponse(status=StatusCode.OK.code, data=result)