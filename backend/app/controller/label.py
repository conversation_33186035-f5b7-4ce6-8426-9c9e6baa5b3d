from fastapi import APIRouter,File, UploadFile, Form, Request
from app.service.label import create_labelgroup, delete_labelgroup, update_labelgroup, get_test_labels,\
                get_label_groups, create_label_info, delete_label_info, update_label_info
from app.proto.label_proto import Label<PERSON>reateRequest, LabelUpdateRequest, LabelDeleteRequest,\
    LabelGroupCreateRequest, LabelGroupUpdateRequest, LabelGroupDeleteRequest
from app.proto import HttpResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/labels")

@router.post("/get_label_group_list")
async def get_label_group_list(request: Request):
    data = await request.json()

    res = get_label_groups(data)
    return res

@router.post("/create_label_group")
async def create_label_group(req: LabelGroupCreateRequest):
    ret = create_labelgroup(req.label_group_name, req.label_group_type)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/delete_label_group")
async def delete_label_group(req: LabelGroupDeleteRequest):
    ret = delete_labelgroup(req.label_group_id)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_label_group")
async def update_label_group(req: LabelGroupUpdateRequest):
    ret = update_labelgroup(req.label_group_id, req.label_group_name, req.label_group_type)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/get_label_list")
async def get_label_list(request: Request):
    data = await request.json()

    res = get_test_labels(data)
    return res

@router.post("/create_label")
async def create_label(req: LabelCreateRequest):
    ret = create_label_info(req.label_name, req.label_group_id, req.color, req.range)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/delete_label")
async def delete_label(req: LabelDeleteRequest):
    ret = delete_label_info(req.label_id)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/update_label")
async def update_label(req: LabelUpdateRequest):
    ret = update_label_info(req.label_id, req.label_name, req.label_group_id, req.color, req.range)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=StatusCode.EXECUTE_FAIL.errmsg)
    return HttpResponse(status=StatusCode.OK.code)