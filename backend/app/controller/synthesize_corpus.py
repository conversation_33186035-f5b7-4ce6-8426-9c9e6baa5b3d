import asyncio
import os.path
import json
import time

from fastapi import APIRouter,File, UploadFile, Form
from fastapi.responses import JSONResponse
from app.dao.models import RouseCorpus, CorpusLabel
from app.proto import SynthesizeListRequest, SynthesizeListResponse, SynthesizeProcessRequest, BatchSynthesizeProcessRequest, CorpusGeneralizate
from app.service.synthesize_svc import SynthesizeSvc
from app.utils.utils import chinese_to_pinyin, get_audio_duration, get_file_dir
from app.dao.corpus_dao import CorpusOperateDao, CorpusQueryDao
from app.dao.models.sqlite_gen import CorpusAudio, TestCorpus, DisturbCorpus
from app.constant import Vocalists
from app.service.corpus import excel_create_syncorpus
from app.service.generalizate_svc import generalizate_local
from app.dao.base_dao import find_table_id
from app.proto import HttpResponse, HttpDataListResponse
from app.constant.code import StatusCode


router = APIRouter(prefix="/synthesize")


@router.post("/synthesize_list")
async def synthesize_list(req: SynthesizeListRequest):
    data = {
        "is_tts": True
    }
    res = []
    corpus_list, total = CorpusQueryDao.showAllTestCorpus(data)
    for corpus in corpus_list:
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            label_list.append(label.label_name)
        temp = {"corpus_id": corpus.corpus_id, "aud_id": corpus.aud_id, "text": corpus.text, "pinyin": corpus.pinyin,
                "test_scenario": corpus.test_scenario, "evaluation_metric": corpus.evaluation_metric,
                "audio_url": corpus.audio_url, "audio_duration": corpus.audio_duration, "speaker": corpus.speaker,
                "language": corpus.language, "label": label_list, "operation": corpus.operation, "expect_result": corpus.expect_result,
                "car_function": corpus.car_function, "audio_path": corpus.audio_path, "is_tts": corpus.is_tts}

        res.append(temp)

    corpus_list, _ = CorpusQueryDao.showAllRouseCorpus(data)
    for corpus in corpus_list:
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            label_list.append(label.label_name)
        temp = {"corpus_id": corpus.corpus_id, "aud_id": corpus.aud_id, "text": corpus.text, "pinyin": corpus.pinyin,
                "test_scenario": corpus.test_scenario, "test_object": corpus.test_object,
                "audio_url": corpus.audio_url, "audio_duration": corpus.audio_duration, "speaker": corpus.speaker,
                "language": corpus.language, "label": label_list, "operation": corpus.operation,
                "audio_path": corpus.audio_path, "is_tts": corpus.is_tts}

        res.append(temp)

    corpus_list, _ = CorpusQueryDao.showAllDisturbCorpus(data)
    for corpus in corpus_list:
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            label_list.append(label.label_name)
        temp = {"corpus_id": corpus.corpus_id, "aud_id": corpus.aud_id, "text": corpus.text, "pinyin": corpus.pinyin,
                "audio_url": corpus.audio_url, "audio_duration": corpus.audio_duration, "speaker": corpus.speaker,
                "language": corpus.language, "label": label_list, "operation": corpus.operation,
                "audio_path": corpus.audio_path, "is_tts": corpus.is_tts}

        res.append(temp)

    result = HttpDataListResponse(list=res, total=len(res))
    return HttpResponse(status=StatusCode.OK.code, data=result)

@router.post("/process_synthesize")
async def process_synthesize(req: SynthesizeProcessRequest):
    svc = SynthesizeSvc()
    loop = asyncio.get_event_loop()
    ret, url = await loop.run_in_executor(None, svc.synthesize, req.text, req.name, req.voice, req.language, req.type, req.is_tone)
    # ret, url = svc.synthesize(req.text, req.name, req.voice, req.language, req.type, req.is_tone)
    if ret:
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg="process_synthesize fail")
    if (req.voice == 1 and req.language >= 7) or (req.voice == 2 and req.language >= 8):
        time.sleep(1)
    file_name_pinyin = chinese_to_pinyin(req.text)
    audio_id = find_table_id("corpus_audio_id")
    
    duration = get_audio_duration(url)
    if duration["status"] == "error":
        os.remove(url)
        return HttpResponse(status=StatusCode.EXECUTE_FAIL.code, error_msg=duration["data"])
    audio = CorpusAudio(
        aud_id="audio_" + str(audio_id),
        aud_url=url,
        pinyin=file_name_pinyin,
        audio_duration=duration["data"]
    )
    save_audio = CorpusOperateDao.saveAudio(audio)
    name = os.path.basename(url)
    if req.voice == Vocalists.female:
        voice = "female"
    elif req.voice == Vocalists.male:
        voice = "male"
    else:
        voice = ""
    # language = "mandarin"
    temp_corpus_id = ''
    if req.type == 2:
        corpus_id = find_table_id("rouse_corpus_id")
        temp_corpus_id = "rousecorpus_" + str(corpus_id)
        corpus = RouseCorpus(
            corpus_id=temp_corpus_id,
            aud_id=save_audio.aud_id,
            text=req.text,
            pinyin=file_name_pinyin,
            audio_url=name,
            audio_duration=duration["data"],
            speaker=voice,
            language=req.language,
            test_scenario="wake-up",
            # label=req.label,
            audio_path=url,
            is_tts=True
        )
    elif req.type == 3:
        corpus_id = find_table_id("disturb_corpus_id")
        temp_corpus_id = "disturbcorpus_" + str(corpus_id)
        corpus = DisturbCorpus(
            corpus_id=temp_corpus_id,
            aud_id=save_audio.aud_id,
            text=req.text,
            pinyin=file_name_pinyin,
            audio_url=name,
            audio_duration=duration["data"],
            speaker=voice,
            language=req.language,
            # label=req.label,
            audio_path=url,
            is_tts=True
        )
    else:
        corpus_id = find_table_id("test_corpus_id")
        temp_corpus_id = "testcorpus_" + str(corpus_id)
        corpus = TestCorpus(
            corpus_id=temp_corpus_id,
            aud_id=save_audio.aud_id,
            text=req.text,
            pinyin=file_name_pinyin,
            audio_url=name,
            audio_duration=duration["data"],
            speaker=voice,
            language=req.language,
            # label=req.label,
            expect_result=req.expect_result,
            audio_path=url,
            is_tts=True
        )
    label_temp = CorpusLabel(
        corpus_id = temp_corpus_id,
        label_name = req.label
    )
    CorpusOperateDao.saveCorpusLabel(label_temp)
    save_corpus = CorpusOperateDao.saveTestCorpus(corpus)
    CorpusOperateDao.updateAudio(save_audio.aud_id,{"corpus_id": save_corpus.corpus_id})
    return HttpResponse(status=StatusCode.OK.code)

@router.post("/batch_process_synthesize")
async def batch_process_synthesize(data_list: BatchSynthesizeProcessRequest):
    reqs = data_list.list
    for req in reqs:
        await process_synthesize(req)
    return HttpResponse(status=StatusCode.OK.code)

# 上传excel，批量生成 测试语料
@router.post("/batch_import")
async def upload_audio_file(file: UploadFile = File(...), info: str = Form(...)):
    data = json.loads(info)
    file_data = await file.read()
    res = excel_create_syncorpus(file, file_data, data)
    return res

@router.post("/corpus_generalizate")
async def corpus_generalizate(req: CorpusGeneralizate):
    result = []
    if req.text:
        result = generalizate_local(req.text)

    data_list = HttpDataListResponse(list=result, total=len(result))
    return HttpResponse(status=StatusCode.OK.code, data=data_list)
