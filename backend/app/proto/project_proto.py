from typing import Optional
from pydantic import BaseModel, Field

class TestResultCheckRequest(BaseModel):
    # project_id: Optional[str] = Field(..., description="项目id")
    # turn_id: Optional[str] = Field(default="", description="轮次id")
    result_id: Optional[str] = Field(default="", description="结果id")
    result: Optional[str] = Field(default="", description="结果")

class TestExportResultsRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    turn_id: Optional[int] = Field(default=0, description="轮次id")
    type: Optional[str] = Field(default="", description="类型")
    columns: Optional[list] = Field(default=[], description="所需表头列表")
    order: Optional[str] = Field(default="asc", description="排序")

class GetTestInfoRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    turn_id: Optional[str] = Field(default="", description="轮次id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    type: Optional[str] = Field(default="", description="类型")
    result: Optional[str] = Field(default="", description="测试结果")
    sign: Optional[str] = Field(default="", description="测试结果")
    page: Optional[int] = Field(default=1, description="当前页码")
    page_size: Optional[int] = Field(default=20, description="每页数据量")

class ProgetInitRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    rouse_corpus: Optional[str] = Field(default="", description="长响应语料")
    long_response_corpus: Optional[str] = Field(default="", description="长响应语料")
    long_order_corpus: Optional[str] = Field(default="", description="长指令语料")
    exit_corpus: Optional[str] = Field(default="", description="退出语料")

class ProgetInitResponse(BaseModel):
    wake_up_picture: Optional[str] = Field(default="", description="车机唤醒图片")
    response_audio: Optional[list] = Field(default="", description="长响应语料")

class TestGenerateReportRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    report_name: Optional[str] = Field(default="", description="报告名字")
    report_number: Optional[str] = Field(default="", description="报告编号")
    testing_unit: Optional[str] = Field(default="", description="测试单位")
    turn_id: Optional[int] = Field(default=0, description="轮次")

class CreateRetestPlanRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    turn_id: Optional[int] = Field(default=0, description="轮次")
    retest_plan_name: Optional[str] = Field(default="", description="复测方案名字")
    corpus_columns: Optional[list] = Field(default=[], description="所需复测结果对应的语料列表")

class ExecuteRetestPlanRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    turn_id: Optional[int] = Field(default=0, description="轮次")

class ResultRemoveSignRequest(BaseModel):
    project_id: Optional[str] = Field(..., description="项目id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    turn_id: Optional[int] = Field(default=0, description="轮次")
    result_id: Optional[str] = Field(default="", description="结果id")
    sign: Optional[str] = Field(default="", description="需要移除的标签")

class TestResultUpdateRequest(BaseModel):
    project_id: Optional[str] = Field(..., description="项目id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    turn_id: Optional[int] = Field(default=0, description="轮次")
    result_id: Optional[str] = Field(default="", description="结果id")
    new_result: Optional[str] = Field(default="", description="需要修改的结果")
    update_result_reason: Optional[str] = Field(default="", description="修改测试结果理由")

class TestResultUpdateOCRRequest(BaseModel):
    result_id: Optional[str] = Field(..., description="result id")
    ocr_accuracy_rate: Optional[float] = Field(default=-1, description="OCR识别率")

class MultiTestResultUpdateRequest(BaseModel):
    update_result_list: Optional[list] = Field(default=[], description="需要更新结果的list")
    new_result: Optional[str] = Field(default="", description="需要修改的结果")
    update_result_reason: Optional[str] = Field(default="", description="修改测试结果理由")
    multi_id: Optional[str] = Field(default=False, description="多轮结果id")
    multi_result_list: Optional[list] = Field(default=[], description="结果id list")

class AddRetestCorpusRequest(BaseModel):
    project_id: Optional[str] = Field(default="", description="项目id")
    plan_id: Optional[str] = Field(default="", description="方案id")
    corpus_ids: Optional[list] = Field(default=[], description="所需复测结果对应的语料列表")

class GetSignalData(BaseModel):
    result_id: Optional[str] = Field(default="", description="结果id")
    signal_name: Optional[str] = Field(default="", description="信号名")