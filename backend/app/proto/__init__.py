import typing
from app.proto.synthesize_proto import *

class HttpResponse(BaseModel):
    status: Optional[int] = Field(default=0, description="状态码,0正常")
    error_msg: Optional[str] = Field(default="", description="错误信息")
    data: typing.Any = Field(default=None, description="数据")


class HttpDataListResponse(BaseModel):
    list: typing.List = Field(default=[], description="数据数组")
    total: Optional[int] = Field(default=0, description="总数")
    page: Optional[int] = Field(default=1, description="当前页码")
    page_size: Optional[int] = Field(default=20, description="每页数据量")
