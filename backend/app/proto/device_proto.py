from typing import Optional
from pydantic import BaseModel, Field


class DeviceConfigResponse(BaseModel):
    # ocr config
    camera_interval: Optional[float] = Field(None, description="ocr抓拍间隔", gt=0)
    camera_times: Optional[int] = Field(None, description="ocr抓拍次数", gt=0)
    camera_start_wait: Optional[float] = Field(None, description="ocr抓拍等待时间", ge=0)
    # llm result judge thread num
    result_judge_thread: Optional[int] = Field(None, description="模型判断并发线程数", gt=0)
    # llm result photo config
    result_photo_interval: Optional[float] = Field(None, description="结果图片抓拍间隔", gt=0)
    result_photo_diff_rate: Optional[float] = Field(None, description="结果图片变化率", gt=0, le=1)
    result_start_wait: Optional[float] = Field(None, description="结果图片抓拍等待时间", ge=0)
    result_timeout: Optional[float] = Field(None, description="结果图片抓拍超市时间", gt=0)
    # Camera Resolution
    video_width: Optional[int] = Field(None, description="分辨率宽度", gt=0)
    video_height: Optional[int] = Field(None, description="分辨率高度", gt=0)
    video_frame_rate: Optional[float] = Field(None, description="帧率", gt=0)
    # 录音
    record_end_wait_time: Optional[int] = Field(None, description="录音结束等待时间", ge=0)


class SetCorpusDeviceRequest(BaseModel):
    corpus: Optional[str] = Field(..., description="选择设置那个语料的播放设备")
    id: Optional[int] = Field(0, description="输出设备id")
    volume: Optional[float] = Field(0, description="声音增益")
    is_default: Optional[bool] = Field(None, description="是否使用默认的输出设备")


class SetVideoDeviceRequest(BaseModel):
    id: Optional[int] = Field(..., description="录像设备id")
    name: Optional[str] = Field(..., description="录像设备名字")
