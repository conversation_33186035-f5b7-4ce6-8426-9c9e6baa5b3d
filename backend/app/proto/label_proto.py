from typing import Optional
from pydantic import BaseModel, Field

class LabelCreateRequest(BaseModel):
    label_name: Optional[str] = Field(default="", description="标签名")
    label_group_id: Optional[str] = Field(default="", description="标签组id")
    color: Optional[str] = Field(default="", description="标签颜色")
    range: Optional[str] = Field(default="", description="标签范围")

class LabelUpdateRequest(BaseModel):
    label_id: Optional[str] = Field(..., description="标签ID")
    label_name: Optional[str] = Field(default="", description="标签名")
    label_group_id: Optional[str] = Field(default="", description="标签组id")
    color: Optional[str] = Field(default="", description="标签颜色")
    range: Optional[str] = Field(default="", description="标签范围")

class LabelDeleteRequest(BaseModel):
    label_id: Optional[str] = Field(..., description="标签ID")

class LabelGroupCreateRequest(BaseModel):
    label_group_name: Optional[str] = Field(default="", description="标签组名")
    label_group_type: Optional[str] = Field(default="", description="标签组类型")

class LabelGroupUpdateRequest(BaseModel):
    label_group_id: Optional[str] = Field(..., description="标签组ID")
    label_group_name: Optional[str] = Field(default="", description="标签组名")
    label_group_type: Optional[str] = Field(default="", description="标签组类型")

class LabelGroupDeleteRequest(BaseModel):
    label_group_id: Optional[str] = Field(..., description="标签组ID")