import threading
import os
import sys
from pydantic_settings import BaseSettings


class AppConfigSettings(BaseSettings):
    # 基础配置
    app_name: str = "CVAtest"
    app_host: str = "0.0.0.0"
    app_port: int = 8080
    app_env: str = "dev"
    app_debug: bool = False

    # data upload
    ocr_confidence_threshold: int = 9
    llm_confidence_threshold: int = 5
    data_upload: int = 1  # 0 代表不上传、1 代表全都上传、2 代表置信度低于 confidence_threshold 上传
    CVA_DATA_SERVER: str = "http://*************:61293"

    # 数据库配置
    db_dsn: str = "sqlite:///cavtest.db"  # mysql+pymysql://username:password@localhost:3306/database_name
    db_echo_sql: bool = False  # 使用打印SQL日志信息
    db_pool_size: int = 10  # 连接池中的初始连接数，默认为 5
    db_max_overflow: int = 20  # 连接池中允许的最大超出连接数

    # 文件存储位置配置
    current_dir: str = os.path.dirname(os.path.abspath(__file__))
    abs_path: str = os.path.abspath(current_dir)
    base_dir: str = os.path.dirname(os.path.dirname(abs_path))
    audio_path: str = os.path.join(base_dir, 'audio')
    mic_path: str = os.path.join(base_dir, 'mic_audio')
    rouse_corpus_path: str = os.path.join(audio_path, 'rouse_corpus')
    synthesize_corpus_path: str = os.path.join(audio_path, 'synthesize')
    test_corpus_path: str = os.path.join(audio_path, 'test_corpus')
    disturb_corpus_path: str = os.path.join(audio_path, 'disturb_corpus')

    # 照片存储目录
    photo_dir: str = os.path.join(base_dir, 'photo')
    photo_project_init_dir: str = os.path.join(photo_dir, 'init')

    # 视频存储目录
    video_dir: str = os.path.join(base_dir, 'video')

    # 视频存储目录
    excel_dir: str = os.path.join(base_dir, 'excel')

    # dbc文件存储目录
    dbc_path: str = os.path.join(base_dir, 'dbc')
    can_path: str = os.path.join(base_dir, 'can_data')

    # scripts文件存储目录
    script_path: str = os.path.join(base_dir, 'scripts')

    # ASR
    asr_use_gpu: bool = sys.platform != 'darwin'

    # OPENAI
    OPENAI_API_KEY: str = "4ae78776-248b-8b84-d861-5b7d600ef9e5"
    OPENAI_API_BASE: str = "http://*************:8732/v1"

    # Minio
    MINIO_ENDPOINT: str = "*************:39000"
    MINIO_ACCESS_KEY: str = "qHGNbLRWtJcfJsDkl3LK"
    MINIO_SECRET_KEY: str = "qmkA57ujunxw2cEpmPCVfsHKtXdbRAfv0SYvtdQC"


APP_RUN = False
APP_RUN_LOCK = threading.Lock()


def set_app_run(flag: bool):
    global APP_RUN
    with APP_RUN_LOCK:
        APP_RUN = flag


def get_app_run():
    with APP_RUN_LOCK:
        app_run = APP_RUN
    return app_run


globalAppSettings = AppConfigSettings()
if not os.path.exists(globalAppSettings.photo_project_init_dir):
    os.makedirs(globalAppSettings.photo_project_init_dir)
if not os.path.exists(globalAppSettings.video_dir):
    os.makedirs(globalAppSettings.video_dir)
if not os.path.exists(globalAppSettings.rouse_corpus_path):
    os.makedirs(globalAppSettings.rouse_corpus_path)
if not os.path.exists(globalAppSettings.synthesize_corpus_path):
    os.makedirs(globalAppSettings.synthesize_corpus_path)
if not os.path.exists(globalAppSettings.test_corpus_path):
    os.makedirs(globalAppSettings.test_corpus_path)
if not os.path.exists(globalAppSettings.disturb_corpus_path):
    os.makedirs(globalAppSettings.disturb_corpus_path)
if not os.path.exists(globalAppSettings.mic_path):
    os.makedirs(globalAppSettings.mic_path)
if not os.path.exists(globalAppSettings.excel_dir):
    os.makedirs(globalAppSettings.excel_dir)
if not os.path.exists(globalAppSettings.dbc_path):
    os.makedirs(globalAppSettings.dbc_path)
if not os.path.exists(globalAppSettings.can_path):
    os.makedirs(globalAppSettings.can_path)
