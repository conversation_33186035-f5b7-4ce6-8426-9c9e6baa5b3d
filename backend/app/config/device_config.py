import json
import threading
import yaml
import os
import sys
import sounddevice as sd
from dataclasses import dataclass, asdict
from app.config.app_config import get_app_run

NO_CAMERA_JSON = {"id": -1, "name": "no camera"}


def is_windows_os():
    """综合判断操作系统类型"""
    import sys
    import platform
    return any([
        sys.platform.startswith('win'),
        platform.system().lower() == 'windows',
        os.name == 'nt'
    ])


if is_windows_os():
    from PyCameraList.camera_device import list_video_devices
    import win32com.client


class DeviceConfig:
    def __init__(self) -> None:
        current_file_path = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_file_path)
        self.yaml = os.path.join(current_dir, "device_config.yaml")
        self.camera_interval = "camera_interval"
        self.camera_times = "camera_times"
        self.camera_start_wait = "camera_start_wait"
        # llm result judge thread num
        self.result_judge_thread = "result_judge_thread"
        # llm result photo config
        self.result_photo_interval = "result_photo_interval"
        self.result_photo_diff_rate = "result_photo_diff_rate"
        self.result_start_wait = "result_start_wait"
        self.result_timeout = "result_timeout"
        # 录音
        self.record_end_wait_time = "record_end_wait_time"
        # Camera Resolution
        self.video_width = "video_width"
        self.video_height = "video_height"
        self.video_frame_rate = "video_frame_rate"
        # 播放设备配置，测试语料和干扰语料
        self.play_test_corpus_device = "play_test_corpus_device"
        self.play_disturb_corpus_device = "play_disturb_corpus_device"
        self.video_device = "video_device"
        self.video_cabin_device = "video_cabin_device"
        self.lock = threading.Lock()
        default_audio_device = get_default_audio_device()
        self.default_map = {
            "camera_interval": 0.5,
            "camera_start_wait": 2.0,
            "result_judge_thread": 5,
            "camera_times": 16,
            "result_photo_diff_rate": 0.1,
            "result_photo_interval": 0.75,
            "result_start_wait": 0,
            "result_timeout": 15,
            "record_end_wait_time": 1,
            "video_height": 1080,
            "video_width": 1920,
            "video_frame_rate": 30,
            "play_test_corpus_device": default_audio_device,
            "play_disturb_corpus_device": default_audio_device,
            "video_device": get_default_video_device(),
            "video_cabin_device": json.dumps(NO_CAMERA_JSON, ensure_ascii=False),
        }

        if os.path.exists(self.yaml):
            self.device_map = self.read_yaml_file()
            write_flag = False
            for k, v in self.default_map.items():
                if k not in self.device_map:
                    self.device_map[k] = v
                    write_flag = True
            if write_flag:
                self.write_yaml_file(self.device_map)
        else:
            self.device_map = self.default_map
            self.write_yaml_file(self.device_map)

    def read_yaml_file(self):
        with open(self.yaml, "r", encoding="utf-8") as file:
            data = yaml.safe_load(file)  # 使用 safe_load 更安全
        return data

    def write_yaml_file(self, device):
        with open(self.yaml, "w", encoding="utf-8") as file:
            yaml.dump(device, file, allow_unicode=True)
        return

    def set(self, key, value):
        self.lock.acquire()
        self.device_map[key] = value
        self.write_yaml_file(self.device_map)
        self.lock.release()

    def set_all(self):
        self.lock.acquire()
        self.write_yaml_file(self.device_map)
        self.lock.release()

    def get(self, key):
        self.lock.acquire()
        value = self.device_map.get(key, "")
        self.lock.release()
        return value

    def set_ocr_config(self, start_wait: float = None, interval: int = None, times: float = None):
        self.lock.acquire()
        if start_wait:
            self.device_map[self.camera_start_wait] = start_wait
        if interval:
            self.device_map[self.camera_interval] = interval
        if times:
            self.device_map[self.camera_times] = times
        self.write_yaml_file(self.device_map)
        self.lock.release()

    def get_ocr_config(self):
        self.lock.acquire()
        camera_interval = self.device_map[self.camera_interval]
        camera_times = self.device_map[self.camera_times]
        camera_start_wait = self.device_map[self.camera_start_wait]
        self.lock.release()
        return camera_start_wait, camera_interval, camera_times

    def get_result_photo_config(self):
        self.lock.acquire()
        result_photo_interval = self.device_map[self.result_photo_interval]
        result_start_wait = self.device_map[self.result_start_wait]
        result_photo_diff_rate = self.device_map[self.result_photo_diff_rate]
        result_timeout = self.device_map[self.result_timeout]
        self.lock.release()
        return result_start_wait, result_photo_interval, result_photo_diff_rate, result_timeout

    def get_video_config(self):
        self.lock.acquire()
        video_width = self.device_map[self.video_width]
        video_height = self.device_map[self.video_height]
        video_frame_rate = self.device_map[self.video_frame_rate]
        video_device = self.device_map[self.video_device]
        self.lock.release()
        video_index = json.loads(video_device)
        return video_frame_rate, video_width, video_height, video_index["id"]

    def get_cabin_video_config(self):
        self.lock.acquire()
        video_width = self.device_map[self.video_width]
        video_height = self.device_map[self.video_height]
        video_frame_rate = self.device_map[self.video_frame_rate]
        video_device = self.device_map[self.video_cabin_device]
        self.lock.release()
        video_index = json.loads(video_device)
        return video_frame_rate, video_width, video_height, video_index["id"]

    def get_video_name(self):
        video_device = self.device_map[self.video_device]
        return json.loads(video_device)["name"]

    def get_cabin_video_name(self):
        video_device = self.device_map[self.video_cabin_device]
        return json.loads(video_device)["name"]

    def get_test_corpus_device_id(self):
        device = self.device_map[self.play_test_corpus_device]
        return get_device_id(device)

    def get_disturb_corpus_device_id(self):
        device = self.device_map[self.play_disturb_corpus_device]
        return get_device_id(device)

    def get_test_corpus_device_volume(self):
        device = self.device_map[self.play_test_corpus_device]
        return get_device_volume(device)

    def get_disturb_corpus_device_volume(self):
        device = self.device_map[self.play_disturb_corpus_device]
        return get_device_volume(device)

    def set_test_corpus_device_id(self, device_id, volume=1.0, is_default=False):
        sd_devices = list_output_devices()
        for dev in sd_devices:
            if dev["index"] == int(device_id):
                json_str = get_device_str(dev, volume=volume, is_default=is_default)
                self.device_map[self.play_test_corpus_device] = json_str
                self.write_yaml_file(self.device_map)
                return
        json_str = self.device_map[self.play_test_corpus_device]
        json_data = json.loads(json_str)
        json_data["volume"] = volume
        json_data["is_default"] = is_default
        self.device_map[self.play_test_corpus_device] = json.dumps(json_data, ensure_ascii=False)
        self.write_yaml_file(self.device_map)

    def set_disturb_corpus_device_id(self, device_id, volume=1.0, is_default=False):
        sd_devices = list_output_devices()
        for dev in sd_devices:
            if dev["index"] == device_id:
                json_str = get_device_str(dev, volume=volume, is_default=is_default)
                self.device_map[self.play_disturb_corpus_device] = json_str
                self.write_yaml_file(self.device_map)
                return
        json_str = self.device_map[self.play_test_corpus_device]
        json_data = json.loads(json_str)
        json_data["volume"] = volume
        json_data["is_default"] = is_default
        self.device_map[self.play_test_corpus_device] = json.dumps(json_data, ensure_ascii=False)
        self.write_yaml_file(self.device_map)

    def set_video_device_id(self, vid, name):
        self.device_map[self.video_device] = json.dumps({"id": vid, "name": name}, ensure_ascii=False)
        self.write_yaml_file(self.device_map)

    def set_cabin_video_device_id(self, vid, name):
        self.device_map[self.video_cabin_device] = json.dumps({"id": vid, "name": name}, ensure_ascii=False)
        self.write_yaml_file(self.device_map)


@dataclass
class OutputDeviceInfo:
    id: int
    name: str
    samplerate: float
    api_name: str
    volume: float
    is_default: bool


def get_device_id(device):
    if device:
        data = json.loads(device)
        if data["is_default"]:
            return sd.default.device[1]
        return data["id"]
    return sd.default.device[1]


def get_device_volume(device):
    if device:
        data = json.loads(device)
        return data["volume"]
    return 1.0


# 列出所有输出设备（借助sounddevice）
def list_output_devices():
    # 没有project在运行才重新初始化sd
    if not get_app_run():
        sd._terminate()
        sd._initialize()
    devices = sd.query_devices()
    host_apis = sd.query_hostapis()
    virtual_keywords = ['映射器', '主声音驱动程序', '声音驱动程序']
    filtered = []
    for dev in devices:
        # 基础过滤：只保留输出设备
        if dev['max_output_channels'] == 0:
            continue

        # 获取宿主API信息
        api_info = host_apis[dev['hostapi']]

        # 排除策略：虚拟设备 + 指定API类型
        if any(kw in dev['name'] for kw in virtual_keywords):
            continue

        # 标记需要保留的设备
        dev['api_name'] = api_info['name']  # 添加宿主API信息
        filtered.append(dev)
    # for i, dev in enumerate(filtered):
    #     print(f"ID {dev['index']}: {dev['name']} (采样率: {dev['default_samplerate']} 驱动接口:{dev['api_name']})")
    return filtered


def get_device_str(dev, volume=1.0, is_default=False, is_list=False):
    data = OutputDeviceInfo(id=dev['index'], name=dev['name'], samplerate=dev['default_samplerate'],
                            api_name=dev['api_name'], volume=volume, is_default=is_default)
    data_dict = asdict(data)
    if is_list:
        del data_dict["is_default"]
        del data_dict["volume"]
    json_str = json.dumps(data_dict, ensure_ascii=False)
    return json_str


def get_default_audio_device():
    default_output_id = sd.default.device[1]
    sd_devices = list_output_devices()
    for dev in sd_devices:
        if dev["index"] == default_output_id:
            return get_device_str(dev, is_default=True)
    return ""


def get_audio_device_map():
    sd_devices = list_output_devices()
    devices_map = {}
    if sys.platform == "darwin":
        for dev in sd_devices:
            devices_map[dev['index']] = json.loads(get_device_str(dev, is_list=True))
    else:
        names_map = {}
        pnp_names = get_audio_windows_devices()
        for name in pnp_names:
            names_map[name.strip()] = 1
        for dev in sd_devices:
            if names_map.get(dev["name"].strip()):
                devices_map[dev['index']] = json.loads(get_device_str(dev, is_list=True))
    return devices_map


# def get_audio_windows_devices():
#     # 通过 PowerShell 获取 AudioEndpoint 设备信息，并转换为 JSON 格式
#     command = [
#         "powershell",
#         "-Command",
#         "Get-PnpDevice -Class AudioEndpoint | Select-Object Status, FriendlyName, InstanceId | ConvertTo-Json"
#     ]
#     result = subprocess.run(command, capture_output=True, text=True, check=True, encoding=locale.getpreferredencoding())
#     devices = json.loads(result.stdout)
#     # 提取 FriendlyName 到列表
#     friendly_names = [device["FriendlyName"] for device in devices if device["Status"] == "OK"]
#     return friendly_names

def get_audio_windows_devices():
    wmi = win32com.client.Dispatch("WbemScripting.SWbemLocator")
    service = wmi.ConnectServer(".", "root\\cimv2")
    devices = service.ExecQuery(
        "SELECT Status, Name, DeviceID FROM Win32_PnPEntity WHERE PNPClass='AudioEndpoint'"
    )
    results = []
    for device in devices:
        # results.append({
        #     "Status": device.Status,
        #     "FriendlyName": device.Name,
        #     "InstanceId": device.DeviceID
        # })
        results.append(device.Name)
    return results


def get_default_video_device():
    if is_windows_os():
        cameras = list_video_devices()
        if cameras:
            return json.dumps({"id": cameras[0][0], "name": cameras[0][1]})
    else:
        return json.dumps({"id": 0, "name": "test1"})
    return json.dumps(NO_CAMERA_JSON, ensure_ascii=False)


def get_video_device_list():
    if is_windows_os():
        return list_video_devices()
    else:
        return [(0, "test1"), (1, "test2")]


# 因为import sounddevice会修改错误输出，需要重新定向回来
original_stderr = sys.stderr
original_stdout = sys.stdout
sys.stderr = sys.stdout
deviceConfig = DeviceConfig()
print("deviceConfig, ", deviceConfig.device_map)
