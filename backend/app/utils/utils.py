import ast
import subprocess
import sys
import os
import string
import pygame
import wave
import pyaudio
import time
import shutil
import pandas as pd
import openpyxl
import uuid
import librosa
import numpy as np
import scipy
import cantools
import json
import queue
import sounddevice as sd
import soundfile as sf
from app.constant import SignType
from app.config.app_config import globalAppSettings
from app.utils.asr.rtasr_python3_demo import XF_Client
from app.utils.sound.recorder import Recorder
from app.utils.sound.multi_recorder import MultiRecorder
from mutagen.mp3 import MP3
from mutagen.mp4 import MP4
from mutagen.wave import WAVE
from datetime import datetime, timedelta
from app.service.can_dk import can_data_queue
from cantools.database.can.signal import NamedSignalValue
from app.middleware.log import set_request_id, logger as log
from pypinyin import pinyin, Style


def chinese_to_pinyin(chinese_str):
    """
    将中文字符串转换为拼音。
    """
    # Style.NORMAL表示返回普通拼音，不带声调
    pinyin_list = pinyin(chinese_str, style=Style.NORMAL)

    # 将拼音列表转换为字符串，拼音之间用空格分隔
    pinyin_str = "".join(["".join(item) for item in pinyin_list])

    return pinyin_str


def get_file_dir():
    if not os.path.exists(globalAppSettings.audio_path):
        os.makedirs(globalAppSettings.audio_path)
    return globalAppSettings.audio_path


def get_mic_dir():
    if not os.path.exists(globalAppSettings.mic_path):
        os.makedirs(globalAppSettings.mic_path)
    return globalAppSettings.mic_path


def get_pic_dir():
    if not os.path.exists(globalAppSettings.photo_dir):
        os.makedirs(globalAppSettings.photo_dir)
    return globalAppSettings.photo_dir


def get_excel_dir():
    if not os.path.exists(globalAppSettings.excel_dir):
        os.makedirs(globalAppSettings.excel_dir)
    return globalAppSettings.excel_dir


def get_dbc_dir():
    if not os.path.exists(globalAppSettings.dbc_path):
        os.makedirs(globalAppSettings.dbc_path)
    return globalAppSettings.dbc_path


def get_can_dir():
    if not os.path.exists(globalAppSettings.can_path):
        os.makedirs(globalAppSettings.can_path)
    return globalAppSettings.can_path


def get_audio_duration(file_path):
    """
    获取音频文件的时长（秒），并处理可能的错误。
    """
    if not os.path.exists(file_path):
        return {"status": "error", "data": f"Error: {file_path} not found."}

    file_extension = os.path.splitext(file_path)[1].lower()
    time.sleep(1)
    # 尝试获取音频时长，最多重新尝试一次
    for attempt in range(2):
        try:
            if file_extension == ".mp3":
                audio = MP3(file_path)
            elif file_extension == ".mp4":
                audio = MP4(file_path)
            elif file_extension == ".wav":
                audio = WAVE(file_path)
            else:
                return {
                    "status": "error",
                    "data": f"Unsupported audio format: {file_extension}",
                }

            # 获取时长（秒），并精确到小数点后1位
            duration = audio.info.length
            rounded_duration = round(duration, 1)
            return {"status": "ok", "data": str(rounded_duration)}

        except Exception as e:
            if attempt == 0:
                print("fail1", e)
                # 第一次尝试失败，等待1秒后重新尝试
                time.sleep(1)
            else:
                # 第二次尝试失败，返回错误信息
                print("fail2", e)
                return {"status": "error", "data": f"An unexpected error occurred: {e}"}


def detect_end_of_speech(file_path, threshold_db=-30, frame_length=256, hop_length=64):
    y, sr = librosa.load(file_path, sr=16000)  # set to 16000
    rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)
    rms_db = librosa.amplitude_to_db(rms, ref=lambda x: np.max(np.abs(x)))
    frames_above_threshold = np.where(rms_db > threshold_db)[1]
    if len(frames_above_threshold) == 0:
        return 0.0  # or raise something there
    end_frame = frames_above_threshold[-1]
    end_time = (end_frame * hop_length + frame_length) / sr
    return round(end_time * 1000)


def get_audio_duration_ms(file_path):
    """
    获取音频文件的时长（ms），并处理可能的错误。
    """
    if not os.path.exists(file_path):
        return "error"
    file_extension = os.path.splitext(file_path)[1].lower()
    try:
        if file_extension == ".mp3":
            audio = MP3(file_path)
        elif file_extension == ".mp4":
            audio = MP4(file_path)
        elif file_extension == ".wav":
            audio = WAVE(file_path)
        else:
            return "error"
        # 获取时长（毫秒），然后转换为秒
        duration = audio.info.length  # 获取音频时长（秒）
        duration_ms = round(duration * 1000)
        last_voice = detect_end_of_speech(file_path)
        log.info(f'语料时长为 {duration_ms}, 有声音最后一帧为 {last_voice}')
        return last_voice
    except Exception as e:
        # 捕获并处理文件损坏或解析错误的情况
        return "error"


def generate_random_string(length=6):
    # 字母和数字的组合
    characters = string.ascii_letters + string.digits
    base = len(characters)

    # 获取当前时间戳
    timestamp = int(time.time() * 1000)  # 毫秒级时间戳

    # 将时间戳转换为指定字符集合的编码字符串
    encoded_string = []
    while timestamp > 0:
        timestamp, remainder = divmod(timestamp, base)
        encoded_string.append(characters[remainder])

    # 将编码结果反转并补全到所需长度
    encoded_string = "".join(reversed(encoded_string))
    if len(encoded_string) < length:
        encoded_string = (
                characters[-1] * (length - len(encoded_string)) + encoded_string
        )

    # 反转字符顺序，使其字典顺序减小
    reversed_characters = characters[::-1]
    descending_string = "".join(
        reversed_characters[characters.index(c)] for c in encoded_string
    )

    return descending_string[-length:]  # 确保长度固定


def play_audio_with_sounddevice(request_id, file_path, can_detection=False, can_url="", can_data_url="", device_id=0,
                                volume=1.0):
    set_request_id(request_id)
    block_size = 1024
    if device_id <= 0:
        device_id = sd.default.device[1]
    try:
        if ".pcm" in file_path:
            data, fs = sf.read(file_path, dtype="float32", format="RAW")
        else:
            data, fs = sf.read(file_path, dtype="float32")
        data = np.ascontiguousarray(data * volume)
        num_channels = data.shape[1] if len(data.shape) > 1 else 1
        with sd.OutputStream(samplerate=fs, device=device_id, channels=num_channels, dither_off=True,
                             blocksize=block_size) as stream:
            start = 0
            length = data.shape[0]
            while start < length:
                end = min(start + block_size, length)
                # 写入当前块数据
                stream.write(data[start:end])
                start = end
    except Exception as e:
        log.error(f"设备 {device_id} 播放出错: {str(e)}")

    if can_detection:
        log.info(f'开始获取can通道结果')
        if not os.path.exists(can_url):
            os.makedirs(can_url)
        start_time = time.time()
        save_can_data(start_time, start_time + 8, can_data_url)


def get_response_time(full_path, play_type):
    # part_file = full_path[:-9] + ".wav"
    try:
        script_path = os.path.join(globalAppSettings.script_path, "cva_spectral_contrast.py")
        cmd = f"{sys.executable} {script_path} {full_path}"
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            # check=True,
            shell=True,
            timeout=30,
        )
        if result.stdout == '-1' or not result.stdout:
            log.warn(f"get_response_time stderr:{result.stderr}")
            return -1
        response_time = float(result.stdout)
        # response_time = process_pair(model, part_file, full_path)
        return response_time
    except Exception as e:
        log.info(f"--- Error: {e=} ---")
        return None


def create_audio_client():
    audio = pyaudio.PyAudio()
    return audio


def close_audio_client(audio):
    audio.terminate()


def record_audio(audio, mic_file_name):
    FORMAT = pyaudio.paInt16  # 16位音频格式
    CHANNELS = 1  # 立体声
    RATE = 16000  # 采样率
    CHUNK = 1024  # 每个块的帧数
    RECORD_SECONDS = 5  # 录音时间

    stream = audio.open(
        format=FORMAT, channels=CHANNELS, rate=RATE, input=True, frames_per_buffer=CHUNK
    )

    frames = []
    for i in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
        data = stream.read(CHUNK)
        frames.append(data)

    stream.stop_stream()
    stream.close()
    # time.sleep(1)
    storage_path = get_mic_dir()
    filepath = os.path.join(storage_path, mic_file_name)
    with open(filepath, "wb") as f:
        f.write(b"".join(frames))
    # print(f"录音已保存为 {filepath}")
    return filepath


def record_audio_to_wav(audio, wav_name):
    FORMAT = pyaudio.paInt16  # 16位音频格式
    CHANNELS = 1  # 立体声
    RATE = 44100  # 采样率
    CHUNK = 1024  # 每个块的帧数
    RECORD_SECONDS = 5  # 录音时间

    stream = audio.open(
        format=FORMAT, channels=CHANNELS, rate=RATE, input=True, frames_per_buffer=CHUNK
    )

    frames = []
    for i in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
        data = stream.read(CHUNK)
        frames.append(data)

    stream.stop_stream()
    stream.close()
    storage_path = get_mic_dir()
    filepath = os.path.join(storage_path, wav_name)
    with wave.open(filepath, "wb") as f:
        f.setnchannels(CHANNELS)
        f.setsampwidth(audio.get_sample_size(FORMAT))
        f.setframerate(RATE)
        f.writeframes(b"".join(frames))
    return filepath


def play_audio_with_pygame_record(q, file_path, audio, filepath):
    # ile_path = get_file_dir() + '/' + file_name
    pygame.mixer.init()
    try:
        pygame.mixer.music.load(file_path)
        pygame.mixer.music.play()
        while pygame.mixer.music.get_busy():
            pygame.time.Clock().tick(10)

        # micaudio_path = record_audio(audio, mic_file_name)
        recorder = Recorder(output_file=filepath)
        voice_timestamps_list = recorder.run()  # 录音
        q.put(voice_timestamps_list)

    except Exception as e:
        print(f"播放音频时出错: {e}")
    finally:
        pygame.quit()


def play_record(request_id, q, filepath, audio_time, wait_time, record_time, photo_map):
    set_request_id(request_id)
    recorder = MultiRecorder(
        output_file=filepath,
        first_audio_time=audio_time,
        chunk_split_silence_duration=wait_time,
        max_record_time=record_time,
        photo_map=photo_map,
    )
    delay_time = recorder.run()  # 录音
    q.put(delay_time)


def record_for_time_by_sd(record_second, output_file):
    default_input = sd.default.device[0]
    device_info = sd.query_devices(default_input)
    sample_rate = device_info['default_samplerate']
    channels = device_info['max_input_channels']
    audio_data = sd.rec(int(record_second * sample_rate),
                        samplerate=sample_rate,
                        channels=channels,
                        dtype='float32')
    sd.wait()  # 等待录制完成

    # 保存为WAV文件
    sf.write(output_file, audio_data, sample_rate)
    return


def record_for_time(record_second, output_file):
    # 初始化 PyAudio 对象
    audio = pyaudio.PyAudio()
    # 打开麦克风输入流
    stream = audio.open(
        format=pyaudio.paInt16,
        channels=1,
        rate=16000,
        input=True,
        frames_per_buffer=1024,
    )
    frames = []
    # 开始录制音频
    # for i in range(0, int(16000 / 1024 * record_second)):
    #     data = stream.read(1024)
    #     frames.append(data)
    total_samples = 16000 * record_second
    chunk_size = 1024
    num_chunks = (total_samples + chunk_size - 1) // chunk_size  # 向上取整
    for _ in range(num_chunks):
        data = stream.read(chunk_size, exception_on_overflow=False)
        frames.append(data)
    # 停止并关闭音频流
    stream.stop_stream()
    stream.close()

    # 终止 PyAudio 对象
    audio.terminate()

    # 将音频数据保存为 .pcm 文件
    with open(output_file, "wb") as f:
        f.write(b"".join(frames))

    # print(f"音频已保存为 {output_file}")


def audio_to_text_xf(filepath):
    # 讯飞语音转文字函数
    client = XF_Client()
    client.send(filepath)
    time.sleep(1)
    res = client.str_output
    client.close()
    return res


def list_all_files(folder_path, file_name=""):
    file_paths = []  # 用于保存文件路径的列表
    for root, _, files in os.walk(folder_path):  # 遍历文件夹
        for file in files:
            if file_name in file:  # 检查文件名是否包含指定字段
                file_path = os.path.join(root, file)  # 构造文件的完整路径
                file_paths.append(file_path)  # 将路径添加到列表中
    return file_paths


def move_file_to_folder(file_path, folder_str):
    # 获取文件的目录  构造目标文件夹路径
    directory = os.path.dirname(file_path)
    target_folder = os.path.join(directory, folder_str)

    # 如果目标文件夹不存在，则创建它
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)

    file_name = os.path.basename(file_path)  # 获取文件名
    target_file_path = os.path.join(target_folder, file_name)  # 构造目标文件路径
    shutil.move(file_path, target_file_path)  # 移动文件

    return target_file_path


def copy_file_to_modifyfolder(file_path, folder_str=None, speaker="", language=""):
    # 获取文件的目录  构造目标文件夹路径
    target_folder = get_file_dir()
    if folder_str is not None:
        target_folder = os.path.join(target_folder, folder_str)

    # 如果目标文件夹不存在，则创建它
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)

    file_name = os.path.basename(file_path)  # 获取文件名
    if not speaker or not language:
        target_file_path = os.path.join(target_folder, file_name)  # 构造目标文件路径
    else:
        name, ext = os.path.splitext(file_name)  # 分离文件名和后缀
        target_file_path = os.path.join(target_folder, f"{name}_{speaker}_{language}{ext}")  # 构造目标文件路径
    shutil.copy(file_path, target_file_path)  # 移动文件

    return target_file_path


def pcm_to_wav(file, rate=16000):
    """
    输入为文件路径和采样频率，默认16k，本系统录音就是用的16k
    输出为这个路径下同名的文件，比如输入路径为
    输入：/Users/<USER>/Desktop/zhaoshang/CVAtest/backend/mic_test.pcm
    输出：/Users/<USER>/Desktop/zhaoshang/CVAtest/backend/mic_test.wav
    """
    if not os.path.exists(file):
        return ""
    out_name = file.replace("pcm", "wav")
    with open(file, "rb") as pcm:
        raw_data = pcm.read()
    data = np.frombuffer(raw_data, dtype=np.int16)
    scipy.io.wavfile.write(out_name, 16000, data)
    return out_name


def save_data_to_excel(file_path, file_name, turn_id, configtype, data_list, order):
    sub_name = ""
    if configtype == "rouse":
        sub_name = "唤醒"
    elif configtype == "false-rouse":
        sub_name = "误唤醒"
    elif configtype == "interaction":
        sub_name = "单次对话"
    elif configtype == "interaction-multi":
        sub_name = "连续对话"
    full_file_path = os.path.join(
        file_path, f"{file_name.strip()}_第{turn_id}轮_{sub_name}.xlsx"
    )
    if os.path.exists(full_file_path):
        try:
            os.remove(full_file_path)
        except Exception as e:
            return False, e
    # 原本data_list是倒序，正序需要再反转一下
    if order == "asc":
        data_list.reverse()
    # 将数据转换为DataFrame格式
    df = pd.DataFrame(data_list)

    # 写入Excel文件
    with pd.ExcelWriter(full_file_path, engine="xlsxwriter") as writer:
        df.to_excel(writer, index=False, sheet_name="Sheet1")

    print(f"数据已成功保存到 {full_file_path}")
    return True, full_file_path


def judge_voice_language(gender, languages):
    male, female = "男声", "女声"
    voice = -1
    if gender == male:
        voice = 1
    elif gender == female:
        voice = 2
    language = languages
    if languages == "男声1" or languages == "女声1":
        language = 1
    elif languages == "男声2" or languages == "女声2":
        language = 2
    elif languages == "男声3" or languages == "女声3":
        language = 3
    elif languages == "男声4" or languages == "女声4":
        language = 4
    elif languages == "男声5" or languages == "女声5":
        language = 5
    elif languages == "男声6" or languages == "女声6":
        language = 6
    elif languages == "女声7":
        language = 7
    elif languages == "童声":
        if gender == male:
            language = 7
        else:
            language = 8
    elif languages == "东北话":
        if gender == male:
            language = 8
        else:
            language = 11
    elif languages == "天津话" or languages == "四川话":
        language = 9
    elif languages == "粤语" and gender == female:
        language = 10
    return voice, language


def analysis_excel(file_path):
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook.worksheets[0]

    # Store the data
    num = 1
    res_list = []
    for row in range(2, sheet.max_row + 1):
        a_value = sheet[f"A{row}"].value
        if a_value is None:
            break
        b_value = sheet[f"B{row}"].value
        c_value = sheet[f"C{row}"].value
        voice, language = judge_voice_language(b_value, c_value)

        d_value = sheet[f"D{row}"].value

        c_type = -1
        if d_value == "测试语料":
            c_type = 1
        elif d_value == "唤醒语料":
            c_type = 2
        elif d_value == "干扰语料":
            c_type = 3

        e_value = sheet[f"E{row}"].value
        f_value = sheet[f"F{row}"].value

        g_value = sheet[f"G{row}"].value
        is_tone = False
        if g_value == "否":
            is_tone = False
        elif g_value == "是":
            is_tone = True

        temp_data = {
            "A": a_value,
            "B": voice,
            "C": language,
            "D": c_type,
            "E": e_value,
            "F": f_value,
            "G": is_tone,
        }

        res_list.append(temp_data)
    return res_list


def rouse_excel(sheet):
    # Store the data
    res_list = []
    for row in range(2, sheet.max_row + 1):
        a_value = sheet[f"A{row}"].value
        if a_value is None:
            break
        b_value = sheet[f"B{row}"].value
        test_sc = ""
        if b_value == "唤醒":
            test_sc = "wake-up"
        elif b_value == "声源定位":
            test_sc = "sound-source-localization"
        elif b_value == "误唤醒":
            test_sc = "false-wake-up"
        elif b_value == "车外唤醒":
            test_sc = "external-wake-up"

        c_value = sheet[f"C{row}"].value
        d_value = sheet[f"D{row}"].value

        speaker_tmp, voice = judge_voice_language(c_value, d_value)

        speaker = ""
        if speaker_tmp == 2:
            speaker = "female"
        elif speaker_tmp == 1:
            speaker = "male"
        else:
            speaker = ""

        e_value = sheet[f"E{row}"].value
        labels = []
        if e_value is not None and e_value != "":
            label = str(e_value)
            normalized_string = label.replace("，", ",")
            labels = normalized_string.split(",")
        f_value = sheet[f"F{row}"].value

        temp_data = {
            "A": a_value,
            "B": test_sc,
            "C": speaker,
            "D": voice,
            "E": labels,
            "F": f_value,
        }

        res_list.append(temp_data)
    return res_list


def test_excel(sheet):
    # Store the data
    res_list = []
    for row in range(2, sheet.max_row + 1):
        a_value = sheet[f"A{row}"].value
        if a_value is None:
            break
        b_value = sheet[f"B{row}"].value
        test_type = ""
        if b_value == "语音识别":
            test_type = "speech-recognition-interaction"
        elif b_value == "智能交互":
            test_type = "intelligent-interaction"
        elif b_value == "免唤醒":
            test_type = "wake-up-free"

        c_value = sheet[f"C{row}"].value
        test_sc = ""
        if c_value == "单指令交互":
            test_sc = "single-command-interaction"
        elif c_value == "连续对话交互":
            test_sc = "continuous-dialogue-interaction"
        elif c_value == "多指令交互":
            test_sc = "multi-command-interaction"
        elif c_value == "模糊指令交互":
            test_sc = "fuzzy-command-interaction"
        elif c_value == "多话题交叉执行":
            test_sc = "multi-topic-cross-execution"

        d_value = sheet[f"D{row}"].value
        e_value = sheet[f"E{row}"].value

        speaker_tmp, voice = judge_voice_language(d_value, e_value)

        speaker = ""
        if speaker_tmp == 2:
            speaker = "female"
        elif speaker_tmp == 1:
            speaker = "male"
        else:
            speaker = ""

        f_value = sheet[f"F{row}"].value
        fun = ""
        if f_value == "音视频":
            fun = "audio-video"
        elif f_value == "导航和出行":
            fun = "navigation-travel"
        elif f_value == "通讯":
            fun = "communication"
        elif f_value == "车辆设置与信息查询":
            fun = "vehicle-settings-info-query"
        elif f_value == "车辆指控指令":
            fun = "vehicle-control-command"
        elif f_value == "AI助手":
            fun = "ai-assistant"
        elif f_value == "安全与隐私":
            fun = "security-privacy"

        g_value = sheet[f"G{row}"].value
        labels = []
        if g_value is not None and g_value != "":
            label = str(g_value)
            normalized_string = label.replace("，", ",")
            labels = normalized_string.split(",")
        h_value = sheet[f"H{row}"].value
        i_value = sheet[f"I{row}"].value
        j_value = sheet[f"J{row}"].value
        is_tone = False
        if j_value == "否":
            is_tone = False
        elif j_value == "是":
            is_tone = True

        temp_data = {
            "A": a_value,
            "B": test_type,
            "C": test_sc,
            "D": speaker,
            "E": voice,
            "F": fun,
            "G": labels,
            "H": h_value,
            "I": i_value,
            "J": is_tone,
        }

        res_list.append(temp_data)
    return res_list


def multi_excel(sheet, is_syn):
    # Store the data
    res_list = []
    temp_data = {}
    temp_data["corpus_list"] = []
    corpus_name = ""
    for row in range(2, sheet.max_row + 2):
        a_value = sheet[f"A{row}"].value
        if a_value is None:
            res_list.append(temp_data)
            break
        if corpus_name != a_value:
            if corpus_name != "":
                res_list.append(temp_data)
                if a_value is None:
                    break
                temp_data = {}
                temp_data["corpus_list"] = []
            corpus_name = a_value
            temp_data["A"] = corpus_name
            b_value = sheet[f"B{row}"].value  # 对应车机功能
            car_function = ""
            if b_value == "音视频":
                car_function = "audio-video"
            elif b_value == "导航与出行":
                car_function = "navigation-travel"
            elif b_value == "通讯":
                car_function = "communication"
            elif b_value == "车辆设置与信息查询":
                car_function = "vehicle-settings-info-query"
            elif b_value == "车辆控制指令":
                car_function = "vehicle-control-command"
            elif b_value == "AI助手":
                car_function = "ai-assistant"
            elif b_value == "安全与隐私":
                car_function = "security-privacy"
            temp_data["B"] = car_function
            d_value = sheet[f"D{row}"].value  # 发声人
            e_value = sheet[f"E{row}"].value  # 语种
            speaker_tmp, voice = judge_voice_language(d_value, e_value)
            speaker = ""
            if speaker_tmp == 2:
                speaker = "female"
            elif speaker_tmp == 1:
                speaker = "male"
            else:
                speaker = ""
            temp_data["D"] = speaker
            temp_data["E"] = voice
            f_value = sheet[f"F{row}"].value  # 标签
            labels = []
            if f_value is not None and f_value != "":
                label = str(f_value)
                normalized_string = label.replace("，", ",")
                labels = normalized_string.split(",")
            temp_data["F"] = labels

            if is_syn:
                h_value = sheet[f"H{row}"].value  # 是否添加语气词
                is_tone = False
                if h_value == "是":
                    is_tone = True
                temp_data["H"] = is_tone
        c_value = sheet[f"C{row}"].value  # 语料文本
        g_value = sheet[f"G{row}"].value  # 预期结果
        h_value = sheet[f"H{row}"].value  # 音频路径
        if is_syn:
            corpus_info = {"text": c_value, "expect_result": g_value}
        else:
            corpus_info = {
                "text": c_value,
                "expect_result": g_value,
                "audio_url": h_value,
            }
        temp_data["corpus_list"].append(corpus_info)
    return res_list


def corpus_excel(file_path):
    workbook = openpyxl.load_workbook(file_path)
    # sheet = workbook.active
    sheet = workbook.worksheets[0]

    # Store the data
    res_list = []
    excel_type = ""
    for row in range(1, 2):
        val = sheet[f"A{row}"].value
        if val == "唤醒语料文本":
            excel_type = "rouse"
            res_list = rouse_excel(sheet)
        elif val == "语料文本":
            excel_type = "test"
            res_list = test_excel(sheet)
        elif val == "合成语料名称":
            excel_type = "multi1"
            res_list = multi_excel(sheet, True)
        elif val == "导入语料名称":
            excel_type = "multi2"
            res_list = multi_excel(sheet, False)
        break
    return excel_type, res_list


def close_handle_and_retry(file_path):
    import win32file
    try:
        # 尝试以独占模式打开文件以解除占用
        handle = win32file.CreateFile(
            file_path,
            win32file.GENERIC_READ | win32file.GENERIC_WRITE,
            win32file.FILE_SHARE_DELETE,  # 允许删除操作
            None,
            win32file.OPEN_EXISTING,
            win32file.FILE_ATTRIBUTE_NORMAL,
            None
        )
        handle.Close()  # 关闭句柄后文件可被删除
        os.remove(file_path)
        return 0
    except Exception as e:
        log.error(f"解除占用失败：{file_path}，错误：{e}")
        return -1


def delete_file_by_project_id(project_id):
    video_dir = os.path.join(globalAppSettings.video_dir, project_id)
    try:
        if os.path.exists(video_dir):
            shutil.rmtree(video_dir)
    except PermissionError:  # 捕获权限或占用异常
        # 遍历目录并处理被占用的文件
        rmtree_flag = True
        for root, dirs, files in os.walk(video_dir, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)  # 尝试删除普通文件
                except PermissionError:
                    # 进入强制解除占用流程（见步骤二）
                    ret = close_handle_and_retry(file_path)
                    if ret == 0:
                        os.remove(file_path)
                    else:
                        rmtree_flag = False
        if rmtree_flag:
            shutil.rmtree(video_dir)
    except Exception as e:
        log.info(f"删除项目{project_id=}, video={video_dir} 时发生错误: {e}")

    pic_path = os.path.join(globalAppSettings.photo_dir, project_id)
    if os.path.exists(pic_path):
        shutil.rmtree(pic_path)
    pic_init = os.path.join(globalAppSettings.photo_dir, "init")
    init_jpg = os.path.join(pic_init, project_id + ".jpg")
    if os.path.exists(init_jpg):
        os.remove(init_jpg)
    init_txt = os.path.join(pic_init, project_id + ".txt")
    if os.path.exists(init_txt):
        os.remove(init_txt)

    micaudio_path = os.path.join(globalAppSettings.mic_path, project_id)
    if os.path.exists(micaudio_path):
        shutil.rmtree(micaudio_path)
    return


def get_mac_address():
    mac = uuid.getnode()  # 获取设备的 MAC 地址
    mac_address = ''.join(f'{(mac >> i) & 0xFF:02x}' for i in range(0, 48, 8)[::-1])
    return mac_address.upper()


# 将指定位置转换为二进制表示
def encode_signs(binary_str, sign):
    binary_representation = int(binary_str, 2)
    position = getattr(SignType, sign, None)
    if position:
        # binary_representation |= (1 << (7 - position))  # 7 - position 确保高位对应 a
        mask = (1 << (8 - position))
        result = binary_representation ^ mask
        return bin(result)
    return binary_str


# 解析二进制字符串为标记位置
def decode_signs(binary_string):
    binary_representation = int(binary_string, 2)
    signs = []
    for sign, position in vars(SignType).items():
        if not sign.startswith("__"):  # 排除内置属性
            if binary_representation & (1 << (8 - position)):
                label = ""
                if sign == "rouse_asr_abnormal":
                    label = "唤醒应答异常"
                elif sign == "rouse_time_abnormal":
                    label = "唤醒时间异常"
                elif sign == "result_need_reviewed":
                    label = "需复核结果"
                elif sign == "result_need_attention":
                    label = "重点关注"
                elif sign == "response_time_abnormal":
                    label = "响应时间异常"
                elif sign == "asr_empty":
                    label = "asr输出为空"
                elif sign == "word_accuracy_abnormal":
                    label = "字识别准确率异常"
                elif sign == "manual_review_correction":
                    label = "人工复核修正"
                signs.append(label)
    if len(signs) == 0:
        signs.append("无")
    return signs


def decode_signs_word(sign_label):
    if "唤醒应答异常".startswith(sign_label):
        return "rouse_asr_abnormal"
    elif "唤醒时间异常".startswith(sign_label):
        return "rouse_time_abnormal"
    elif "需复核结果".startswith(sign_label):
        return "result_need_reviewed"
    elif "重点关注".startswith(sign_label):
        return "result_need_attention"
    elif "响应时间异常".startswith(sign_label):
        return "response_time_abnormal"
    elif "asr输出为空".startswith(sign_label):
        return "asr_empty"
    elif "字识别准确率异常".startswith(sign_label):
        return "word_accuracy_abnormal"
    else:
        return None


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, NamedSignalValue):
            return {
                "value": obj.value,
                "name": obj.name
            }
        return super().default(obj)


def dbc_to_json(dbc_file_path):
    # 加载 DBC 文件
    db = cantools.db.load_file(dbc_file_path, encoding='gb2312')

    # 初始化 JSON 数据结构
    json_data = {
        "messages": [],
        "nodes": [],
        "attribute_definitions": {},
        "namespace": {}
    }
    # 检查是否有环境变量属性
    if hasattr(db, 'environment_variables'):
        json_data["environment_variables"] = []

    # 处理属性定义信息
    for attr_def in db._dbc.attribute_definitions.values():
        attr_def_info = {
            "type": attr_def.type_name,
            "minimum": attr_def.minimum,
            "maximum": attr_def.maximum,
            "default": attr_def.default_value,
            "choices": attr_def.choices
        }
        json_data["attribute_definitions"][attr_def.name] = attr_def_info

    # 处理节点信息
    for node in db.nodes:
        node_info = {
            "name": node.name
        }
        if hasattr(node, '_dbc') and hasattr(node._dbc, 'attributes'):
            node_info["attributes"] = {attr.name: attr.value for attr in node._dbc.attributes.values()}
        json_data["nodes"].append(node_info)

    # 处理环境变量信息
    if hasattr(db, 'environment_variables'):
        for env_var in db.environment_variables:
            env_var_info = {
                "name": env_var.name,
                "initial_value": env_var.initial_value,
                "minimum": env_var.minimum,
                "maximum": env_var.maximum,
                "unit": env_var.unit,
                "receivers": env_var.receivers
            }
            if hasattr(env_var, '_dbc') and hasattr(env_var._dbc, 'attributes'):
                env_var_info["attributes"] = {attr.name: attr.value for attr in env_var._dbc.attributes.values()}
            json_data["environment_variables"].append(env_var_info)

    # 处理报文信息
    for message in db.messages:
        message_info = {
            "frame_id": message.frame_id,
            "name": message.name,
            "length": message.length,
            "senders": message.senders
        }
        if hasattr(message, '_dbc') and hasattr(message._dbc, 'attributes'):
            message_info["attributes"] = {attr.name: attr.value for attr in message._dbc.attributes.values()}
        message_info["signals"] = []

        # 处理信号信息
        for signal in message.signals:
            signal_info = {
                "name": signal.name,
                "start": signal.start,
                "length": signal.length,
                "scale": signal.scale,
                "offset": signal.offset,
                "minimum": signal.minimum,
                "maximum": signal.maximum,
                "unit": signal.unit,
                "receivers": signal.receivers,
                "choices": signal.choices,
                "comment": signal.comment
            }
            if hasattr(signal, '_dbc') and hasattr(signal._dbc, 'attributes'):
                signal_info["attributes"] = {attr.name: attr.value for attr in signal._dbc.attributes.values()}
            if hasattr(signal, 'is_little_endian'):
                signal_info["is_little_endian"] = signal.is_little_endian
            if hasattr(signal, 'is_signed'):
                signal_info["is_signed"] = signal.is_signed
            message_info["signals"].append(signal_info)

        json_data["messages"].append(message_info)

    return json_data


def my_recv_callback(msgs):
    for msg in msgs:
        signal_values = msg.get_dbc_data()
        if signal_values is not None:
            data = {
                "recv_time": time.time(),
                "frame_id": msg.can_frame.id,
                # "data":msg.get_data(),
            }
            for signal_name, value in signal_values.items():
                data[f"{signal_name}"] = value
            can_data_queue.put(data)


def save_can_data(start_time, end_time, file_path):
    with open(file_path, 'w') as f:
        while True:
            try:
                data = can_data_queue.get(timeout=1)
                data_str = str(data)
                encode_byte = data_str.encode('gbk', errors='ignore')
                encode_str = encode_byte.decode('gbk', errors='ignore')
                timestamp = data["recv_time"]
                # 判断数据的时间戳是否在指定范围内
                if start_time <= timestamp <= end_time:
                    f.write(str(encode_str) + '\n')
                    f.flush()
                # 如果时间戳超过结束时间，停止保存数据
                elif timestamp > end_time:
                    break
                can_data_queue.task_done()
            except queue.Empty:
                if time.time() > end_time:
                    break
                continue


def check_data(file_path, rules):
    with open(file_path, 'r', encoding='gbk') as file:
        for line in file:
            try:
                # 将每行数据解析为字典
                data = ast.literal_eval(line.strip())
                # 检查当前行是否满足所有规则
                if all(_check_rule(data, rule) for rule in rules):
                    return True
            except (ValueError, SyntaxError):
                # 如果解析失败，跳过该行
                continue
    return False


def _check_rule(data, rule):
    target = rule['title']
    if target not in data:
        return False  # 如果字段不存在，直接返回 False

    # 获取字段的值
    value = data[target]
    # 检查是否满足所有条件
    for condition in rule['values']:
        relation = condition['relation']
        expected_value = condition['value']
        if not _compare(value, relation, expected_value):
            return False
    return True


def _compare(value, relation, expected_value):
    if relation == '>':
        return value > expected_value
    elif relation == '<':
        return value < expected_value
    elif relation == '=':
        return value == expected_value
    elif relation == '>=':
        return value >= expected_value
    elif relation == '<=':
        return value <= expected_value
    elif relation == '!=':
        return value != expected_value
    else:
        raise ValueError(f"Unsupported relation: {relation}")


def extract_title_and_values(input_str):
    # 将输入字符串解析为 Python 对象（列表）
    data = ast.literal_eval(input_str)
    result = [{'title': item['title'], 'values': item['values']} for item in data]
    return result


def judge_can(can_file_url, signal_str):
    rules = extract_title_and_values(signal_str)
    save_path = can_file_url[:-8] + "result.txt"
    can_judge_result = True
    for rule in rules:
        temp_rule_list = []
        temp_rule_list.append(rule)
        can_judge_result = check_data(can_file_url, temp_rule_list, save_path)
        if can_judge_result == False:
            break
    return can_judge_result, save_path


def find_signal_data(file_path, signal_name):
    matched_lines = []  # 用于存储匹配成功的行数据
    with open(file_path, 'r', encoding='gbk') as file:
        for line in file:
            stripped_line = line.strip()
            try:
                data = ast.literal_eval(stripped_line)
                if signal_name not in data:
                    continue
                else:
                    dt = datetime.fromtimestamp(data["recv_time"])
                    time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                    temp = {"time": time_str, "signal_value": data[f"{signal_name}"]}
                    matched_lines.append(temp)
            except (ValueError, SyntaxError):
                continue  # 解析失败的行跳过
    return matched_lines
