from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from datetime import datetime
import os
from typing import List, Optional
from .utils import get_excel_dir


# Base class with default initialization
class BaseModel:
    def __init__(self, **kwargs):
        for field in self.__annotations__:
            setattr(self, field, kwargs.get(field, "-"))

    def items(self):
        """
        Allows iteration like a dictionary.
        Yields key-value pairs for all fields.
        """
        for field in self.__annotations__:
            yield field, getattr(self, field)

    def __getitem__(self, key):
        """
        Makes the object subscriptable.
        Allows access to fields using obj[key].
        """
        if key in self.__annotations__:
            return getattr(self, key)
        raise KeyError(f"{key} is not a valid field name.")

    def __repr__(self):
        """
        String representation for debugging purposes.
        """
        return f"{self.__class__.__name__}({', '.join(f'{k}={v}' for k, v in self.items())})"


# Main Report Class
class Report(BaseModel):
    report_id: str
    report_name: str
    inspection_department: str
    product_name: str
    product_model: str
    entrust_company: str
    sample_name: str
    type_specification: str
    manufacture_company: str
    entrust_date: str
    sample_num: str
    manufacture_date: str
    test_project: str
    test_result_total: str
    project_name: str
    test_item: str  # 检测项目
    test_result_data: List["TestResultData"]
    rouse_test_data: List["RouseTestData"]
    false_rouse_test_data: List["FalseRouseTestData"]
    single_test_data: List["SingleTestData"]
    multi_test_data: List["MultiTestData"]


# Nested data classes
class TestResultData(BaseModel):
    测试方案: str
    测试类型: str
    测试场景: str
    指标: str
    测试结果: str


class RouseTestData(BaseModel):
    唤醒时间: str
    语料文本: str
    判断结果: str
    响应时间: str
    车机响应: str
    # 图片路径: str
    # 录音路径: str


class FalseRouseTestData(BaseModel):
    误唤醒时刻: str
    唤醒响应: str
    # 图片路径: str
    # 录音路径: str


class SingleTestData(BaseModel):
    时间: str
    语料文本: str
    判断结果: str
    响应时间: str
    车机响应: str
    预期结果: str
    字识别率: str
    # ocr图片路径: str
    # 结果图片路径: str
    # 录音路径: str


class MultiTestData(BaseModel):
    时间: str
    语料文本: str
    判断结果: str
    响应时间: str
    车机响应: str
    预期结果: str
    字识别率: str
    # ocr图片路径: str
    # 结果图片路径: str
    # 录音路径: str


# Example Usage
report_data = Report(
    report_id="report_id_demo",
    report_name="report_name",
    inspection_department="inspection_department",
    entrust_date="20241111",
    test_result_data=[
        TestResultData(
            测试方案="方案一", 测试类型="唤醒", 指标="唤醒成功率", 测试结果="100%"
        ),
        TestResultData(
            测试方案="方案二", 测试类型="单次对话", 指标="交互成功率", 测试结果="100%"
        ),
    ],
    rouse_test_data=[
        RouseTestData(
            唤醒时间="2024-12-03 17:52:46.643148",
            语料文本="你好，小p",
            判断结果="通过",
            响应时间="0.7s",
            车机响应="我在",
        )
    ],
    false_rouse_test_data=[
        FalseRouseTestData(
            误唤醒时刻="2024-12-03 17:52:46.643148",
            唤醒响应="11111",
            图片路径="2222",
            录音路径="3333",
        )
    ],
    single_test_data=[
        SingleTestData(
            时间="2024-12-03 17:52:46.643148",
            语料文本="你好，小p",
            判断结果="通过",
            响应时间="0.7s",
            车机响应="我在",
            预期结果="我在",
            字识别率="我在",
        )
    ],
    multi_test_data=[
        MultiTestData(
            时间="AAAAAAA",
            语料文本="AAAAAAA",
            判断结果="AAAAAAA",
            响应时间="0.AAAAAAA",
            车机响应="AAAAAAA",
            预期结果="AAAAAAA",
            字识别率="AAAAAAA",
        )
    ],
)


def set_style(cell):
    for paragraph in cell.paragraphs:
        for run in paragraph.runs:
            # 设置字体大小
            run.font.size = Pt(15)
            # 设置字体颜色（可选）
            run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
            # 设置字体加粗（可选）
            # run.bold = True
        # 设置段落对齐方式
        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER


def get_word_report(file_path, data_to_fill):
    out_path = get_excel_dir()
    directory = os.path.join(out_path, "report")
    if not os.path.exists(directory):
        os.makedirs(directory)
    # directory = os.path.dirname(file_path)
    file_name = data_to_fill.report_name + ".docx"
    output_file = os.path.join(directory, file_name)
    print(directory)
    # 加载文档
    doc = Document(file_path)
    # 获取当前日期和时间
    now = datetime.now()

    # 格式化日期和时间以获取年、月、日
    current_year = now.strftime("%Y")
    current_month = now.strftime("%m")
    current_day = now.strftime("%d")

    date_info = {
        "year": current_year,
        "month": current_month,
        "day": current_day,
    }

    # 替换标注部分
    for paragraph in doc.paragraphs:
        # print(paragraph.text)
        for key, value in data_to_fill.items():
            if isinstance(value, str):
                if f"{{{key}}}" in paragraph.text:
                    paragraph.text = paragraph.text.replace(f"{{{key}}}", value)
                    # 获取段落中的运行对象
                    for run in paragraph.runs:
                        if value in run.text:
                            # 设置字体大小
                            run.font.size = Pt(12)
                            # 设置字体颜色（可选）
                            run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
                            # 设置字体加粗（可选）
                            # run.bold = True
                    # 设置段落的对齐方式（可选）
                    if "报告编号" in paragraph.text:
                        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT
                    else:
                        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

    # 填充附录A检验结果表格
    for table in doc.tables:
        # print( table.cell(0, 0).text)
        if "产品名称" in table.cell(0, 0).text:
            table.cell(0, 1).text = data_to_fill.product_name
            # 设置样式
            set_style(table.cell(0, 1))
            table.cell(1, 1).text = data_to_fill.product_model
            set_style(table.cell(1, 1))
            table.cell(2, 1).text = data_to_fill.entrust_company
            set_style(table.cell(2, 1))
        if "样 品 名 称" in table.cell(0, 0).text:
            table.cell(0, 1).text = data_to_fill.sample_name
            table.cell(1, 1).text = data_to_fill.type_specification
            table.cell(2, 1).text = data_to_fill.entrust_company
            # table.cell(3, 1).text = data_to_fill.entrust_company
            # table.cell(4, 1).text = data_to_fill.sample_num
            # table.cell(6, 1).text = data_to_fill['test_result_total']

            # table.cell(2, 3).text = data_to_fill.manufacture_company
            # table.cell(3, 3).text = data_to_fill.entrust_date
            # table.cell(4, 3).text = data_to_fill.manufacture_date
            table.cell(5, 3).text = data_to_fill.test_item
            # print(table.cell(6, 1).text)
            for row in table.rows:
                for cell in row.cells:
                    # 检查是否包含占位符
                    if (
                        "{year}" in cell.text
                        or "{month}" in cell.text
                        or "{day}" in cell.text
                    ):
                        # 替换占位符
                        cell.text = (
                            cell.text.replace("{year}", date_info["year"])
                            .replace("{month}", date_info["month"])
                            .replace("{day}", date_info["day"])
                        )
                        # 设置段落对齐方式
                        for paragraph in cell.paragraphs:
                            # 设置段落右对齐
                            paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.RIGHT

        # 查找附录A表格
        if "测试方案" in table.cell(0, 0).text:
            if data_to_fill.test_result_data != "-":
                for row_data in data_to_fill.test_result_data:
                    row = table.add_row()
                    for idx, key in enumerate(
                        ["测试方案", "测试类型", "测试场景", "指标", "测试结果"]
                    ):
                        if row_data[key] is not None:
                            row.cells[idx].text = row_data[key]
                        else:
                            row.cells[idx].text = "-"

        if "唤醒时间" in table.cell(0, 0).text:
            if data_to_fill.rouse_test_data != "-":
                for row_data in data_to_fill.rouse_test_data:
                    row = table.add_row()
                    for idx, key in enumerate(
                        ["唤醒时间", "语料文本", "判断结果", "响应时间", "车机响应"]
                    ):
                        if row_data[key] is not None:
                            row.cells[idx].text = row_data[key]
                        else:
                            row.cells[idx].text = "-"

        if "误唤醒时刻" in table.cell(0, 0).text:
            if data_to_fill.false_rouse_test_data != "-":
                for row_data in data_to_fill.false_rouse_test_data:
                    row = table.add_row()
                    for idx, key in enumerate(["误唤醒时刻", "唤醒响应"]):
                        if row_data[key] is not None:
                            row.cells[idx].text = row_data[key]
                        else:
                            row.cells[idx].text = "-"

        if "单次对话时间" in table.cell(0, 0).text:
            if data_to_fill.single_test_data != "-":
                for row_data in data_to_fill.single_test_data:
                    row = table.add_row()
                    for idx, key in enumerate(
                        [
                            "时间",
                            "语料文本",
                            "判断结果",
                            "响应时间",
                            "车机响应",
                            "预期结果",
                            "字识别率",
                        ]
                    ):
                        if row_data[key] is not None:
                            row.cells[idx].text = row_data[key]
                        else:
                            row.cells[idx].text = "-"

        if "连续对话时间" in table.cell(0, 0).text:
            if data_to_fill.multi_test_data != "-":
                for row_data in data_to_fill.multi_test_data:
                    row = table.add_row()
                    for idx, key in enumerate(
                        [
                            "时间",
                            "语料文本",
                            "判断结果",
                            "响应时间",
                            "车机响应",
                            "预期结果",
                            "字识别率",
                        ]
                    ):
                        if row_data[key] is not None:
                            row.cells[idx].text = row_data[key]
                        else:
                            row.cells[idx].text = "-"

    for section in doc.sections:
        # 获取页眉对象
        header = section.header
        # 页眉通常包含段落，我们可以遍历这些段落并进行修改
        for paragraph in header.paragraphs:
            # print(paragraph.text)
            for key, value in data_to_fill.items():
                if isinstance(value, str):
                    if f"{{{key}}}" in paragraph.text:
                        paragraph.text = paragraph.text.replace(f"{{{key}}}", value)

    # 保存文档
    # output_path = '/Users/<USER>/Desktop/zhaoshang/report/demo_filled.docx'
    doc.save(output_file)

    # print(f"文档已保存到 {output_file}")
    return output_file


# file_path = '/Users/<USER>/Desktop/zhaoshang/report/demo2.docx'

# get_word_report(file_path, report_data)
