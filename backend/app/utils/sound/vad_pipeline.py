
import librosa
from silero_vad import read_audio, get_speech_timestamps
import numpy as np
# from utils import moving_average, replace_outliers
import plotly.graph_objects as go
import noisereduce as nr  # type: ignore
from app.middleware.log import logger as log
from scipy.ndimage import maximum_filter
from scipy.signal import savgol_filter
from skimage.filters import threshold_otsu
from io import BytesIO
import soundfile as sf
from silero_vad import load_silero_vad
from time import sleep

SAMPLE_RATE = 16000
BACKWARD_SILENCE_INTERVAL = 12800  # 800 ms
BW_ABOVE_SILENCE_FRAMES_WINDOW = 1280 - 60  # 1600-60  # 100 ms 有声音的时长
FW_ABOVE_SILENCE_FRAMES_WINDOW = 1280 - 60  # 1600-60  # 100 ms 有声音的时长
LOCAL_GLOBAL_NOISE_RATIO_END_THRESHOLD = 2.0
NOISE_SPLIT_RATIO_THRESHOLD = 0.1
IGNORE_NOISE_BEFORE_SECONDS = 0.08
DYNAMIC_NOISE_TO_AVERAGE_THRESHOLD = 0.1
DEFAULT_NOISE_LEVEL = 0.004
START_SEARCH_BEFORE_MS = 800  # Start searching for voice no earlier than this (relative to VAD start time)
MINIMUM_SPEECH_PAUSE_REPORT = 0.55  # Discard pauses shorter than this when reporting final result in pipeline.stats()
IGNORE_CONTRIBUTION_BEFORE_VAD_START = 2400  # = 150 ms Don't use possible start voice for local noise correction for this time before vad start
TOO_LOW_NOISE = 0.0001


def moving_average(array, window=60):
    # Use np.convolve for efficient moving average calculation
    return np.convolve(np.abs(array), np.ones(window)/window, mode='same')


def rolling_split(array):
    max_diff = 0
    max_index = 0
    result = np.zeros_like(array)
    for i in range(1, array.size):
        left, right = np.split(array, [i])
        diff = np.abs(left.mean() - right.mean())
        # log.info(f'{i} {diff = } {left.mean() = } {right.mean() = }')
        if diff > max_diff:
            max_diff = diff
            max_index = i
            result[:i] = left.mean()
            result[i:] = right.mean()
    return result, max_index


def outliers_replacer_step(array, nstd=2):
    lw = array.mean() - nstd * array.std()
    hi = array.mean() + nstd * array.std()
    filtered_data = array[(array >= lw) & (array <= hi)]
    return filtered_data.size, filtered_data


def outliers_replacer(array, nstd=2):
    array = np.array(array)
    original_array = array.copy()
    last_size = array.size
    new_size = -1
    # log.info(f'{original_array=}')
    while True:
        new_size, array = outliers_replacer_step(array, nstd)
        # log.info(f'{new_size=} {array=}')
        if last_size == new_size:
            break
        else:
            last_size = new_size
    final_mean = array.mean()
    final_max = array.max()
    final_min = array.min()
    original_array = np.where(original_array > final_max, final_mean, original_array)
    # original_array = np.where(original_array < final_min, final_mean, original_array)
    return original_array


class IntermediateSegment:
    def __init__(self, index, speech_timestamp_before, speech_timestamp_after, mva, sound):
        if speech_timestamp_before is not None:
            self.start_frame = speech_timestamp_before["end"]
        else:
            self.start_frame = 0
        if speech_timestamp_after is not None:
            self.end_frame = speech_timestamp_after["start"]
        else:
            self.end_frame = mva.size

        self.local_noise = np.abs(sound[self.start_frame:self.end_frame]).mean()
        self.above_global_noise_level = np.nan
        self.index = index
        self.segment_mva_level = mva[self.start_frame:self.end_frame]
        self.segment_sound_level = sound[self.start_frame:self.end_frame]

        self.separated_level_noise, separation_index = rolling_split(self.segment_mva_level)
        self.separation_frame = self.start_frame + separation_index
        separated_noise_levels, weights = np.unique(self.separated_level_noise, return_counts=True)
        self.two_noise_weights = weights / weights.sum()
        try:
            self.two_noise_ratio = min(weights)/max(weights)
            # log.info(f'{self.index=} {self.two_noise_ratio=}')
            if max(separated_noise_levels) < self.local_noise * DYNAMIC_NOISE_TO_AVERAGE_THRESHOLD:
                self.two_noise_ratio = 0
        except ValueError:
            self.two_noise_ratio = 0

        # log.info(f'Segment {index} weight {self.two_noise_weights} levels = {separated_noise_levels} ratio = {self.two_noise_ratio}')
        self.separated_noise_levels = np.unique(self.separated_level_noise)

    def data_for_arrays(self, integrated_noise, rolling_split_noise):
        if self.is_empty_segment:
            return
        # integrated_segment = integrate_array(self.segment_mva_level)
        # integrated_noise[self.start_frame:self.end_frame] = integrated_segment
        rolling_split_noise[self.start_frame:self.end_frame] = self.separated_level_noise

    @property
    def is_double_noise(self):
        return self.two_noise_ratio > NOISE_SPLIT_RATIO_THRESHOLD

    @property
    def is_empty_segment(self):
        return self.start_frame == self.end_frame

    @property
    def next_voice_segment_index(self):
        return self.index

    @property
    def previous_voice_segment_index(self):
        return self.index - 1

    @property
    def start_time(self):
        return self.start_frame / SAMPLE_RATE

    @property
    def end_time(self):
        return self.end_frame / SAMPLE_RATE


class VoiceSegment:
    def __init__(self, index, speech_timestamp, mva, sound):
        self.index = index
        self.start_frame = speech_timestamp["start"]
        self.end_frame = speech_timestamp["end"]
        self.segment_mva_level = mva[self.start_frame:self.end_frame]
        self.segment_sound_level = sound[self.start_frame:self.end_frame]
        self.start_local_correction = 0
        self.start_global_correction = 0
        self.end_local_correction = 0
        self.end_global_correction = 0
        self.chosen_start_correction = None
        self.chosen_end_correction = None
        self.chosen_dynamic_start_correction = None
        self.chosen_dynamic_end_correction = None

    def find_correction(self, intermediate_segments, direction, global_noise_average):
        if direction == 'from_start':
            prev_noise_index = self.previous_intermediate_segment_index
            intermediate_segment = intermediate_segments.get(prev_noise_index, None)
            if intermediate_segment is None or intermediate_segment.is_empty_segment:
                # log.info(f'{self.index=} {direction=} Setting default correction to local (empty segment)')
                self.chosen_start_correction = 'local'
                self.start_local_correction = intermediate_segment.start_frame
                self.start_global_correction = intermediate_segment.start_frame
                return
            local_mva = np.hstack((intermediate_segment.segment_mva_level, self.segment_mva_level))

        elif direction == 'from_end':
            next_noise_index = self.next_intermediate_segment_index
            intermediate_segment = intermediate_segments.get(next_noise_index, None)
            if intermediate_segment is None or intermediate_segment.is_empty_segment:
                self.chosen_end_correction = 'local'
                self.end_local_correction = intermediate_segment.end_frame
                self.end_global_correction = intermediate_segment.end_frame
                return
            local_mva = np.hstack((self.segment_mva_level, intermediate_segment.segment_mva_level))

        search_start = max(int(intermediate_segment.segment_mva_level.size - START_SEARCH_BEFORE_MS * SAMPLE_RATE / 1000), 0)
        # fig = go.Figure()

        local_noise_difference = np.sign(local_mva - np.ones_like(local_mva) * intermediate_segment.local_noise)
        # if not np.isnan(intermediate_segment.above_global_noise_level):
        #     above_global_noise_difference = np.sign(local_mva - np.ones_like(local_mva) * intermediate_segment.above_global_noise_level)
        global_noise_difference = np.sign(local_mva - np.ones_like(local_mva) * global_noise_average)

        # fig.add_trace(go.Scatter(x=np.arange(local_mva.size)/SAMPLE_RATE, y=local_mva, name='local_mva'))
        # fig.add_trace(go.Scatter(x=np.arange(local_mva.size)/SAMPLE_RATE, y=np.ones_like(local_mva) * global_noise_average, name='global_noise'))
        # fig.add_trace(go.Scatter(x=np.arange(local_mva.size)/SAMPLE_RATE, y=np.ones_like(local_mva) * intermediate_segment.local_noise, name='local_noise'))
        # fig.add_trace(go.Scatter(x=np.arange(local_mva.size)/SAMPLE_RATE, y=local_noise_difference, name='local_noise_difference'))
        # fig.add_trace(go.Scatter(x=np.arange(local_mva.size)/SAMPLE_RATE, y=global_noise_difference, name='global_noise_difference'))
        # fig.show()

        level_change_detected_local = False
        level_change_detected_global = False

        local_correction = None
        global_correction = None

        strategy = {}

        if intermediate_segment.is_double_noise:
            # Implement additional logic for double noise segments

            level_change_detected_dynamic_low = False
            level_change_detected_dynamic_high = False
            dynamic_low_correction = None
            dynamic_high_correction = None

            separated_level_noise = intermediate_segment.separated_level_noise

            dynamic_low_level = min(separated_level_noise[0], separated_level_noise[-1])
            dynamic_high_level = max(separated_level_noise[0], separated_level_noise[-1])

            dynamic_low_difference = np.sign(local_mva - np.ones_like(local_mva) * dynamic_low_level)
            dynamic_high_difference = np.sign(local_mva - np.ones_like(local_mva) * dynamic_high_level)

            if direction == 'from_end':
                separated_level_noise = separated_level_noise[::-1]
            closest_length_frames = np.isclose(separated_level_noise, separated_level_noise[-1]).sum()
            closest_length_seconds = closest_length_frames / SAMPLE_RATE

            if direction == 'from_start':
                if separated_level_noise[-1] > separated_level_noise[0] and closest_length_seconds > IGNORE_NOISE_BEFORE_SECONDS:
                    log.info(f'{self.index} start: Noise over 80 ms, prefer high dynamic correction ({direction})')
                    strategy['prefer_dynamic_high_correction'] = True
                elif separated_level_noise[-1] > separated_level_noise[0] and closest_length_seconds <= IGNORE_NOISE_BEFORE_SECONDS:
                    log.info(f'{self.index} start: Noise under 80 ms, prefer local correction ({direction})')
                    strategy['prefer_local_correction'] = True

                if separated_level_noise[-1] < separated_level_noise[0] and closest_length_seconds > IGNORE_NOISE_BEFORE_SECONDS:
                    log.info(f'{self.index} start: Closest noise low and over 80 ms, prefer low dynamic correction ({direction})')
                    strategy['prefer_dynamic_low_correction'] = True
                elif separated_level_noise[-1] < separated_level_noise[0] and closest_length_seconds <= IGNORE_NOISE_BEFORE_SECONDS:
                    log.info(f'{self.index} start: Closest noise low and under 80 ms, prefer local correction ({direction})')
                    strategy['prefer_local_correction'] = True

        else:
            strategy['prefer_local_correction'] = True
            # if direction == 'from_start':
            #     strategy['prefer_local_correction'] = True
            # else:
            #     strategy['prefer_global_correction'] = True

        if direction == 'from_end':
            search_start = intermediate_segment.segment_mva_level.size
            local_noise_difference = local_noise_difference[::-1]
            global_noise_difference = global_noise_difference[::-1]
            if intermediate_segment.is_double_noise:
                dynamic_low_difference = dynamic_low_difference[::-1]
                dynamic_high_difference = dynamic_high_difference[::-1]

        for moving_index in range(search_start, local_mva.size):
            if moving_index + 1 < local_mva.size:
                sign_head_local = np.sign(local_noise_difference[moving_index])
                sign_tail_local = np.sign(local_noise_difference[moving_index+1])
                sign_head_global = np.sign(global_noise_difference[moving_index])
                sign_tail_global = np.sign(global_noise_difference[moving_index+1])

                if intermediate_segment.is_double_noise:
                    sign_head_dynamic_low = np.sign(dynamic_low_difference[moving_index])
                    sign_tail_dynamic_low = np.sign(dynamic_low_difference[moving_index+1])
                    sign_head_dynamic_high = np.sign(dynamic_high_difference[moving_index])
                    sign_tail_dynamic_high = np.sign(dynamic_high_difference[moving_index+1])

                    level_change_detected_dynamic_low = sign_head_dynamic_low < 0 and sign_tail_dynamic_low > 0 or level_change_detected_dynamic_low
                    if sign_head_dynamic_low > 0 and sign_tail_dynamic_low < 0:
                        level_change_detected_dynamic_low = False

                    level_change_detected_dynamic_high = sign_head_dynamic_high < 0 and sign_tail_dynamic_high > 0 or level_change_detected_dynamic_high
                    if sign_head_dynamic_high > 0 and sign_tail_dynamic_high < 0:
                        level_change_detected_dynamic_high = False

                    if level_change_detected_dynamic_low and dynamic_low_correction is None:
                        if dynamic_low_difference[moving_index:moving_index+BW_ABOVE_SILENCE_FRAMES_WINDOW].sum() == BW_ABOVE_SILENCE_FRAMES_WINDOW:
                            dynamic_low_correction = moving_index

                    if level_change_detected_dynamic_high and dynamic_high_correction is None:
                        if dynamic_high_difference[moving_index:moving_index+BW_ABOVE_SILENCE_FRAMES_WINDOW].sum() == BW_ABOVE_SILENCE_FRAMES_WINDOW:
                            dynamic_high_correction = moving_index
                            # log.info(f'{dynamic_high_correction=}')

            level_change_detected_local = sign_head_local < 0 and sign_tail_local > 0 or level_change_detected_local
            if sign_head_local > 0 and sign_tail_local < 0:
                level_change_detected_local = False

            level_change_detected_global = sign_head_global < 0 and sign_tail_global > 0 or level_change_detected_global
            if sign_head_global > 0 and sign_tail_global < 0:
                level_change_detected_global = False

            if level_change_detected_local and local_correction is None:
                if local_noise_difference[moving_index:moving_index+BW_ABOVE_SILENCE_FRAMES_WINDOW].sum() == BW_ABOVE_SILENCE_FRAMES_WINDOW:
                    local_correction = moving_index

            if level_change_detected_global and global_correction is None:
                if global_noise_difference[moving_index:moving_index+BW_ABOVE_SILENCE_FRAMES_WINDOW].sum() == BW_ABOVE_SILENCE_FRAMES_WINDOW:
                    global_correction = moving_index

        if direction == 'from_start':
            if local_correction is None:
                local_correction = 0
            if global_correction is None:
                global_correction = 0
            if intermediate_segment.is_double_noise:
                if dynamic_low_correction is None:
                    dynamic_low_correction = 0
                if dynamic_high_correction is None:
                    dynamic_high_correction = 0

            self.start_local_correction = intermediate_segment.start_frame + local_correction
            self.start_global_correction = intermediate_segment.start_frame + global_correction
            if intermediate_segment.is_double_noise:
                self.start_dynamic_low_correction = intermediate_segment.start_frame + dynamic_low_correction
                self.start_dynamic_high_correction = intermediate_segment.start_frame + dynamic_high_correction

            # log.info(f'Local start correction: {self.start_local_correction/SAMPLE_RATE} Global start correction: {self.start_global_correction/SAMPLE_RATE}')
            if strategy.get('prefer_local_correction', False):
                self.chosen_start_correction = 'local'
            if strategy.get('prefer_global_correction', False):
                self.chosen_start_correction = 'global'

        elif direction == 'from_end':
            if local_correction is None:
                local_correction = 0
            if global_correction is None:
                global_correction = 0
            if intermediate_segment.is_double_noise:
                if dynamic_low_correction is None:
                    dynamic_low_correction = 0
                if dynamic_high_correction is None:
                    dynamic_high_correction = 0

            self.end_local_correction = intermediate_segment.end_frame - local_correction
            self.end_global_correction = intermediate_segment.end_frame - global_correction

            if intermediate_segment.is_double_noise:
                self.end_dynamic_low_correction = intermediate_segment.end_frame - dynamic_low_correction
                self.end_dynamic_high_correction = intermediate_segment.end_frame - dynamic_high_correction

            # log.info(f'Local end correction: {self.end_local_correction/SAMPLE_RATE} Global end correction: {self.end_global_correction/SAMPLE_RATE}')

            if strategy.get('prefer_local_correction', False):
                self.chosen_end_correction = 'local'
            if strategy.get('prefer_global_correction', False):
                self.chosen_end_correction = 'global'

        if direction == 'from_start':
            if intermediate_segment.local_noise <= global_noise_average:
                self.chosen_start_correction = 'global'
                log.info(f'{self.index} start: Chosing global noise because its above local')
                # if self.start_global_correction > self.start_local_correction:
                #     self.chosen_start_correction = 'global'
                # else:
                #     self.chosen_start_correction = 'global'

                # if self.end_global_correction < self.end_local_correction:
                #     log.info(f'{self.index} start: Chosing global noise because it is earlier')
                #     self.chosen_start_correction = 'global'
                # else:
                #     log.info(f'{self.index} start: Chosing local noise because it is earlier')
                #     self.chosen_start_correction = 'local'

        if direction == 'from_end':
            if intermediate_segment.local_noise <= global_noise_average:
                log.info(f'{self.index} end: Chosing global noise because its above local')
                self.chosen_end_correction = 'global'
            else:
                log.info(f'{self.index} end: Chosing local noise because its above global')
                self.chosen_end_correction = 'local'

        if intermediate_segment.is_double_noise:
            if dynamic_high_level < global_noise_average and separated_level_noise[-1] > separated_level_noise[0]:
                log.info(f'{self.index} {direction} Enforcing global correction because dynamic level too low')
                self.chosen_dynamic_start_correction = None
                strategy['prefer_dynamic_low_correction'] = False
                strategy['prefer_dynamic_high_correction'] = False
                self.chosen_start_correction = 'global'

            if direction == 'from_start':
                if strategy.get('prefer_dynamic_low_correction', False):
                    self.chosen_dynamic_start_correction = self.start_dynamic_low_correction
                elif strategy.get('prefer_dynamic_high_correction', False):
                    self.chosen_dynamic_start_correction = self.start_dynamic_high_correction
            elif direction == 'from_end':
                if strategy.get('prefer_dynamic_low_correction', False):
                    self.chosen_dynamic_end_correction = self.end_dynamic_low_correction
                elif strategy.get('prefer_dynamic_high_correction', False):
                    self.chosen_dynamic_end_correction = self.end_dynamic_high_correction

        if self.chosen_start_correction is None:
            log.info(f'{self.index=} {direction=} Default start correction: local')
            self.chosen_start_correction = 'local'

        if self.chosen_end_correction is None:
            log.info(f'{self.index=} {direction=} Default end correction: local')
            self.chosen_end_correction = 'local'

        log.info(f'{self.index=} {direction=} {strategy=} {self.chosen_start_correction=}')
    # NG >= NL -> TG
    # NG < NL -> TL

    @property
    def start_time_local_corrected(self):
        return (self.start_time + self.start_local_correction)/SAMPLE_RATE

    @property
    def start_time_global_corrected(self):
        return (self.start_time + self.start_global_correction)/SAMPLE_RATE

    @property
    def end_time_local_corrected(self):
        return (self.end_time + self.end_local_correction)/SAMPLE_RATE

    @property
    def end_time_global_corrected(self):
        return (self.end_time + self.end_global_correction)/SAMPLE_RATE

    @property
    def start_time_dynamic_corrected(self):
        return (self.start_time + self.chosen_dynamic_start_correction)/SAMPLE_RATE

    @property
    def end_time_dynamic_corrected(self):
        return (self.end_time + self.chosen_dynamic_end_correction)/SAMPLE_RATE

    @property
    def next_intermediate_segment_index(self):
        return self.index + 1

    @property
    def previous_intermediate_segment_index(self):
        return self.index

    @property
    def start_time(self):
        return self.start_frame / SAMPLE_RATE

    @property
    def end_time(self):
        return self.end_frame / SAMPLE_RATE


class NoSegmentsFound(Exception):
    pass


class TTSPipeline:
    def __init__(self, **kwargs) -> None:
        RESPONSE_TOO_LATE = int(3 * SAMPLE_RATE)

        filename_full = kwargs["filename_full"]
        filename_endpart = kwargs["filename_endpart"]
        model = kwargs["model"]

        full_original_audio, sample_rate = librosa.load(filename_full, sr=SAMPLE_RATE)
        self.full_original_audio = full_original_audio
        endpart_original_audio, sample_rate = librosa.load(filename_endpart, sr=SAMPLE_RATE)

        firstpast_frame_size = full_original_audio.size - endpart_original_audio.size

        endpart_noise_reduced = nr.reduce_noise(y=endpart_original_audio, sr=sample_rate, stationary=True)
        full_noise_reduced = nr.reduce_noise(y=full_original_audio, sr=sample_rate, stationary=True)

        silero_wav = read_audio(filename_endpart)
        speech_timestamps = get_speech_timestamps(silero_wav,
                                                  model,
                                                  threshold=0.22,
                                                  min_speech_duration_ms=96,
                                                  min_silence_duration_ms=96,  # 192 为唤醒相应时间, 160 刚改成96 - 2025.1.22
                                                  speech_pad_ms=0,
                                                  sampling_rate=SAMPLE_RATE,
                                                  return_seconds=False,  # Return speech timestamps in seconds (default is samples)
                                                  )

        # we take silence segment from partial end file based on VAD timestamps
        if len(speech_timestamps) == 0:
            raise IndexError
        elif len(speech_timestamps) >= 1:
            self.vad_novoice_points = [firstpast_frame_size, firstpast_frame_size + speech_timestamps[0]["start"]]
        else:
            if speech_timestamps[0]["end"] > RESPONSE_TOO_LATE:
                self.vad_novoice_points = [firstpast_frame_size, firstpast_frame_size + speech_timestamps[0]["start"]]
            else:
                self.vad_novoice_points = [firstpast_frame_size + speech_timestamps[0]["end"], firstpast_frame_size + speech_timestamps[1]["start"]]

        # 20% of silence interval (centered on middle) used to pick noise level
        noise_interval_middle = int((self.vad_novoice_points[0] + self.vad_novoice_points[1])//2)
        noise_interval_half_length_20percent = int((self.vad_novoice_points[1] - self.vad_novoice_points[0])//10)
        noise_threshold = np.abs(full_original_audio[noise_interval_middle-noise_interval_half_length_20percent:noise_interval_middle+noise_interval_half_length_20percent]).max()

        # print(f'{noise_threshold=}')

        self.time_scale = np.arange(full_original_audio.size)/SAMPLE_RATE
        self.full_mva = moving_average(full_original_audio, window=60)
        forward_mva = self.full_mva.copy()

        # do noise reduction on awake part, starting time put to 0 to avoid bugs from input file with bad cut timings
        reduced_forward_mva = moving_average(endpart_noise_reduced, window=60)
        forward_mva[-reduced_forward_mva.size:] = reduced_forward_mva
        forward_mva[:noise_interval_middle] = 0.0

        # argmax stops at first "True"
        self.end_time = np.argmax(forward_mva > 0.5 * noise_threshold) / SAMPLE_RATE

        # now same for start segment (except noise reduction), but we need reverse array to use argmax
        backward_mva = self.full_mva.copy()
        # backward_mva = moving_average(full_noise_reduced, window=60)
        backward_mva[noise_interval_middle:] = 0.0
        reversed_backward_mva = backward_mva[::-1]
        reversed_start_frame = np.argmax(reversed_backward_mva > 2 * noise_threshold)
        # self.start_time = (full_original_audio.size - endpart_original_audio.size) / SAMPLE_RATE
        self.start_time = (full_original_audio.size - reversed_start_frame) / SAMPLE_RATE
        self.final_timedelta = self.end_time - self.start_time
        if self.final_timedelta < 0 and len(self.vad_novoice_points) > 1:
            # This can happen when non-human hearable noise appears just before response.
            # In this case we can rely on VAD to find response start and recalculate noise level
            # for the first part of the file.

            # 20% of silence interval (centered on beginning) used to pick noise level
            noise_interval_start = self.vad_novoice_points[0]
            noise_interval_half_length_10percent = int((self.vad_novoice_points[1] - self.vad_novoice_points[0])//5)
            noise_threshold_fallback = np.abs(full_original_audio[noise_interval_start-noise_interval_half_length_10percent:noise_interval_start]).max()

            backward_mva = self.full_mva.copy()
            backward_mva[noise_interval_middle-noise_interval_half_length_10percent:] = 0.0
            reversed_backward_mva = backward_mva[::-1]
            reversed_start_frame = np.argmax(reversed_backward_mva > 2 * noise_threshold_fallback)

            self.start_time = (full_original_audio.size - reversed_start_frame) / SAMPLE_RATE

            # self.start_time = self.vad_novoice_points[0]/SAMPLE_RATE
            self.end_time = self.vad_novoice_points[1]/SAMPLE_RATE
            self.final_timedelta = self.end_time - self.start_time


class TTSPipelineExperimental:
    def __init__(self, **kwargs) -> None:
        def response_begin_calculation(
                start_frame: int,
                CHARACTERISTIC_TIMESCALE_FRAMES: int,
                CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE: float,
                RESPONSE_ENVELOP_THRESHOLD: float,
                contrast_voice_feature: np.array,
                envelope: np.array
        ) -> dict:
            response_anchor_frame = start_frame + np.argmax((contrast_voice_feature[start_frame:] == 1) * (envelope[start_frame:] > RESPONSE_ENVELOP_THRESHOLD))

            if start_frame >= response_anchor_frame:
                response_anchor_frame = start_frame + CHARACTERISTIC_TIMESCALE_FRAMES
            response_min_envelope_value = envelope[start_frame:response_anchor_frame].min()
            response_envelop_correction_frame = response_anchor_frame - np.argmax(envelope[start_frame:response_anchor_frame][::-1] < CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE * response_min_envelope_value)

            # and this is for case jump was too far
            try:
                response_envelop_correction_frame += np.where(np.diff(np.sign(np.diff(envelope[response_envelop_correction_frame:response_anchor_frame]))) == 2)[0][-1] + 1
            except IndexError:
                pass

            response_begin_time = response_envelop_correction_frame/SAMPLE_RATE
            return {'response_begin_time': response_begin_time, 'response_anchor_frame': response_anchor_frame, 'response_anchor_frame': response_anchor_frame, 'response_envelop_correction_frame': response_envelop_correction_frame}


        def speech_end_calculation(
                start_frame: int,
                ONSET_ENV_METRICS_THRESHOLD: float,
                CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH: float,
                onset_env_metrics: np.array,
                f0_interp: np.array,
                envelope: np.array
        ) -> dict:

            speech_by_level_frame = start_frame - np.argmax((onset_env_metrics[:start_frame])[::-1] > ONSET_ENV_METRICS_THRESHOLD)
            speech_by_level = speech_by_level_frame / SAMPLE_RATE

            if not np.isnan(f0_interp[speech_by_level_frame]):
                speech_correction_frame = np.argmax(np.isnan(f0_interp[speech_by_level_frame:])) + speech_by_level_frame
                speech_correction = speech_correction_frame / SAMPLE_RATE
            else:
                speech_correction = False

            force_level_value = False
            if speech_correction:
                if speech_correction_frame >= start_frame:
                    force_level_value = True  # for short times or false negative values
                    speech_envelope_correction_frame = speech_correction_frame
                else:
                    speech_min_envelope_value = envelope[speech_correction_frame:start_frame].min()
                    speech_envelope_correction_frame = speech_correction_frame + np.argmax(envelope[speech_correction_frame:] < CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH * speech_min_envelope_value)
            else:
                if speech_by_level_frame >= start_frame:
                    speech_envelope_correction_frame = speech_by_level_frame
                else:
                    speech_min_envelope_value = envelope[speech_by_level_frame:start_frame].min()
                    speech_envelope_correction_frame = speech_by_level_frame + np.argmax(envelope[speech_by_level_frame:] < CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH * speech_min_envelope_value)
            speech_envelope_correction = speech_envelope_correction_frame / SAMPLE_RATE
            
            if force_level_value:
                speech_envelope_correction = speech_by_level
            speech_end_time = max(speech_by_level, speech_envelope_correction)
            return {'speech_end_time': speech_end_time, 'speech_by_level': speech_by_level, 'speech_envelope_correction': speech_envelope_correction}

        def normalize(arr, amin=-1, amax=1):
            return amin + (amax-amin) * (arr - arr.min()) / (arr.max() - arr.min())

        def estimate_envelope(
            x: np.ndarray,
            maximum_filter_window_size: int,
            savgol_filter_window_size: int,
            savgol_filter_polyorder: int,
        ) -> np.ndarray:

            if maximum_filter_window_size % 2 == 0:
                maximum_filter_window_size += 1

            if savgol_filter_window_size % 2 == 0:
                savgol_filter_window_size += 1

            envelope = np.abs(x)
            envelope = maximum_filter(envelope, size=maximum_filter_window_size)
            return savgol_filter(
                envelope,
                window_length=savgol_filter_window_size,
                polyorder=savgol_filter_polyorder,
            )

        def interpolate_to_size(arr, size):
            return np.interp(
                np.arange(size)/SAMPLE_RATE,  # at frames
                np.linspace(0, size/SAMPLE_RATE, arr.shape[0]),  # available x
                arr  # values
            )

        CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH = 3.18
        CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE = 4.46
        RESPONSE_ENVELOP_THRESHOLD = 0.45
        ONSET_ENV_METRICS_THRESHOLD = 0.007
        ENVEL_MAXIMUM_FILTER_WINDOW_SIZE = 629
        ENVEL_SAVGOL_FILTER_WINDOW_SIZE = 9
        ENVEL_AVGOL_FILTER_POLYORDER = 0
        CHARACTERISTIC_TIMESCALE_FRAMES = 1486  # corresponds to 93ms with sample rate 16000Hz, recommended by librosa

        filename_full = kwargs["filename_full"]
        filename_part = kwargs["filename_endpart"]
        self.full_original_audio, _ = librosa.load(filename_full, sr=SAMPLE_RATE)
        full_original_audio = self.full_original_audio
        self.time_scale = np.arange(full_original_audio.size)/SAMPLE_RATE
        self.full_mva = moving_average(full_original_audio, window=60)
        filename_part = filename_full.replace('_full', '')
        part_audio, _ = librosa.load(filename_part, sr=SAMPLE_RATE)

        start_frame = full_original_audio.size - part_audio.size

        full_original_audio[:start_frame] = normalize(full_original_audio[:start_frame])
        full_original_audio[start_frame:] = normalize(full_original_audio[start_frame:])

        S = np.abs(librosa.stft(full_original_audio, n_fft=CHARACTERISTIC_TIMESCALE_FRAMES))  # changed default value 2048 to 1486 because sample rate is 16000 instead of 22050
        contrast = librosa.feature.spectral_contrast(S=S, sr=SAMPLE_RATE, n_fft=CHARACTERISTIC_TIMESCALE_FRAMES)
        feature_contrast = contrast.sum(axis=0)
        threshold = threshold_otsu(feature_contrast)
        voice_labels = feature_contrast >= threshold
        voice = voice_labels.astype(int)

        voice = np.hstack((voice[:-1] * voice[1:], voice[-1]))  # remove too short segments

        contrast_voice_feature = interpolate_to_size(voice, full_original_audio.size)

        envelope = estimate_envelope(
            x=np.abs(full_original_audio),
            maximum_filter_window_size=ENVEL_MAXIMUM_FILTER_WINDOW_SIZE,  # from experimenting a bit
            savgol_filter_window_size=ENVEL_SAVGOL_FILTER_WINDOW_SIZE,  # from experimenting a bit
            savgol_filter_polyorder=ENVEL_AVGOL_FILTER_POLYORDER,  # from experimenting a bit
        )

        f0, _, voicing_probability = librosa.pyin(y=full_original_audio, sr=SAMPLE_RATE, frame_length=CHARACTERISTIC_TIMESCALE_FRAMES, fmin=65, fmax=2094)
        """
        f0_split = []
        voiceprob_split = []
        for chunk in np.array_split(full_original_audio, 16):
            f0_chunk, _, voicing_probability_chunk = librosa.pyin(y=full_original_audio, sr=SAMPLE_RATE, frame_length=1024, fmin=65, fmax=2094)
            if len(f0_split):
                medium_point = 0.5*(f0_split[-1][-1]+f0_chunk[0])
                f0_split.append([medium_point])
            f0_split.append(f0_chunk)
    
            if len(voiceprob_split):
                medium_point = 0.5*(voiceprob_split[-1][-1]+voiceprob_split[0])
                voiceprob_split.append([medium_point])
            voiceprob_split.append(voicing_probability_chunk)
    
        f0 = np.hstack(f0_split)
        voicing_probability = np.hstack([voiceprob_split])
        """
        f0_interp = np.interp(  # interpolation of f0 curve (less than frames)
            np.arange(full_original_audio.size)/SAMPLE_RATE,  # at frames
            np.linspace(0, full_original_audio.size/SAMPLE_RATE, f0.shape[0]),  # available x
            f0  # values
        )
        voicing_probability_interp = np.interp(  # interpolation of f0 curve (less than frames)
            np.arange(full_original_audio.size)/SAMPLE_RATE,  # at frames
            np.linspace(0, full_original_audio.size/SAMPLE_RATE, voicing_probability.shape[0]),  # available x
            voicing_probability  # values
        )

        onset_env = librosa.onset.onset_strength(y=full_original_audio, sr=SAMPLE_RATE,
                                                aggregate=np.median,
                                                fmax=4000, n_mels=256)
        onset_env = normalize(interpolate_to_size(onset_env, full_original_audio.size), 0, 1)
        onset_env_metrics = normalize(voicing_probability_interp * onset_env, 0, 1)

        response_begin_time_data = response_begin_calculation(start_frame, CHARACTERISTIC_TIMESCALE_FRAMES, CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE,
                                                            RESPONSE_ENVELOP_THRESHOLD, contrast_voice_feature, envelope)
        response_begin_time = response_begin_time_data["response_begin_time"]
        speech_end_time_data = speech_end_calculation(start_frame, ONSET_ENV_METRICS_THRESHOLD, CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH, onset_env_metrics, f0_interp, envelope)
        speech_end_time = speech_end_time_data["speech_end_time"]

        back_offset = 0
        if speech_end_time < 20:  # most probably file was cut too late or awake time extremely short
            back_offset = start_frame-4*CHARACTERISTIC_TIMESCALE_FRAMES + np.argmin(envelope[start_frame-4*CHARACTERISTIC_TIMESCALE_FRAMES:start_frame])
            start_frame = back_offset

            response_begin_time_data = response_begin_calculation(start_frame, CHARACTERISTIC_TIMESCALE_FRAMES, CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE,
                                                                RESPONSE_ENVELOP_THRESHOLD, contrast_voice_feature, envelope)
            response_begin_time = response_begin_time_data["response_begin_time"]
            speech_end_time_data = speech_end_calculation(start_frame, ONSET_ENV_METRICS_THRESHOLD, CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH,
                                                        onset_env_metrics, f0_interp, envelope)
            speech_end_time = speech_end_time_data["speech_end_time"]


        self.start_time = speech_end_time
        self.end_time = response_begin_time
        self.final_timedelta = self.end_time - self.start_time


class TTSPipelineExperimentalOld:
    def __init__(self, **kwargs) -> None:
        def normalize(arr, amin=-1, amax=1):
            return amin + (amax-amin) * (arr - arr.min()) / (arr.max() - arr.min())

        def estimate_envelope(
            x: np.ndarray,
            maximum_filter_window_size: int,
            savgol_filter_window_size: int,
            savgol_filter_polyorder: int,
        ) -> np.ndarray:

            if maximum_filter_window_size % 2 == 0:
                maximum_filter_window_size += 1

            if savgol_filter_window_size % 2 == 0:
                savgol_filter_window_size += 1

            envelope = np.abs(x)
            envelope = maximum_filter(envelope, size=maximum_filter_window_size)
            return savgol_filter(
                envelope,
                window_length=savgol_filter_window_size,
                polyorder=savgol_filter_polyorder,
            )

        def interpolate_to_size(arr, size):
            return np.interp(
                np.arange(size)/SAMPLE_RATE,  # at frames
                np.linspace(0, size/SAMPLE_RATE, arr.shape[0]),  # available x
                arr  # values
            )

        CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH = 3.0
        CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE = 3.0
        POWER_FILTER_THRESHOLD = 0.1
        ONSET_ENV_METRICS_THRESHOLD = 0.01
        ENVEL_MAXIMUM_FILTER_WINDOW_SIZE = 448
        ENVEL_SAVGOL_FILTER_WINDOW_SIZE = 15
        ENVEL_AVGOL_FILTER_POLYORDER = 0
        filename_full = kwargs["filename_full"]
        filename_part = kwargs["filename_endpart"]
        full_original_audio, sr = librosa.load(filename_full, sr=SAMPLE_RATE)
        self.full_original_audio = full_original_audio
        self.time_scale = np.arange(full_original_audio.size)/SAMPLE_RATE
        self.full_mva = moving_average(full_original_audio, window=60)
        filename_part = filename_full.replace('_full', '')
        part_audio, _ = librosa.load(filename_part, sr=SAMPLE_RATE)

        start_frame = full_original_audio.size - part_audio.size

        # norm for speech and response should be independent!
        full_original_audio[:start_frame] = normalize(full_original_audio[:start_frame])
        full_original_audio[start_frame:] = normalize(full_original_audio[start_frame:])

        envelope = estimate_envelope(
            x=np.abs(full_original_audio),
            maximum_filter_window_size=ENVEL_MAXIMUM_FILTER_WINDOW_SIZE,  # from experimenting a bit
            savgol_filter_window_size=ENVEL_SAVGOL_FILTER_WINDOW_SIZE,  # from experimenting a bit
            savgol_filter_polyorder=ENVEL_AVGOL_FILTER_POLYORDER,  # from experimenting a bit
        )

        f0, _, voicing_probability = librosa.pyin(y=full_original_audio, sr=SAMPLE_RATE, frame_length=1024, fmin=65, fmax=2094)  # , fill_na=None)

        f0_interp = np.interp(  # interpolation of f0 curve (less than frames)
            np.arange(full_original_audio.size)/SAMPLE_RATE,  # at frames
            np.linspace(0, full_original_audio.size/SAMPLE_RATE, f0.shape[0]),  # available x
            f0  # values
        )

        onset_env = librosa.onset.onset_strength(y=full_original_audio, sr=SAMPLE_RATE,
                                                 aggregate=np.median,
                                                 fmax=4000, n_mels=256)
        onset_env = interpolate_to_size(onset_env, full_original_audio.size)
        onset_env = onset_env / onset_env.max()

        power_metrics = np.log10(1+np.abs(full_original_audio))

        power_metrics = (power_metrics - power_metrics.min()) / (power_metrics.max() - power_metrics.min())

        voicing_probability_interp = np.interp(  # interpolation of f0 curve (less than frames)
            np.arange(full_original_audio.size)/SAMPLE_RATE,  # at frames
            np.linspace(0, full_original_audio.size/SAMPLE_RATE, voicing_probability.shape[0]),  # available x
            voicing_probability  # values
        )

        onset_env_metrics = voicing_probability_interp * onset_env
        onset_env_metrics = onset_env_metrics / onset_env_metrics.max()

        prob_amplitude_convolv = voicing_probability_interp * power_metrics

        speech_by_level_frame = start_frame - np.argmax((onset_env_metrics[:start_frame])[::-1] > ONSET_ENV_METRICS_THRESHOLD)
        speech_by_level = speech_by_level_frame / SAMPLE_RATE

        if not np.isnan(f0_interp[speech_by_level_frame]):
            speech_correction_frame = np.argmax(np.isnan(f0_interp[speech_by_level_frame:])) + speech_by_level_frame
            speech_correction = speech_correction_frame / SAMPLE_RATE
        else:
            speech_correction = False

        if speech_correction:
            if speech_correction_frame >= start_frame:
                speech_envelope_correction_frame = speech_correction_frame
            else:
                speech_min_envelope_value = envelope[speech_correction_frame:start_frame].min()
                speech_envelope_correction_frame = speech_correction_frame + np.argmax(envelope[speech_correction_frame:] < CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH * speech_min_envelope_value)
        else:
            if speech_by_level_frame >= start_frame:
                speech_envelope_correction_frame = speech_by_level_frame
            else:
                speech_min_envelope_value = envelope[speech_by_level_frame:start_frame].min()
                speech_envelope_correction_frame = speech_by_level_frame + np.argmax(envelope[speech_by_level_frame:] < CHECK_ENVELOPE_MINIMUM_RATIO_SPEECH * speech_min_envelope_value)
        speech_envelope_correction = speech_envelope_correction_frame / SAMPLE_RATE

        # if frequency_frame == 0:
        speech_end = speech_by_level

        # response_begin_frame = np.argmax(f0_interp[speech_end_frame+search_response_start:] > FUNDAMENDAL_FREQUENCY_CUTOFF_RESPONSE) + speech_end_frame+search_response_start

        power_filter = power_metrics > POWER_FILTER_THRESHOLD
        response_begin_frame = np.argmax(power_filter[start_frame:]*prob_amplitude_convolv[start_frame:] > POWER_FILTER_THRESHOLD) + start_frame

        response_begin = response_begin_frame/SAMPLE_RATE
        final_response = response_begin

        # this searches where amplitude is low, but can jump too far
        if start_frame >= response_begin_frame:
            response_envelop_correction_frame = response_begin_frame
        else:
            response_min_envelope_value = envelope[start_frame:response_begin_frame].min()
            response_envelop_correction_frame = response_begin_frame - np.argmax(envelope[start_frame:response_begin_frame][::-1] < CHECK_ENVELOPE_MINIMUM_RATIO_RESPONSE * response_min_envelope_value)

        # and this is for case jump was too far
        try:
            response_envelop_correction_frame += np.where(np.diff(np.sign(np.diff(envelope[response_envelop_correction_frame:response_begin_frame]))) == 2)[0][-1] + 1
        except IndexError:
            pass
        # explanation:
        # a = np.array([1,2,3,6,4,9,8,11,4]) how to find last not-border local minimum 8?
        # np.diff(np.sign(np.diff(a))) -> array([ 0,  0, -2,  2, -2,  2, -2])
        # np.where(np.diff(np.sign(np.diff(a)))==2)[0][-1] -> 5
        # a[5+1] = 8
        # this finds last local minimum (note that a[0] and a[-1] are not counted)

        response_envelop_correction = response_envelop_correction_frame / SAMPLE_RATE
        self.start_time = max(speech_end, speech_by_level, speech_envelope_correction)
        self.end_time = min(final_response, response_envelop_correction)
        self.final_timedelta = self.end_time - self.start_time
        # return max(speech_end, speech_by_level, speech_envelope_correction), min(final_response, response_envelop_correction)


class VadPipeline:
    def __init__(self, model, filename, min_silence_duration_ms) -> None:
        # if args.plot:
        #     self.plot = True
        # else:
        #     self.plot = False
        self.plot = False
        self.filename = filename
        self.y, self.sr = librosa.load(filename, sr=SAMPLE_RATE)
        self.y_original = self.y.copy()
        wav = read_audio(filename, sampling_rate=SAMPLE_RATE)
        self.speech_timestamps = get_speech_timestamps(wav,
                                                       model,
                                                       threshold=0.22,
                                                       min_speech_duration_ms=96,
                                                       min_silence_duration_ms=min_silence_duration_ms,  # 192 为唤醒相应时间
                                                       speech_pad_ms=0,
                                                       sampling_rate=SAMPLE_RATE,
                                                       return_seconds=False,  # Return speech timestamps in seconds (default is samples)
                                                       )
        if len(self.speech_timestamps) == 0:
            raise NoSegmentsFound

        self.time_scale = np.arange(self.y.size)/SAMPLE_RATE
        self.full_mva = moving_average(self.y, window=60)
        self.full_mva_original = self.full_mva.copy()

        if self.plot:
            self.fig = go.Figure()
        # self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.y, name='original', opacity=0.7))
        self.noise_reduction()

        self.local_noise = np.full((self.y.size), np.nan)
        self.above_global_noise = np.full((self.y.size), np.nan)
        self.integrated_noise = np.full((self.y.size), np.nan)
        self.rolling_split_noise = np.full((self.y.size), np.nan)

        self.intermediate_segments = {}
        self.voice_segments = {}
        self.corrections = []

        self.intermediate_segments[0] = IntermediateSegment(0, None, self.speech_timestamps[0], self.full_mva, self.y)

        for i, ts in enumerate(self.speech_timestamps):
            log.info(f'{i} {ts["start"]/SAMPLE_RATE} {ts["end"]/SAMPLE_RATE}')
            self.voice_segments[i] = VoiceSegment(i, ts, self.full_mva, self.y)
            if i + 1 < len(self.speech_timestamps):
                self.intermediate_segments[i+1] = (IntermediateSegment(i+1, self.speech_timestamps[i], self.speech_timestamps[i+1], self.full_mva, self.y))
            else:
                self.intermediate_segments[i+1] = (IntermediateSegment(i+1, self.speech_timestamps[i], None, self.full_mva, self.y))

        for i, s in self.intermediate_segments.items():
            s.data_for_arrays(self.integrated_noise, self.rolling_split_noise)

        # self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.integrated_noise, name='integrated_noise'))
        if self.plot:
            self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.rolling_split_noise, name='rolling_split_noise'))

    def noise_reduction(self):
        y_clean = nr.reduce_noise(y=self.y, sr=self.sr, stationary=True)
        self.y = y_clean
        self.full_mva = moving_average(self.y, window=60)
        if self.plot:
            self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.y, name='noise removed', opacity=0.7))
            self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.full_mva, name='NR mva'))

    def restore_original_sound(self):
        self.y = self.y_original.copy()
        self.full_mva = moving_average(self.y)

    def segments_correction(self):
        for i, s in self.voice_segments.items():
            if self.plot:
                self.fig.add_shape(type='line', x0=s.start_time, x1=s.start_time, y0=-np.abs(self.full_mva).max()/2, y1=np.abs(self.full_mva).max()/2, line=dict(width=3))
                self.fig.add_shape(type='line', x0=s.end_time, x1=s.end_time, y0=-np.abs(self.full_mva).max()/2, y1=np.abs(self.full_mva).max()/2, line=dict(width=3, dash='dash'))
            s.find_correction(self.intermediate_segments, 'from_start', self.average_noise)

            if s.chosen_start_correction == 'local':
                start_correction = s.start_time_local_corrected
                start_local_width = 4
                start_global_width = 2
            else:
                start_correction = s.start_time_global_corrected
                start_local_width = 2
                start_global_width = 4

            if s.chosen_dynamic_start_correction is not None:
                start_correction = s.start_time_dynamic_corrected
                start_local_width = 2
                start_global_width = 2
                if self.plot:
                    self.fig.add_shape(type='line', x0=s.start_time_dynamic_corrected, x1=s.start_time_dynamic_corrected, y0=-
                                       np.abs(self.full_mva).max()/2, y1=np.abs(self.full_mva).max()/2, line=dict(width=4, dash="dot", color='red'))
            if self.plot:
                self.fig.add_shape(type='line', x0=s.start_time_local_corrected, x1=s.start_time_local_corrected, y0=-np.abs(self.full_mva).max() /
                                   2, y1=np.abs(self.full_mva).max()/2, line=dict(width=start_local_width, dash="dash", color='red'))
                self.fig.add_shape(type='line', x0=s.start_time_global_corrected, x1=s.start_time_global_corrected, y0=-np.abs(self.full_mva).max() /
                                   2, y1=np.abs(self.full_mva).max()/2, line=dict(width=start_global_width, dash="solid", color='red'))

            s.find_correction(self.intermediate_segments, 'from_end', self.average_noise)
            if s.chosen_end_correction == 'local':
                end_correction = s.end_time_local_corrected
                end_local_width = 4
                end_global_width = 2
            else:
                end_correction = s.end_time_global_corrected
                end_local_width = 2
                end_global_width = 4

            if s.chosen_dynamic_end_correction is not None:
                end_correction = s.end_time_dynamic_corrected
                end_local_width = 2
                end_global_width = 2
                if self.plot:
                    self.fig.add_shape(type='line', x0=s.end_time_dynamic_corrected, x1=s.end_time_dynamic_corrected, y0=-
                                       np.abs(self.full_mva).max()/2, y1=np.abs(self.full_mva).max()/2, line=dict(width=4, dash="dot", color='blue'))
            if self.plot:
                self.fig.add_shape(type='line', x0=s.end_time_local_corrected, x1=s.end_time_local_corrected, y0=-np.abs(self.full_mva).max() /
                                   2, y1=np.abs(self.full_mva).max()/2, line=dict(width=end_local_width, dash="dash", color='blue'))
                self.fig.add_shape(type='line', x0=s.end_time_global_corrected, x1=s.end_time_global_corrected, y0=-np.abs(self.full_mva).max() /
                                   2, y1=np.abs(self.full_mva).max()/2, line=dict(width=end_global_width, dash="solid", color='blue'))
            log.info(f'{self.filename} {i} {start_correction:.8f} {end_correction:.8f}')
            self.corrections.append((start_correction, end_correction))

    def noise_average_levels(self):
        noise_dict = {}
        append_average_noise_index = []

        noises, weights = [], []

        for i, s in self.intermediate_segments.items():
            if s.is_empty_segment:
                append_average_noise_index.append(i)
            else:
                noise_dict[i] = s.local_noise
                noises.append(s.local_noise)
                weights.append(s.end_time - s.start_time)
        if len(noise_dict) == 0:
            self.average_noise = DEFAULT_NOISE_LEVEL
        else:
            self.average_noise = np.average(noises, weights=weights)
            # all_segments_noise_cleaned = outliers_replacer(list(noise_dict.values()))
            # self.average_noise = np.mean(all_segments_noise_cleaned)
        for i in append_average_noise_index:
            noise_dict[i] = self.average_noise
        for k, v in noise_dict.items():
            self.intermediate_segments[k].local_noise = v
            log.info(f'Segment {k} : Noise {v:.8f} Segment noise {self.intermediate_segments[k].local_noise:.8f}')
            self.local_noise[self.intermediate_segments[k].start_frame:self.intermediate_segments[k].end_frame] = v

        for i, s in self.intermediate_segments.items():
            above_global_noise_segment = np.where(s.segment_mva_level[:-IGNORE_CONTRIBUTION_BEFORE_VAD_START] > self.average_noise,
                                                  s.segment_mva_level[:-IGNORE_CONTRIBUTION_BEFORE_VAD_START]-self.average_noise, 0).sum()
            divisor = np.where(s.segment_mva_level[:-IGNORE_CONTRIBUTION_BEFORE_VAD_START] > self.average_noise)[0].size
            if divisor > 0:
                self.above_global_noise[s.start_frame:s.end_frame] = above_global_noise_segment/divisor*2
                s.above_global_noise_level = above_global_noise_segment/divisor*2
                log.info(f'{i=} {s.above_global_noise_level=}')
                if (next_voice := self.voice_segments.get(s.next_voice_segment_index)) is not None:
                    # if next_voice.segment_mva_level.mean() > above_global_noise_segment/divisor*2:
                    s.local_noise = above_global_noise_segment/divisor*2
            else:
                s.above_global_noise_level = None

            # s.local_noise = above_global_noise_segment/divisor*2
            # log.info(f'{i} MAV noise correction: {above_global_noise_segment/divisor*2}')

        if self.plot:
            self.fig.add_trace(go.Scatter(x=self.time_scale, y=np.ones_like(self.time_scale) * self.average_noise, name='global_noise'))
            self.fig.add_trace(go.Scatter(x=self.time_scale, y=self.above_global_noise, name='above_global_noise'))
            if len(noise_dict) > 0:
                self.fig.add_trace(go.Scatter(x=self.time_scale, y=np.ones_like(self.time_scale) * self.local_noise, name='local_noise'))

    def stats(self):
        for i, s in self.voice_segments.items():
            # log.info(f'{i}: Local start correction: {s.start_local_correction/SAMPLE_RATE} Global start correction: {s.start_global_correction/SAMPLE_RATE}')
            # log.info(f'{i}: Local end correction: {s.end_local_correction/SAMPLE_RATE} Global end correction: {s.end_global_correction/SAMPLE_RATE}')
            if s.chosen_dynamic_start_correction is not None:
                log.info(f'{i}: Dynamic start correction: {s.start_time_dynamic_corrected}')
            if s.chosen_dynamic_end_correction is not None:
                log.info(f'{i}: Dynamic end correction: {s.end_time_dynamic_corrected}')
        dt_array = []
        for i, (start, end) in enumerate(self.corrections):
            if i+1 < len(self.corrections):
                dt_array.append(self.corrections[i+1][0] - end)

        if len(dt_array) == 1:
            log.info(f'{self.filename} {dt_array[0]}')
            return dt_array[0]
        elif len(dt_array) > 1:
            first = dt_array[0]
            dt_array = list(filter(lambda x: x >= MINIMUM_SPEECH_PAUSE_REPORT, dt_array))
            if len(dt_array) > 0:
                log.info(f'{self.filename} {dt_array[0]}')
                return dt_array[0]
            else:
                log.info(f'{self.filename} {first}')
                return first
        else:
            log.info(f'{self.filename} n/f')
            return None


class Args:
    plot = None


def process_pair(model, part_file, full_file):

    fig = go.Figure()

    tts_pipeline = TTSPipelineExperimental(filename_full=full_file, filename_endpart=part_file, model=model)

    fig.add_trace(go.Scatter(x=tts_pipeline.time_scale, y=tts_pipeline.full_original_audio, name='original_audio'))
    fig.add_trace(go.Scatter(x=tts_pipeline.time_scale, y=tts_pipeline.full_mva, name='mva'))

    PLOT_AMPLITUDE = np.abs(tts_pipeline.full_original_audio).max()/2

    fig.add_shape(type='line', x0=tts_pipeline.start_time, x1=tts_pipeline.start_time, y0=-PLOT_AMPLITUDE, y1=PLOT_AMPLITUDE, line=dict(width=4, dash="solid", color='orange'))
    fig.add_shape(type='line', x0=tts_pipeline.end_time, x1=tts_pipeline.end_time, y0=-PLOT_AMPLITUDE, y1=PLOT_AMPLITUDE, line=dict(width=4, dash="solid", color='red'))

    # for vad in tts_pipeline.vad_novoice_points:
    #     pass  # fig.add_shape(type='line', x0=(vad)/SAMPLE_RATE, x1=(vad)/SAMPLE_RATE, y0=-PLOT_AMPLITUDE, y1=PLOT_AMPLITUDE, line=dict(width=4, dash="solid", color='black'))

    fig.update_layout(
        title=dict(text=f"{full_file}")
    )
    # fig.show()
    log.info(f'{full_file=} {tts_pipeline.final_timedelta=}')
    return tts_pipeline.final_timedelta


# print('=')
# full_file = ''
# part_file = ''
# model = load_silero_vad()
# time = process_pair(model, part_file, full_file)
# print(time)
