import wave
import os
import math
import sounddevice as sd

from time import perf_counter as time
from app.config.device_config import deviceConfig
from app.middleware.log import get_request_id, logger as log
from silero_vad import load_silero_vad, read_audio, get_speech_timestamps
from app.utils.thread_manager import thread_manager
from app.constant.constant import *


class MultiRecorder:
    # ffplay -f s16le -ar 16k 1022-mic.pcm
    model = load_silero_vad()

    def __init__(self, output_file, first_audio_time, chunk_split_silence_duration=2.0, max_record_time=10.0,
                 rate=16000, photo_map=None) -> None:
        if photo_map is None:
            photo_map = {}
        self.rate = rate
        self.CHUNK = 1024
        self.stream = None
        self.output_file = output_file
        self.first_audio_time = first_audio_time
        self.chunk_split_silence_duration = chunk_split_silence_duration  # seconds
        self.max_record_time = max_record_time  # seconds
        self.stop_record = False
        self.voice_present = False
        self.current_frame = None
        self.level = []
        self.current_frames = []
        self.last_speech_time = 0
        self.file_counter = 1
        self.chunk_start_time = time()
        self.photo_map = photo_map
        self.photograph_thread = None
        with open(f'{self.output_file}.log', 'wt') as f:
            f.write(f'')

    def get_intermediate_timestamps(self, timestamps):
        # intermediates = []
        for i, _ in enumerate(timestamps):
            # print(i, timestamps)
            if i > 0:
                start = timestamps[i - 1]["end"]
                end = timestamps[i]["start"]

                s_exclude = start * self.CHUNK / self.rate
                e_exclude = end * self.CHUNK / self.rate
                if e_exclude >= self.first_audio_time and (
                        e_exclude - s_exclude) / 1000 < self.chunk_split_silence_duration:
                    # a = int(end / 1024)
                    # b = int(end_frames / 1024)
                    # with open(f'{self.output_file}', 'wb') as f:
                    #     f.write(b''.join(self.current_frames[a: b]))
                    return start * self.CHUNK / self.rate, end * self.CHUNK / self.rate
                # intermediate = f'{start} - {end}'
                # print(intermediate)
        # return intermediates
        return 1, 0

    def run(self):
        is_first = False
        reset_time = 0
        reset_result_photo = False
        result_interval = deviceConfig.get("result_photo_interval")
        record_end_wait_time = deviceConfig.get("record_end_wait_time")
        start_time = time()
        self.last_speech_time = start_time
        try:
            default_input = sd.default.device[0]
            self.stream = sd.RawInputStream(
                samplerate=self.rate,
                channels=1,
                dtype='int16',
                blocksize=self.CHUNK,
                device=default_input
            )
            self.stream.start()
            while True:
                if time() - start_time < (self.first_audio_time / 1000) + 1.5:
                    try:
                        data, overflowed = self.stream.read(self.CHUNK)
                        if overflowed:
                            continue
                    except OSError:
                        continue
                    else:
                        self.current_frames.append(data)
                        # self.level.append(np.abs(np.frombuffer(data, dtype='h')).mean())
                else:
                    self.last_speech_time = time()
                    break
            start_time = time()
            while True:
                # 没有获取到车机响应，超时退出
                if time() - start_time > self.chunk_split_silence_duration and self.voice_present == False:
                    res = self.save_and_reset()
                    return -1
                # 如果到了硬超时 or 有响应+无声音时间>1s
                if time() - start_time > self.max_record_time or (
                        time() - self.last_speech_time > record_end_wait_time and self.voice_present):
                    # print("退出时间=", time(),  "Ts = ", self.last_speech_time)
                    res = self.save_and_reset()
                    return res
                try:
                    data, overflowed = self.stream.read(self.CHUNK)
                    if overflowed:
                        continue
                except OSError:
                    continue
                else:
                    self.current_frames.append(data)
                    # self.level.append(np.abs(np.frombuffer(data, dtype='h')).mean())
                # print(f'{len(self.current_frames) = } {time() - start_time = } {self.level[-1] = }')
                speech_timestamps = self.get_speech_timestamps()
                if speech_timestamps:
                    self.voice_present = True
                    self.last_speech_time = time()
                if self.photo_map["type"] == PlayConfigType.interaction or self.photo_map[
                    "type"] == PlayConfigType.multi:
                    if speech_timestamps:
                        # 表示检查到声音，但是这个位置如果有声音都会进来，所以可能得做一个只有第一次检查到声音才执行拍照的逻辑
                        if reset_time and not reset_result_photo and self.last_speech_time > reset_time:
                            if os.path.exists(self.photo_map.get("result_photo_path", "")):
                                self.photograph_thread = thread_manager.start_sub_thread(self.photo_map["project_id"],
                                                                                         "multi_photo_sub",
                                                                                         self.photo_map[
                                                                                             "svc_client"].photograph_process,
                                                                                         get_request_id(),
                                                                                         self.photo_map["pic_path"],
                                                                                         self.photo_map["file_name"],
                                                                                         self.photo_map[
                                                                                             "audio_duration"],
                                                                                         self.photo_map["random_str"],
                                                                                         False)
                                log.info(f"restart result photo: {self.photo_map.get('result_photo_path')}")
                            reset_result_photo = True
                        # 打印相应时间日志
                        if not is_first:
                            reset_time = time() + result_interval
                            is_first = True
                            log.info(
                                f"multi first speech_timestamps response is {time() - start_time + 1.5} {reset_time=}.")

                if os.path.exists('stop'):
                    os.remove('stop')
                    return -1
        except Exception as e:
            log.error("error is ", str(e))
        finally:
            # 终止audio对象
            if self.stream is not None:
                self.stream.stop()
                self.stream.close()
            if self.photograph_thread:
                self.photograph_thread.join()

    def save_recorder(self, timestamps):
        temp = True
        for timestamp in timestamps:
            if temp:
                temp = False
            else:
                a = int(timestamp["start"] / self.CHUNK)
                b = int(timestamp["end"] / self.CHUNK)
                with open(f'{self.output_file}', 'wb') as f:
                    f.write(b''.join(self.current_frames[a: b]))

    def save_and_reset(self):
        if len(self.current_frames) < 10:
            self.voice_present = False
            return -1
        full_record = self.output_file[:-4] + "_full.pcm"
        with open(f'{full_record}', 'wb') as f:
            f.write(b''.join(self.current_frames))
        save_point = math.ceil(self.first_audio_time / 64) + 4
        with open(f'{self.output_file}', 'wb') as f:
            f.write(b''.join(self.current_frames[save_point:]))
        try:
            timestamps = self.get_speech_timestamps(all_time=True)
            # print(timestamps)
            # self.save_recorder(timestamps)
            start, end = self.get_intermediate_timestamps(timestamps)
            # print(f'[({start},{end})]')

        except RuntimeError:
            return -1

        with open(f'{self.output_file}.log', 'a+') as f:
            f.write(f'{timestamps}\n')
        self.current_frames = []
        self.file_counter += 1
        self.last_speech_time = time()
        self.chunk_start_time = time()
        self.voice_present = False
        return int(end - start)

    def get_speech_timestamps(self, all_time=False):
        from uuid import uuid4
        random_file = uuid4().hex

        with wave.open(random_file, 'wb') as wavfile:
            wavfile.setparams((2, 2, int(self.rate / 2), 0, 'NONE', 'NONE'))
            if all_time:
                wavfile.writeframes(b''.join(self.current_frames))
            else:
                wavfile.writeframes(b''.join(self.current_frames[-16:]))

        wav = read_audio(random_file)

        speech_timestamps = get_speech_timestamps(
            wav,
            self.model,
            min_speech_duration_ms=250,
            min_silence_duration_ms=512,
            sampling_rate=self.rate,
            return_seconds=False,  # Return speech timestamps in seconds (default is samples)
        )
        os.remove(random_file)
        return speech_timestamps
