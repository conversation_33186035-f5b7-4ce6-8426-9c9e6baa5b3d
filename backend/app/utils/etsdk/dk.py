
from .dk_cffi import *
from .dk_msg import DKCanMsg
from .can_sender import IPCSender
import socket
import cantools
import time


class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

class DKRuntime(metaclass=SingletonMeta):
    def startup(self):
        self.ipc = IPCSender(host=socket.gethostname())
        return dk_startup()

    def cleanup(self):
        self.ipc.stop()
        return dk_cleanup()

    def query_channels(self):
        return dk_query_ifdesc()

    def get_ipc(self):
        return self.ipc
        
    
class DKCanChannel:
    def __init__(self, desc, recv_cb):
        """
        初始化 DKCanChannel 实例。

        :param desc: dk_ifdesc_t 结构体的指针
        :param recv_cb: 用户定义的接收回调函数，签名应为 callable(frame: DKCanMsg)
        """
        ch_name = desc.ch_name
        self.channel_name = convert_c_buffer_to_string(ch_name, len(ch_name))
        print(f"打开通道: {self.channel_name}")
        self.ipc = DKRuntime().get_ipc()
        self.dbc=None
        self.recv_cb = recv_cb
        # 定义 C 回调类型
        self._recv_cb_type = DK_RECV_CB_T
        # 包装用户回调
        self._wrapped_recv_cb = self._recv_cb_type(self._on_frame_received)
        self._dbit_timing = create_can_bit_timing()
        self._arbit_timing = create_can_bit_timing()
        self._config = create_can_config()
        self.fd = dk_open(desc, self._wrapped_recv_cb, ffi.NULL)
        if self.fd < 0:
            raise RuntimeError("dk_open failed")
        result = dk_get_can_config(self.fd, self._config)
        if(result != 0):
            raise RuntimeError("dk_get_can_config failed")
        result = dk_get_can_timing(self.fd, self._arbit_timing, self._dbit_timing)
        if(result != 0):
            raise RuntimeError("dk_get_can_timing failed")
    
    def set_dbit_timing(self, bitrate, presc, sjw, propseg, pseg1, pseg2):
        """
        设置 CAN 位定时参数。

        :param bitrate: 波特率
        :param presc: 预分频器
        :param sjw: 重新同步跳转宽度
        :param propseg: 传播时间段
        :param pseg1: 相位缓冲段1
        :param pseg2: 相位缓冲段2
        """
        self._dbit_timing.bitrate = bitrate
        self._dbit_timing.presc = presc
        self._dbit_timing.sjw = sjw
        self._dbit_timing.propseg = propseg
        self._dbit_timing.pseg1 = pseg1
        self._dbit_timing.pseg2 = pseg2

    def set_arbit_timing(self, bitrate, presc, sjw, propseg, pseg1, pseg2):
        """
        设置 CAN 位定时参数。

        :param bitrate: 波特率
        :param presc: 预分频器
        :param sjw: 重新同步跳转宽度
        :param propseg: 传播时间段
        :param pseg1: 相位缓冲段1
        :param pseg2: 相位缓冲段2
        """
        self._arbit_timing.bitrate = bitrate
        self._arbit_timing.presc = presc
        self._arbit_timing.sjw = sjw
        self._arbit_timing.propseg = propseg
        self._arbit_timing.pseg1 = pseg1
        self._arbit_timing.pseg2 = pseg2

    def set_config(
        self,
        ack=False,
        resister=False,
        brs=False,
        sample=8,
        frequency=500000,
        mode=0,  # CAN_MODE_NORMAL
        run_mode=0  # CAN_RUN_MODE_START
    ):
        """
        设置 CAN 配置参数。

        :param ack: ACK位
        :param resister: 注册位
        :param brs: BRS位
        :param sample: 采样点
        :param frequency: 频率
        :param mode: CAN模式
        :param run_mode: 运行模式
        """
        self._config.flags.ack = 1 if ack else 0
        self._config.flags.resister = 1 if resister else 0
        self._config.flags.brs = 1 if brs else 0
        self._config.mode = mode
        self._config.run_mode = run_mode
        self._config.sample = sample
        self._config.frequence = frequency

    def get_dbit_timing(self):
        """
        获取当前位定时设置。

        :return: 字典形式的位定时参数
        """
        return {
            "bitrate": self._dbit_timing.bitrate,
            "presc": self._dbit_timing.presc,
            "sjw": self._dbit_timing.sjw,
            "propseg": self._dbit_timing.propseg,
            "pseg1": self._dbit_timing.pseg1,
            "pseg2": self._dbit_timing.pseg2,
        }

    def get_arbit_timing(self):
        """
        获取当前位定时设置。

        :return: 字典形式的位定时参数
        """
        return {
            "bitrate": self._arbit_timing.bitrate,
            "presc": self._arbit_timing.presc,
            "sjw": self._arbit_timing.sjw,
            "propseg": self._arbit_timing.propseg,
            "pseg1": self._arbit_timing.pseg1,
            "pseg2": self._arbit_timing.pseg2,
        }

    def get_config(self):
        """
        获取当前 CAN 配置。

        :return: 字典形式的配置参数
        """
        return {
            "flags_ack": self._config.flags.bit.ack,
            "flags_resister": self._config.flags.bit.resister,
            "flags_brs": self._config.flags.bit.brs,
            "mode": self._config.mode,
            "run_mode": self._config.run_mode,
            "sample": self._config.sample,
            "frequency": self._config.frequence,
        }
    
    def apply_config(self):
        """
        应用当前配置。
        """
        result = dk_set_can_config(self.fd, self._config)
        if result != 0:
            raise RuntimeError("dk_set_can_config failed")
        result = dk_get_can_config(self.fd, self._config)
        if(result != 0):
            raise RuntimeError("dk_get_can_config failed")


    def set_resistor(self,enable):
        dk_set_resistor(self.fd,enable)

    def apply_bit_timing(self):
        result = dk_set_can_timing(self.fd, self._arbit_timing, self._dbit_timing)
        if result != 0:
            raise RuntimeError("dk_set_can_timing failed")
        result = dk_get_can_timing(self.fd, self._arbit_timing, self._dbit_timing)
        if(result != 0):
            raise RuntimeError("dk_get_can_timing failed")
        
    def _on_frame_received(self, c_fb_array, count, user_data):
        """
        C 回调的包装器，将接收到的帧封装成 DKCanMsg 对象并调用用户回调。

        :param c_fb_array: 指向 dk_fb_t* 数组的指针
        :param count: 帧的数量
        :param user_data: 用户数据（未使用）
        """
        msgs = []
        for i in range(count):
            fb_ptr = c_fb_array[i]
            msg = DKCanMsg(dir='Rx')
            msg.set_frame(fb_ptr)
            if self.dbc is not None:
                msg.set_dbc_ptr(self.dbc)
            msgs.append(msg)
            self.ipc.send(self.channel_name, msg, msg.get_type())
    
        self.recv_cb(msgs)

    def close(self):
        """关闭 CAN 通道。"""
        result = dk_close(self.fd)
        if result != 0:
            raise RuntimeError("dk_close 失败")
        
    def reset(self):
        """重置 CAN 通道。"""
        result = dk_reset(self.fd)
        if result != 0:
            raise RuntimeError("dk_reset 失败")

    def enable(self):
        """启用 CAN 通道。"""
        result = dk_enable(self.fd)
        if result != 0:
            raise RuntimeError("dk_enable 失败")

    def disable(self):
        """禁用 CAN 通道。"""
        result = dk_disable(self.fd)
        if result != 0:
            raise RuntimeError("dk_disable 失败")

    def transmit(self, frames):
        """
        传输 CAN 帧。

        :param frames: DKCanMsg 对象的列表
        :return: 传输结果
        """
        if not frames:
            raise ValueError("No frames to transmit")

        c_frames = [frame.can_frame for frame in frames]
        
        # 调用 C 函数 dk_transmit
        result = dk_transmit(self.fd, c_frames)
        if result < 0:
            raise RuntimeError("dk_transmit failed")
        
        timestamp = time.time_ns()
        for frame in frames:
            frame.set_timestamp(timestamp)
            self.ipc.send(self.channel_name, frame, frame.get_type())
        
        return result

    def set_dbc_file(self, file_path,encoding='gbk'):
        self.dbc = cantools.database.load_file(file_path, encoding=encoding)

    
    def __del__(self):
        """在实例销毁时关闭 CAN 通道。"""
        if self.fd >= 0:
            self.close()



        
