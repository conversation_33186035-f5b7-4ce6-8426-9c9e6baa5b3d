from cffi import FFI
from enum import IntEnum


ffi = FFI()

ffi.cdef("""
/* dk_frame_t 结构体 */
typedef struct dk_frame_s {
    uint8_t type;           /* bit[0:3], bit[7] must be 1 */
    uint8_t busid;          /* bit[0:5] */
    uint16_t length;        /* total length */
    uint16_t extension;     /* device dependent extension */
    uint16_t seqno;         /* sequence num per subif */
    uint64_t timestamp;
} dk_frame_t;

/* dk_canframe_t 结构体 */
typedef struct dk_canframe_s {
    dk_frame_t base;
    uint32_t id;
    union {
        struct {
            uint8_t DLC : 4;
            uint8_t r1 : 4;    /* reserved */
            uint8_t IDE : 1;
            uint8_t RTR : 1;
            uint8_t BRS : 1;
            uint8_t FDF : 1;
            uint8_t ESI : 1;
            uint8_t r2 : 3;    /* reserved */
            uint16_t SEQ : 16;
        } bf;
        uint32_t flag;    /* pad, mtv, rtr, eff, brs, fdf, esi */
    };
    uint8_t data[64];
} dk_canframe_t;

/* dk_linframe_t 结构体 */
typedef struct dk_linframe_s {
    dk_frame_t base;
    uint8_t id;
    uint8_t flag;    /* pad, mtv */
    uint8_t data[8];
} dk_linframe_t;

/* dk_mac_addr_s 结构体 */
typedef struct dk_mac_addr_s {
    uint8_t mac[6];
} dk_mac_addr_t;

/* dk_vlan_tag_s 结构体 */
typedef struct dk_vlan_tag_s {
    uint16_t tpid;    /* 0x8100 */
    uint16_t tic;     /* pri(3), cfi(1), vlanid(12) */
} dk_vlan_tag_t;

/* dk_ethframe_s 结构体 */
typedef struct dk_ethframe_s {
    dk_frame_t base;
    uint8_t dst[6];
    uint8_t src[6];
    uint16_t type;
    uint8_t data[1];
} dk_ethframe_t;

/* dk_ethframe_vlan_s 结构体 */
typedef struct dk_ethframe_vlan_s {
    dk_frame_t base;
    uint8_t dst[6];
    uint8_t src[6];
    dk_vlan_tag_t tag;
    uint16_t type;
    uint8_t data[1];
} dk_ethframe_vlan_t;

/* dk_ethframe_qinq_s 结构体 */
typedef struct dk_ethframe_qinq_s {
    dk_frame_t base;
    uint8_t dst[6];
    uint8_t src[6];
    dk_vlan_tag_t tag;      /* 0x8100 or 0x88a8 */
    dk_vlan_tag_t inner_tag;
    uint16_t type;
    uint8_t data[1];
} dk_ethframe_qinq_t;
""", packed=True)

ffi.cdef("""

typedef struct dk_ifdesc_s {
    char ch_name[20];
    int ch_idx;
    int dev_idx;
    int type;
} dk_ifdesc_t;

typedef unsigned char can_mode_t;
typedef unsigned char can_run_mode_t;

typedef struct can_config_s {
    union {
        unsigned char byte;
        struct {
            unsigned char ack : 1;
            unsigned char resister : 1;
            unsigned char brs : 1;
            unsigned char reserved : 5;
        } bit;
    } flags;
    can_mode_t mode;
    can_run_mode_t run_mode;
    unsigned char sample;
    unsigned int frequence;
} can_config_t;

typedef struct can_bit_timing_s {
    unsigned int bitrate;
    unsigned int presc;
    unsigned char sjw;
    unsigned char propseg;
    unsigned char pseg1;
    unsigned char pseg2;
} can_bit_timing_t;

typedef struct dk_frame_s dk_fb_t;

typedef void (*dk_recv_cb_t)(const dk_fb_t* fbs[], int count, void* user);

int dk_startup();
int dk_cleanup();
int dk_query_ifdesc(const dk_ifdesc_t*** descs);
int dk_free_ifdesc(const dk_ifdesc_t** descs);

int dk_open(const dk_ifdesc_t* desc, dk_recv_cb_t cb, void* user);
int dk_close(int fd);
int dk_reset(int fd);
int dk_enable(int fd);
int dk_disable(int fd);

int dk_transmit(int fd, dk_fb_t* fbs[], int count);

int dk_set_resistor(int fd, bool enable);
int dk_set_can_config(int fd, can_config_t* can_config);
int dk_get_can_config(int fd, can_config_t* can_config);
int dk_set_canfd_timing(int fd, can_bit_timing_t* bit_timing, can_bit_timing_t* dbit_timing);
int dk_get_canfd_timing(int fd, can_bit_timing_t* bit_timing, can_bit_timing_t* dbit_timing);

dk_frame_t* dk_frame_alloc(int type, int totalsize);
void dk_frame_free(dk_frame_t* fb);
""")


import sys
import os

if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    base_path = os.path.dirname(sys.executable)
    print("frozen base_path:", base_path)
else:
    # 如果是开发环境
    base_path = os.path.dirname(os.path.abspath(__file__))
dll_path = os.path.join(base_path, ".\\DLL\\libdk.dll")

try:
    libdk = ffi.dlopen(dll_path)
    print("DLL 加载成功")
except OSError as e:
    print(f"DLL 加载失败: {e}")

DK_FRAME_HDR_SIZE = ffi.sizeof("dk_frame_t")
DK_CAN_HDR_SIZE = ffi.sizeof("dk_canframe_t") - 64
DK_LIN_HDR_SIZE = ffi.sizeof("dk_linframe_t") - 8

class CanMode(IntEnum):
    DK_STANDARD_CAN = 0
    DK_ISO_CANFD = 1
    DK_NONE_ISO_CANFD = 2

# 定义 can_run_mode_t 枚举
class CanRunMode(IntEnum):
    DK_CAN_RUN_NORMAL = 0
    DK_CAN_RUN_LOOPBACK = 1
    DK_CAN_RUN_LISTEN_ONLY = 2

class DkBusTypeEnum(IntEnum):
    DK_UNDEF = 0
    DK_ETH = 1
    DK_CAN = 2
    DK_LIN = 3
    DK_FLEXRAY = 4

# DK_STANDARD_CAN = 0
# DK_ISO_CANFD = 1
# DK_NONE_ISO_CANFD = 2

# DK_CAN_RUN_NORMAL = 0
# DK_CAN_RUN_LOOPBACK = 1
# DK_CAN_RUN_LISTEN_ONLY = 2

# __all__ = [
#     "ffi",
#     "libdk",
#     "DK_FRAME_HDR_SIZE",
#     "DK_CAN_HDR_SIZE",
#     "DK_LIN_HDR_SIZE",
#     "CanMode",
#     "CanRunMode",
#     "DkBusTypeEnum",
#     # 添加其他需要导出的对象
# ]
