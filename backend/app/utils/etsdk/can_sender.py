import time
import random
import socket
import pickle
import threading


from .dk_msg import DKCanMsg

class IPCSender:
    def __init__(self, host='localhost', port=12345, timeout=0.1):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.server_socket = None
        self.conn = None
        self.monitoring = False
        self._stop_event = threading.Event()
        
        # 启动监听线程
        self.listening_thread = threading.Thread(target=self.start_listening)
        self.listening_thread.start()
        
    def __del__(self):
        self.stop()

    def start_listening(self):
        """开始监听客户端连接"""
        try:
            # 创建 socket 对象
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            # 绑定端口
            self.server_socket.bind((self.host, self.port))
            # 设置最大连接数，超过后排队
            self.server_socket.listen(5)
            self.server_socket.settimeout(self.timeout)
            print(f"服务器正在监听 {self.host}:{self.port}...")

            while not self._stop_event.is_set():
                try:
                    self.conn, addr = self.server_socket.accept()
                    print(f"连接地址: {str(addr)}")
                    self.monitoring = True
                    while self.monitoring and not self._stop_event.is_set():
                        time.sleep(1)
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"接受连接时出错: {e}")
                finally:
                    if self.conn:
                        self.conn.close()
                        self.conn = None
                        self.monitoring = False
        except Exception as e:
            print(f"监听初始化失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()

    def start_simulation(self):
        """开始模拟发送 CAN 消息"""
        while not self._stop_event.is_set():
            if self.monitoring:
                try:
                    # 随机选择消息类型
                    msg_id = 0x123
                    msg_type = 'CAN' #random.choice(['CAN', 'CAN FD'])
                    # 随机生成数据长度
                    data_length = 8 #random.randint(0, 8)
                    dlc = DKCanMsg.length_to_dlc(data_length)
                    # 生成随机数据
                    data = [random.randint(0, 255) for _ in range(data_length)]

                    # 创建 DKCanMsg 实例
                    can_msg = DKCanMsg(dlc=dlc, data=data)
                    can_msg.set_id(msg_id)

                    # 提取数据用于发送
                    actual_data = can_msg.get_data()
                    # 模拟 can.Message 格式，这里简化处理
                    message = {
                        'arbitration_id': msg_id,
                        'timestamp': can_msg.get_timestamp(),
                        'data': actual_data,
                        'is_extended_id': False
                    }

                    message_data = ('Tx', msg_type, message)
                    # 使用 pickle 序列化数据
                    serialized_data = pickle.dumps(message_data)
                    if self.conn:
                        self.conn.send(serialized_data)
                        # print(f"发送报文: {msg_type}, {message}")
                        msg_id = (msg_id + 1) % 0x800  # 保持在有效的11位ID范围内
                    time.sleep(0.001)  # 每秒发送一次
                except (ConnectionAbortedError, ConnectionResetError):
                    print("客户端连接中断，等待新连接...")
                    self.monitoring = False
                    self.conn = None
                except Exception as e:
                    print(f"发送消息时出错: {e}")
            else:
                time.sleep(1)  # 若不监控，等待一段时间后再次检查状态

    def stop(self):
        """停止服务"""
        self._stop_event.set()
        if self.conn:
            self.conn.close()
        if self.server_socket:
            self.server_socket.close()

    def send(self, channel_name, can_msg, msg_type='CAN'):
        if not self._stop_event.is_set():
            if self.monitoring:
                try:
                    # 提取数据用于发送
                    msg_id = can_msg.get_id()
                    actual_data = can_msg.get_data()
                    dir = can_msg.get_dir()
                    # 模拟 can.Message 格式，这里简化处理
                    message = {
                        'channel': channel_name,
                        'arbitration_id': msg_id,
                        'timestamp': can_msg.get_timestamp(),
                        'data': actual_data,
                        'is_extended_id': False
                    }

                    message_data = (dir, msg_type, message)
                    # 使用 pickle 序列化数据
                    serialized_data = pickle.dumps(message_data)
                    if self.conn:
                        self.conn.send(serialized_data)
                        # print(f"发送报文: {msg_type}, {message}")
                except (ConnectionAbortedError, ConnectionResetError):
                    print("客户端连接中断，等待新连接...")
                    self.monitoring = False
                    self.conn = None
                except Exception as e:
                    print(f"发送消息时出错: {e}")

if __name__ == "__main__":
    sender = IPCSender(host=socket.gethostname())

    # 启动模拟发送线程
    simulation_thread = threading.Thread(target=sender.start_simulation)
    simulation_thread.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("服务器关闭...")
        sender.stop()
        simulation_thread.join()
