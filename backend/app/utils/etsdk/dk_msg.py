
from .dk_cffi import *

def convert_64bit_timestamp_to_nanoseconds(timestamp_64bit):
    # 高 32 位表示秒
    seconds = (timestamp_64bit >> 32) & 0xFFFFFFFF
    # 低 32 位表示纳秒
    nanoseconds = timestamp_64bit & 0xFFFFFFFF
    # 计算总的纳秒数
    total_nanoseconds = seconds * 10**9 + nanoseconds
    return total_nanoseconds

def convert_nanoseconds_to_64bit_timestamp(total_nanoseconds):
    # 计算秒数
    seconds = total_nanoseconds // (10**9)
    # 计算剩余的纳秒数
    remaining_nanoseconds = total_nanoseconds % (10**9)

    # 确保秒数和纳秒数在 32 位范围内
    seconds &= 0xFFFFFFFF
    remaining_nanoseconds &= 0xFFFFFFFF

    # 合并秒数和纳秒数为 64 位时间戳
    timestamp_64bit = (seconds << 32) | remaining_nanoseconds
    return timestamp_64bit


class DKCanMsg:
    # DLC 和实际数据长度的映射表
    DLC_TO_LENGTH = {
        0: 0,   1: 1,   2: 2,   3: 3,
        4: 4,   5: 5,   6: 6,   7: 7,
        8: 8,   9: 12,  10: 16, 11: 20,
        12: 24, 13: 32, 14: 48, 15: 64
    }

    LENGTH_TO_DLC = {length: dlc for dlc, length in DLC_TO_LENGTH.items()}

    def __init__(self, dlc=0, data=None, type='CAN', dir='Tx'):
        """
        创建一个新的 CAN FD 帧。
        :param dlc: 数据长度码（Data Length Code），范围为 0-15
        :param data: 数据内容，可以是字节序列或列表
        """
        if not (0 <= dlc <= 15):
            raise ValueError("DLC must be between 0 and 15")

        # 分配内存
        self.can_frame = dk_canframe_new(dlc, type)
        self.dir = dir
        self.dbc_ptr=None
        if self.can_frame == ffi.NULL:
            raise MemoryError("Failed to allocate memory for CAN FD frame")
            
        # 设置数据
        if data is not None:
            self.set_data(data)
            
    def set_id(self, id):
        """
        设置 CAN FD 帧的 ID。
        :param id: 帧 ID
        """
        self.can_frame.id = id
        
    def get_id(self):
        return self.can_frame.id

    def get_type(self):
        return 'CANFD' if self.can_frame.bf.FDF else 'CAN'

    def get_dir(self):
        return self.dir

    def set_data(self, data):
        """
        设置 CAN FD 帧的数据。
        :param data: 数据内容，可以是字节序列或列表
        """
        if isinstance(data, bytes) or isinstance(data, bytearray):
            data = list(data)
        elif not isinstance(data, list):
            raise TypeError("Data must be bytes, bytearray, or list")

        actual_length = len(data)
        if actual_length > self.DLC_TO_LENGTH[self.can_frame.bf.DLC]:
            raise ValueError("Data length exceeds the maximum allowed by DLC")

        for i, byte in enumerate(data):
            self.can_frame.data[i] = byte

    def get_data(self):
        """
        获取 CAN FD 帧的数据。
        :return: 数据内容（字节序列）
        """
        actual_length = self.DLC_TO_LENGTH[self.can_frame.bf.DLC]
        return ffi.unpack(self.can_frame.data, actual_length)

    def get_dbc_data(self,parse_enum=False):
        if self.dbc_ptr is None:
            return None
        frame = self.dbc_ptr.get_message_by_frame_id(self.can_frame.id)
        if frame:
            signal_values = frame.decode(bytearray(self.get_data()),decode_choices=parse_enum)
            return signal_values


    def set_timestamp(self, timestamp):
        self.can_frame.base.timestamp = convert_nanoseconds_to_64bit_timestamp(timestamp)
    
    def get_timestamp(self):
        tt_base = self.can_frame.base.timestamp
        return convert_64bit_timestamp_to_nanoseconds(tt_base)

    def set_frame(self, frame):
        ffi.memmove(self.can_frame, frame, ffi.sizeof("dk_canframe_t"))

    def set_dbc_ptr(self,dbc_ptr):
        self.dbc_ptr=dbc_ptr
    def get_c_pointer(self):
        """返回指向底层 C 结构体的指针"""
        return ffi.addressof(self.can_frame)

    @staticmethod
    def dlc_to_length(dlc):
        """
        将 DLC 转换为实际数据长度。
        :param dlc: 数据长度码（DLC）
        :return: 实际数据长度
        """
        if dlc not in DKCanMsg.DLC_TO_LENGTH:
            raise ValueError("Invalid DLC value")
        return DKCanMsg.DLC_TO_LENGTH[dlc]

    @staticmethod
    def length_to_dlc(length):
        """
        将实际数据长度转换为 DLC。
        :param length: 实际数据长度
        :return: 数据长度码（DLC）
        """
        if length not in DKCanMsg.LENGTH_TO_DLC:
            raise ValueError("Invalid data length")
        return DKCanMsg.LENGTH_TO_DLC[length]

    def __del__(self):
        """
        释放 CAN 帧的内存。
        """
        if self.can_frame:
            dk_frame_free(self.can_frame)
            self.can_frame = None

    def __repr__(self):
        """
        返回帧的字符串表示。
        """
        return f"DKCanMsg(DLC={self.can_frame.bf.DLC}, Data={self.get_data()})"
