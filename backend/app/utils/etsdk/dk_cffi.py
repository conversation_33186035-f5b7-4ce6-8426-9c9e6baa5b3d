
from .dk_cffi_defs import *


# 定义回调类型
DK_RECV_CB_T = ffi.callback("void(const dk_fb_t* fbs[], int count, void* user)", None)

# 定义 Python 回调函数，并使用装饰器
@ffi.callback("void(const dk_fb_t* fbs[], int count, void* user)")
def recv_callback(fbs, count, user):
    for fb in fbs:
        # 处理接收到的帧缓冲区
        print(f"Received frame: {fb}")  # 根据实际情况处理


def create_can_config():
    config = ffi.new("can_config_t*")
    return config

def create_can_bit_timing():
    bit_timing = ffi.new("can_bit_timing_t*")
    return bit_timing

def convert_c_buffer_to_string(buffer, length):
    return ffi.unpack(buffer, length).decode("utf-8").rstrip("\x00")

def dk_canframe_new(dlc, type='CAN'):
    frame = libdk.dk_frame_alloc(DkBusTypeEnum.DK_CAN, ffi.sizeof("dk_canframe_t"))
    if frame == ffi.NULL:
        return None  # 分配失败

    can_frame = ffi.cast("dk_canframe_t*", frame)
    can_frame.bf.DLC = dlc
    can_frame.bf.FDF = type == 'CANFD' 
    return can_frame

def dk_frame_free(frame):
    libdk.dk_frame_free(ffi.cast("dk_frame_t*", frame))

# 示例：访问结构体字段
def print_dk_frame(frame_ptr):
    frame = frame_ptr.contents
    print(f"Type: {frame.type}")
    print(f"Bus ID: {frame.busid}")
    print(f"Length: {frame.length}")
    print(f"Extension: {frame.extension}")
    print(f"Sequence No: {frame.seqno}")
    print(f"Timestamp: {frame.timestamp}")

def dk_startup():
    return libdk.dk_startup()

def dk_cleanup():
    return libdk.dk_cleanup()

def dk_query_ifdesc():
    descs_ptr = ffi.new("const dk_ifdesc_t***")
    result = libdk.dk_query_ifdesc(descs_ptr)
    if result < 0:
        raise Exception("dk_query_ifdesc failed")
    return descs_ptr[0]
    #return [ffi.unpack(descs_ptr[0][i], ffi.sizeof("dk_ifdesc_t")) for i in range(result)]

def dk_free_ifdesc(descs_ptr):
    libdk.dk_free_ifdesc(descs_ptr)

def dk_open(desc, cb, user):
    return libdk.dk_open(desc, cb, user)

def dk_close(fd):
    return libdk.dk_close(fd)

def dk_reset(fd):
    return libdk.dk_reset(fd)

def dk_enable(fd):
    return libdk.dk_enable(fd)

def dk_disable(fd):
    return libdk.dk_disable(fd)

def dk_transmit(fd, fbs):
    fbs_ptr = ffi.new("dk_fb_t*[{}]".format(len(fbs)))
    for i, fb in enumerate(fbs):
        fbs_ptr[i] = ffi.addressof(fb.base)
    return libdk.dk_transmit(fd, fbs_ptr, len(fbs))

def dk_set_resistor(fd, enable):
    return libdk.dk_set_resistor(fd, enable)

def dk_set_can_config(fd, can_config):
    return libdk.dk_set_can_config(fd, can_config)

def dk_get_can_config(fd, can_config):
    return libdk.dk_get_can_config(fd, can_config)

def dk_set_can_timing(fd, bit_timing, dbit_timing):
    return libdk.dk_set_canfd_timing(fd, bit_timing, dbit_timing)

def dk_get_can_timing(fd, bit_timing, dbit_timing):
    return libdk.dk_get_canfd_timing(fd, bit_timing, dbit_timing)


# # 示例：使用封装的接口
# if __name__ == "__main__":
#     dk_startup()
#     time.sleep(3)
#     # 查询接口描述
#     ifdescs = dk_query_ifdesc()

#     fd = dk_open(ifdescs[0], recv_callback, ffi.NULL)
#     can_config = ffi.new("can_config_t*")
#     dk_get_can_config(fd, can_config)
#     can_config.run_mode = CanRunMode.DK_CAN_RUN_NORMAL
#     dk_set_can_config(fd, can_config)
    
#     dk_enable(fd)
#     # 发送数据
#     fb1 = dk_canframe_new(8)
#     fb1.id = 0x123
#     res = dk_transmit(fd, [fb1])
#     print(f"Transmit result: {res}")
#     # 关闭设备
#     dk_close(fd)

#     # 清理
#     dk_cleanup()
