from datetime import datetime, timedelta

from sqlalchemy import Column, Index, String, TIMESTAMP, text, ForeignKey, Integer, DateTime, Boolean, Float
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


def get_beijing_time():
    return datetime.utcnow() + timedelta(hours=8)


class User(Base):
    __tablename__ = 'user'
    __table_args__ = (
        Index('idx_nick_name', 'nick_name'),
        Index('idx_phone', 'phone'),
        {'comment': '用户表'}
    )

    id = Column(BIGINT, primary_key=True, comment='主键')
    union_id = Column(String(64), nullable=False, server_default=text("''"), comment='微信开放平台下的用户唯一标识')
    open_id = Column(String(64), nullable=False, server_default=text("''"), comment='微信openid')
    nick_name = Column(String(32), nullable=False, server_default=text("''"), comment='昵称')
    password = Column(String(64), nullable=False, server_default=text("''"), comment='密码')
    avatar = Column(String(256), nullable=False, server_default=text("''"), comment='头像')
    phone = Column(String(11), nullable=False, server_default=text("''"), comment='手机号')
    email = Column(String(50), nullable=False, server_default=text("''"), comment='电子邮箱')
    last_login = Column(String(20), nullable=False, server_default=text("''"), comment='上次登录时间')
    delete_at = Column(String(20), nullable=False, server_default=text("''"), comment='删除时间')
    created_at = Column(TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')


class CorpusAudio(Base):
    __tablename__ = "corpus_audio"

    id = Column(Integer, primary_key=True, autoincrement=True)  # 自增字段
    aud_id = Column(String(64), unique=True, index=True)
    corpus_id = Column(String(64), nullable=True)
    aud_url = Column(String(256), nullable=True, comment='语料路径,存的完整路径')
    pinyin = Column(String(64), nullable=True)
    audio_duration = Column(String(32), nullable=True)


class TestCorpus(Base):
    __tablename__ = "test_corpus"

    id = Column(Integer, primary_key=True, autoincrement=True)
    corpus_id = Column(String(64), unique=True, index=True)
    aud_id = Column(String(64), nullable=True, comment='语料id')
    text = Column(String(64), nullable=True, comment='语料文本')
    pinyin = Column(String(64), nullable=True, comment='语料拼音')
    test_scenario = Column(String(64), nullable=True, comment='测试场景')
    evaluation_metric = Column(String(32), nullable=True, comment='评估指标')
    audio_url = Column(String(64), nullable=True, comment='语料路径,其实存的只是名字')
    audio_duration = Column(String(32), nullable=True, comment='语料时长')
    speaker = Column(String(64), nullable=True, comment='声音')
    language = Column(String(64), nullable=True, comment='语言')
    # label = Column(String(64), nullable=True, comment='其他标签')
    operation = Column(String(64), nullable=True, comment='操作')
    car_function = Column(String(64), nullable=True, comment='测试车机功能')
    expect_result = Column(String(256), nullable=True, comment='预期结果')
    test_type = Column(String(64), nullable=True, comment='测试类型')
    audio_path = Column(String(256), nullable=True, comment='语料路径')
    is_tts = Column(Boolean, default=False, comment='是否tts生成')
    is_multi = Column(Boolean, default=False, comment='是否是多伦对话语料')
    # signal_id = Column(String(64), nullable=True, comment='can目标信号')
    target_signal = Column(String(256), nullable=True, comment='目标信号取值配置')


class MultiCorpus(Base):
    __tablename__ = "multi_corpus"
    __table_args__ = (
        Index('find_corpus_id', 'corpus_id'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    corpus_id = Column(String(64), nullable=True, comment='多伦语料id')
    corpus_name = Column(String(64), nullable=True, comment='多伦语料名字')
    testcorpus_id = Column(String(64), nullable=True, comment='测试语料id')
    is_delete = Column(Boolean, nullable=True, comment='假删除，标记位')
    order = Column(Integer, default=0, comment='排序')


class RouseCorpus(Base):
    __tablename__ = "rouse_corpus"

    id = Column(Integer, primary_key=True, autoincrement=True)
    corpus_id = Column(String(64), unique=True, index=True)
    aud_id = Column(String(64), nullable=True, comment='语料id')
    text = Column(String(64), nullable=True, comment='语料文本')
    pinyin = Column(String(64), nullable=True, comment='语料拼音')
    test_scenario = Column(String(64), nullable=True, comment='测试场景')
    test_object = Column(String(32), nullable=True, comment='测试对象')
    audio_url = Column(String(64), nullable=True, comment='语料路径')
    audio_duration = Column(String(32), nullable=True, comment='语料时长')
    speaker = Column(String(64), nullable=True, comment='声音')
    language = Column(String(64), nullable=True, comment='语言')
    # label = Column(String(64), nullable=True, comment='其他标签')
    operation = Column(String(64), nullable=True, comment='操作')
    audio_path = Column(String(256), nullable=True, comment='语料路径')
    is_tts = Column(Boolean, default=False, comment='是否tts生成')


class DisturbCorpus(Base):
    __tablename__ = "disturb_corpus"

    id = Column(Integer, primary_key=True, autoincrement=True)
    corpus_id = Column(String(64), unique=True, index=True)
    aud_id = Column(String(64), nullable=True, comment='语料id')
    text = Column(String(64), nullable=True, comment='语料文本')
    pinyin = Column(String(64), nullable=True, comment='语料拼音')
    audio_url = Column(String(64), nullable=True, comment='语料路径')
    audio_duration = Column(String(32), nullable=True, comment='语料时长')
    speaker = Column(String(64), nullable=True, comment='声音')
    language = Column(String(64), nullable=True, comment='语言')
    # label = Column(String(64), nullable=True, comment='其他标签')
    operation = Column(String(64), nullable=True, comment='操作')
    audio_path = Column(String(256), nullable=True, comment='语料路径')
    is_tts = Column(Boolean, default=False, comment='是否tts生成')


class BackgroundNoise(Base):
    __tablename__ = "background_noise"

    id = Column(Integer, primary_key=True, autoincrement=True)
    corpus_id = Column(String(64), unique=True, index=True)
    aud_id = Column(String(64), nullable=True, comment='语料id')
    text = Column(String(64), nullable=True)
    noise_environ = Column(String(64), nullable=True, comment='噪声环境')
    pinyin = Column(String(64), nullable=True, comment='语料拼音')
    audio_url = Column(String(64), nullable=True, comment='语料路径')
    audio_duration = Column(String(32), nullable=True, comment='语料时长')
    # label = Column(String(64), nullable=True, comment='其他标签')
    operation = Column(String(64), nullable=True, comment='操作')
    audio_path = Column(String(256), nullable=True, comment='语料路径')


class TestProject(Base):
    __tablename__ = "test_project"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(String(64), unique=True, index=True)
    project_name = Column(String(64), nullable=True, comment='项目名称')
    project_code = Column(String(64), nullable=True, comment='项目编号')
    description = Column(String(256), nullable=True, comment='项目描述')
    test_object_name = Column(String(64), nullable=True, comment='测试对名字')
    test_object_version = Column(String(64), nullable=True, comment='测试对象版本')
    project_status = Column(String(64), nullable=True, default='new', comment='项目状态')
    project_process = Column(String(32), nullable=True, comment='项目进度')


class ProjectPlan(Base):
    __tablename__ = "project_plan"
    __table_args__ = (
        Index('find_play_config_id1', 'play_config_id'),
        Index('find_project_id1', 'project_id'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    plan_id = Column(String(64), unique=True, index=True)
    plan_name = Column(String(64), nullable=True, comment='方案名字')
    project_id = Column(String(64), nullable=True, comment='方案所属项目id')
    turn_id = Column(Integer, nullable=True, default=None, comment='第几轮')  # 该字段只在复测方案有用，其余为None
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    plan_status = Column(String(64), nullable=True, comment='方案状态')
    is_delete = Column(Boolean, nullable=True, default=False, comment='假删除，标记位')
    type = Column(String(64), nullable=True, comment='播放配置类型 唤醒 rouse 非唤醒 interaction')
    # 唤醒
    wakeup_time = Column(Float, nullable=True, default=None, comment='唤醒时间')
    wakeup_success_rate = Column(Float, nullable=True, default=None, comment='唤醒成功率')
    # 误唤醒
    false_wakeup_times = Column(Integer, nullable=True, default=None, comment='误唤醒次数')
    # 单次交互 '交互成功率' 在多轮对话中复用
    interaction_success_rate = Column(Float, nullable=True, default=None, comment='交互成功率')
    word_recognition_rate = Column(Float, nullable=True, default=None, comment='字识别率')  # 如果为 false 则不需要ocr识别
    response_time = Column(Float, nullable=True, default=None, comment='响应时间')
    # can
    can_detection = Column(Float, nullable=True, default=None, comment='can判断检测成功率')


class TestResult(Base):
    __tablename__ = "test_result"
    __table_args__ = (
        Index('find_project_id', 'project_id'),
        Index('find_turn_id', 'turn_id'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    result_id = Column(String(64), unique=True, index=True)
    time = Column(DateTime, default=get_beijing_time)
    project_id = Column(String(64), nullable=True, comment='结果对应的项目id')
    plan_id = Column(String(64), nullable=True, comment='该条语料对应的方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')
    turn_id = Column(Integer, nullable=False, default=1, comment='第几轮')
    circle = Column(Integer, nullable=False, default=1, comment='配置内大循环第几次')
    test_scenario = Column(String(64), nullable=True, comment='测试场景')
    text = Column(String(64), nullable=True, comment='语料文本')
    result = Column(String(64), nullable=True, default='未测试', comment='测试结果')
    can_result = Column(String(64), nullable=True, default='未测试', comment='can信号检测结果')
    reason = Column(String(256), nullable=True, default='未测试', comment='评估理由')
    image = Column(String(64), nullable=True, comment='备用结果判断')
    score = Column(Integer, nullable=False, default=-1, comment='测试结果通过模型出来的分数')
    asr_result = Column(String(256), nullable=True, default='未测试', comment='语音转文字出来的结果')
    mic_audio_url = Column(String(256), nullable=True, comment='车机响应录音url')
    ocr_pic_url = Column(String(64), nullable=True, comment='ocr图片路径')
    ocr_result = Column(String(256), nullable=True, default='未测试', comment='ocr识别文字内容')
    ocr_accuracy_rate = Column(Float, nullable=True, default=None, comment='ocr识别车机文字和text的准确率')
    relative_interval = Column(Integer, nullable=False, default=0, comment='相对间隔时间，单位秒')
    response_time = Column(Float, nullable=True, default=None, comment='测试响应时间, 单位 秒')
    expect_result = Column(String(256), nullable=True, default='无', comment='预期结果')
    image_confidence = Column(Float, nullable=True, default=None, comment='图片置信度')
    # result_confidence = Column(Integer, nullable=True, default=None, comment='模型判断置信度')
    llm_ui_confidence = Column(Integer, nullable=True, default=None, comment='模型系统判断置信度')
    llm_voice_confidence = Column(Integer, nullable=True, default=None, comment='模型语音判断置信度')
    signlist = Column(String(64), nullable=True, default='0b00000000', comment='标记')
    can_data_url = Column(String(256), nullable=True, comment='can获取数据的url')
    video_path = Column(String(256), nullable=True, comment='视频路径')
    cabin_video_path = Column(String(256), nullable=True, comment='座舱视频路径')
    update_result_reason = Column(String(256), nullable=True, comment='修改测试结果理由')
    request_id = Column(String(256), nullable=True, comment='request_id')


class MultiResult(Base):
    __tablename__ = "multis_result"

    id = Column(Integer, primary_key=True, autoincrement=True)
    multi_result_id = Column(String(64), unique=True, index=True)
    project_id = Column(String(64), nullable=True, comment='结果对应的项目id')
    plan_id = Column(String(64), nullable=True, comment='该条语料对应的方案id')
    multicorpus_id = Column(String(64), nullable=True, comment='多伦语料id')
    turn_id = Column(Integer, nullable=False, default=1, comment='第几轮')
    circle = Column(Integer, nullable=False, default=1, comment='配置内大循环第几次')
    result = Column(String(64), nullable=True, default='未测试', comment='测试结果')
    can_result = Column(String(64), nullable=True, default='未测试', comment='can信号检测结果')
    reason = Column(String(64), nullable=True, comment='评估理由')
    success_rate = Column(String(64), nullable=True, comment='执行成功率')
    time = Column(DateTime, default=get_beijing_time)


class FalseRouseResult(Base):
    __tablename__ = "false_rouse_result"

    id = Column(Integer, primary_key=True, autoincrement=True)
    result_id = Column(String(64), unique=True, index=True)
    project_id = Column(String(64), nullable=True, comment='结果对应的项目id')
    plan_id = Column(String(64), nullable=True, comment='该条语料对应的方案id')
    turn_id = Column(Integer, nullable=False, default=1, comment='第几轮')
    disturb_corpus_id = Column(String(64), nullable=True, comment='干扰语料id')
    asr_result = Column(String(64), nullable=True, comment='唤醒响应')
    mic_audio_url = Column(String(64), nullable=True, comment='唤醒录音')
    image = Column(String(64), nullable=True, comment='唤醒图像')
    time = Column(String(64), nullable=True, comment='误唤醒时刻')
    relative_interval = Column(Integer, nullable=False, default=0, comment='相对间隔时间，单位秒')
    video_path = Column(String(256), nullable=True, comment='视频路径')
    cabin_video_path = Column(String(256), nullable=True, comment='座舱视频路径')


class TCorpusTree(Base):
    __tablename__ = "tcorpus_tree"
    __table_args__ = (
        Index('find_corpus_id1', 'corpus_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(String(64), nullable=True, comment='方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')
    score = Column(Integer, nullable=False, default=-1, comment='测试结果')
    result_id = Column(String(64), nullable=True, comment='语料id')


class RCorpusTree(Base):
    __tablename__ = "rcorpus_tree"
    __table_args__ = (
        Index('find_corpus_id2', 'corpus_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(String(64), nullable=True, comment='方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')
    result_id = Column(String(64), nullable=True, comment='语料id')


class DCorpusTree(Base):
    __tablename__ = "dcorpus_tree"
    __table_args__ = (
        Index('find_corpus_id3', 'corpus_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(String(64), nullable=True, comment='方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')


class BNoiseTree(Base):
    __tablename__ = "bnoise_tree"
    __table_args__ = (
        Index('find_corpus_id4', 'corpus_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(String(64), nullable=True, comment='方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')


class PlayConfig(Base):
    __tablename__ = "play_config"

    id = Column(Integer, primary_key=True, autoincrement=True)
    play_config_id = Column(String(64), unique=True, index=True)
    config_name = Column(String(64), nullable=True, comment='播放配置名')
    description = Column(String(64), nullable=True, comment='配置描述')
    type = Column(String(256), nullable=True, comment='播放配置类型 唤醒 rouse 非唤醒 interaction')


# 开始
class StartConfig(Base):
    __tablename__ = "start_config"
    __table_args__ = (
        Index('find_play_config_id2', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    circle = Column(String(32), nullable=True, comment='循环次数')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')
    # 唤醒
    wakeup_time = Column(Boolean, default=True, comment='唤醒时间')
    wakeup_success_rate = Column(Boolean, default=True, comment='唤醒成功率')
    # 误唤醒
    false_wakeup_times = Column(Boolean, default=True, comment='误唤醒次数')
    interval_wait_time = Column(Integer, default=0, comment='间隔等待时间')
    # 单次交互 '交互成功率' 在多轮对话中复用
    interaction_success_rate = Column(Boolean, default=True, comment='交互成功率')
    word_recognition_rate = Column(Boolean, default=True, comment='字识别率')  # 如果为 false 则不需要ocr识别
    response_time = Column(Boolean, default=True, comment='响应时间')
    asr_correct = Column(Boolean, default=True, comment='优化asr识别内容中的错别字')
    # can
    can_detection = Column(Boolean, default=False, comment='can检测配置')


# 嵌入唤醒
class RouseConfig(Base):
    __tablename__ = "rouse_config"
    __table_args__ = (
        Index('find_play_config_id3', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    wakeUpWaitDifferent = Column(Integer, nullable=False, default=0, comment='不同语料间唤醒等待时间 单位ms')
    wakeUpWaitRepeated = Column(Integer, nullable=False, default=0, comment='语料重复时唤醒等待时间 单位ms')
    frequencyDifferent = Column(String(64), nullable=True,
                                comment='不同语料间唤醒频率 first 仅一次 every 每次 interval 间隔frequency_interval次')
    frequencyRepeated = Column(String(64), nullable=True, comment='语料重复时唤醒频率 none 不嵌入 every 每次')
    frequencyIntervalDifferent = Column(Integer, nullable=False, default=0,
                                        comment='不同语料间间隔次数，当上面为 interval 的时候起作用')
    channel = Column(String(64), nullable=True, comment='播放通道配置')
    gain = Column(Integer, nullable=False, default=0, comment='增益配置')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


# 播放干扰语料
class DisturbConfig(Base):
    __tablename__ = "disturb_config"
    __table_args__ = (
        Index('find_play_config_id4', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    channel = Column(String(64), nullable=True, comment='播放通道配置')
    gain = Column(Integer, nullable=False, default=0, comment='增益配置')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


# 播放背景噪声
class NoiseConfig(Base):
    __tablename__ = "noise_config"
    __table_args__ = (
        Index('find_play_config_id5', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    channel = Column(String(64), nullable=True, comment='播放通道配置')
    gain = Column(Integer, nullable=False, default=0, comment='增益配置')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


# 播放测试语料
class TestCorpusConfig(Base):
    __tablename__ = "testcorpus_config"
    __table_args__ = (
        Index('find_play_config_id6', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    repeat = Column(Integer, nullable=False, default=1, comment='重复次数')
    wait_time = Column(Integer, nullable=False, default=1, comment='无声音等待时间s')
    timout = Column(Integer, nullable=False, default=10, comment='最长录制时间s')
    channel = Column(String(64), nullable=True, comment='播放通道配置')
    gain = Column(Integer, nullable=False, default=0, comment='增益配置')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


# 唤醒场景 播放唤醒语料
class RouseCorpusConfig(Base):
    __tablename__ = "rousecorpus_config"
    __table_args__ = (
        Index('find_play_config_id7', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    repeat = Column(Integer, nullable=False, default=1, comment='重复次数')
    channel = Column(String(64), nullable=True, comment='播放通道配置')
    gain = Column(Integer, nullable=False, default=0, comment='增益配置')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


# 等待
class WaitConfig(Base):
    __tablename__ = "wait_config"
    __table_args__ = (
        Index('find_play_config_id8', 'play_config_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    play_config_id = Column(String(64), nullable=True, comment='对应播放配置id')
    duration = Column(Integer, nullable=False, default=0, comment='等待时长')
    seat = Column(Integer, nullable=False, comment='该配置所在配置列表中的位置')


class ReviewResult(Base):
    __tablename__ = "review_result"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(String(64), nullable=True, comment='结果对应的项目id')
    turn_id = Column(Integer, nullable=False, default=1, comment='第几轮')
    video_path = Column(String(256), nullable=True, comment='视频名字')


### TestLabelGroup 表
class TestLabelGroup(Base):
    __tablename__ = "test_label_groups"

    label_group_id = Column(String(64), primary_key=True, index=True)
    label_group_name = Column(String(256), nullable=True, comment='标签组名字')
    # creator = Column(String(64), nullable=True, comment='标签组创建人')
    label_group_type = Column(String(256), nullable=True, comment='标签组类型')


### TestLabel 表
class TestLabel(Base):
    __tablename__ = "test_labels"

    label_id = Column(String(64), primary_key=True, index=True)
    label_name = Column(String(256), nullable=True, comment='标签名字')
    label_group_id = Column(String(64), ForeignKey('test_label_groups.label_group_id'))
    color = Column(String(64), nullable=True, comment='标签颜色')
    range = Column(String(256), nullable=True, comment='标签范围')
    # creator = Column(String(64), nullable=True, comment='标签创建人')
    created_time = Column(DateTime, default=get_beijing_time)


class TableId(Base):
    __tablename__ = "table_id"

    id = Column(Integer, primary_key=True, index=True)
    corpus_audio_id = Column(Integer, nullable=False, default=1)
    test_corpus_id = Column(Integer, nullable=False, default=1)
    rouse_corpus_id = Column(Integer, nullable=False, default=1)
    disturb_corpus_id = Column(Integer, nullable=False, default=1)
    background_noise_id = Column(Integer, nullable=False, default=1)
    project_plan_id = Column(Integer, nullable=False, default=1)
    test_result_id = Column(Integer, nullable=False, default=1)
    multi_result_id = Column(Integer, nullable=False, default=1)
    false_rouse_result_id = Column(Integer, nullable=False, default=1)
    play_config_id = Column(Integer, nullable=False, default=1)
    test_project_id = Column(Integer, nullable=False, default=1)
    running_project_id = Column(String(64), nullable=True, default=None, comment='正在运行的项目id')


class RerunCorpus(Base):
    __tablename__ = "rerun_corpus"

    id = Column(Integer, primary_key=True, autoincrement=True)
    result_id = Column(String(64), unique=True, index=True)
    project_id = Column(String(64), nullable=True, comment='结果对应的项目id')
    plan_id = Column(String(64), nullable=True, comment='该条语料对应的方案id')
    corpus_id = Column(String(64), nullable=True, comment='语料id')
    turn_id = Column(Integer, nullable=False, default=1, comment='第几轮')
    circle = Column(Integer, nullable=False, default=1, comment='配置内大循环第几次')
    corpus_type = Column(String(64), nullable=True, comment='语料类型')


class CorpusLabel(Base):
    __tablename__ = "corpus_label"

    id = Column(Integer, primary_key=True, index=True)
    corpus_id = Column(String(64), nullable=True, comment='语料id')
    label_name = Column(String(256), nullable=True, comment='标签名')


class ExecuteQueue(Base):
    __tablename__ = "execute_queue"

    id = Column(Integer, primary_key=True, index=True)
    execute_status = Column(String(32), nullable=True, comment='执行状态')
    play_config_type = Column(String(32), nullable=True, comment='配置类型')
    data_index_config = Column(String(256), nullable=True, comment='配置内容')
    play_wait = Column(Integer, nullable=True, comment='播放配置流程中的等待')
    project_id = Column(String(32), nullable=True, comment='项目id')
    plan_id = Column(String(32), nullable=True, comment='方案id')
    corpus_id = Column(String(32), nullable=True, comment='语料id')
    circle = Column(Integer, nullable=True, comment='轮次')
    repeat = Column(Integer, nullable=True, comment='重复次数')
    wait_time = Column(Integer, nullable=True, comment='测试过程中无响应时间')
    timeout = Column(Integer, nullable=True, comment='测试过程中超时时间')
    turn = Column(Integer, nullable=True, comment='轮次')
    # relative_interval = Column(Integer, nullable=True, comment='间隔时间')
    # thread_manager = Column(String(64), nullable=True, comment='主线程管理') #可能不需要
    channel = Column(String(64), nullable=True, default='channel1', comment='通道')
    gain = Column(Integer, nullable=False, default=0, comment='增益')
    is_multi = Column(Boolean, default=False, comment='是否为多轮')
    init = Column(Boolean, default=False, comment='是否为初始化')
    video_path = Column(String(256), nullable=True, comment='视频video_path')
    request_id = Column(String(256), nullable=True, comment='request_id')


class DbcFile(Base):
    __tablename__ = "dbc_file"

    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(String(64), unique=True, index=True)
    file_name = Column(String(64), nullable=True, comment='dbc文件名')
    file_path = Column(String(64), nullable=True, comment='dbc文件路径')


class ChannelInfo(Base):
    __tablename__ = "channel_info"

    id = Column(Integer, primary_key=True, index=True)
    channel_id = Column(String(64), unique=True, index=True, comment='通道索引，sdk中是ch_idx')
    channel_name = Column(String(64), nullable=True, comment='通道名称，sdk中是ch_name')
    dev_idx = Column(String(64), nullable=True, comment='设备索引')
    channel_seat = Column(Integer, nullable=True, comment='channel位置')
    mode = Column(String(64), nullable=True, comment='通道类型，sdk中是type，（CAN、ISO CAN FD、NON ISO CAN FD）')
    dbc_file_id = Column(String(64), nullable=True, comment='关联的DBC文件ID')
    bitrate = Column(Integer, nullable=True, default=None, comment='比特率（仅CAN模式）')
    sampling_point = Column(Float, nullable=True, default=None, comment='采样点（仅CAN模式）')
    sjw = Column(Integer, nullable=True, default=None, comment='SJW（仅CAN模式）')
    arbitration_bitrate = Column(Integer, nullable=True, default=None, comment='仲裁段配置（CAN FD模式）比特率')
    arbitration_sampling_point = Column(Float, nullable=True, default=None, comment='仲裁段配置（CAN FD模式）采样点')
    arbitration_sjw = Column(Integer, nullable=True, default=None, comment='仲裁段配置（CAN FD模式）SJW')
    data_bitrate = Column(Integer, nullable=True, default=None, comment='数据段配置（CAN FD模式）比特率')
    data_sampling_point = Column(Float, nullable=True, default=None, comment='数据段配置（CAN FD模式）采样点')
    data_sjw = Column(Integer, nullable=True, default=None, comment='数据段配置（CAN FD模式）SJW')


class CanFrame(Base):
    __tablename__ = "can_frame"

    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(String(64), nullable=True, comment='dbc文件id')
    frame_id = Column(Integer, nullable=True, comment='报文id')
    frame_name = Column(String(64), nullable=True, comment='报文名')
    length = Column(Integer, nullable=True, comment='报文长度，单位byte')
    senders = Column(String(256), nullable=True, comment='信号信息')  # 可能有多个值，如果有多个，在数据库中以,隔开
    attributes = Column(String(256), nullable=True, comment='信号信息')


class CanSignal(Base):
    __tablename__ = "can_signal"

    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(String(64), nullable=True, comment='dbc文件id')
    frame_id = Column(Integer, nullable=True, comment='报文id')
    signal_id = Column(String(64), nullable=True, comment='信号id')  # 这个是唯一id
    signal_name = Column(String(64), nullable=True, comment='信号名')
    signal_info = Column(String(256), nullable=True, comment='信号信息')
