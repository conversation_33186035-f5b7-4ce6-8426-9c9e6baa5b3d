from sqlalchemy import desc

from .base_dao import getDatabaseSession
from app.dao.models import CanSignal, DbcFile, ChannelInfo, CanFrame


class CanQueryDao(object):
    """音频查询类dao"""

    @classmethod
    def findDbcFileById(cls, id: str) -> DbcFile:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(DbcFile).filter(DbcFile.file_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllDbcFile(cls, filter_data: dict = None) -> list[DbcFile]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(DbcFile)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(DbcFile, key):
                        column_attr = getattr(DbcFile, key)
                        query = query.filter(column_attr == value)
            results = query.all()
        return results
    
    @classmethod
    def findChannelInfoById(cls, id: str) -> ChannelInfo:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(ChannelInfo).filter(ChannelInfo.channel_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllChannelInfo(cls, filter_data: dict = None) -> list[ChannelInfo]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(ChannelInfo)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(ChannelInfo, key):
                        column_attr = getattr(ChannelInfo, key)
                        query = query.filter(column_attr == value)
            results = query.all()
        return results

    @classmethod
    def findCanSignalById(cls, id: str) -> CanSignal:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(CanSignal).filter(CanSignal.signal_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllCanSignal(cls, filter_data: dict = None) -> list[CanSignal]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(CanSignal)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(CanSignal, key):
                        column_attr = getattr(CanSignal, key)
                        query = query.filter(column_attr == value)
            results = query.all()
        return results
    
    @classmethod
    def findCanFrameById(cls, id: int) -> CanFrame:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(CanFrame).filter(CanFrame.frame_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllCanFrame(cls, filter_data: dict = None) -> list[CanFrame]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(CanFrame)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(CanFrame, key):
                        column_attr = getattr(CanFrame, key)
                        query = query.filter(column_attr == value)
            results = query.all()
        return results


class CanOperateDao(object):
    """操作音频相关dao"""

    @classmethod
    def saveDbcFile(cls, dbc_file: DbcFile) -> DbcFile:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(dbc_file)
            session.commit()
            session.refresh(dbc_file)
        return dbc_file
    
    @classmethod
    def deleteDbcFile(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            dbc_file = session.query(DbcFile).filter(DbcFile.file_id == id).first()
            if not dbc_file:
                return 
            session.delete(dbc_file)
            session.commit()
            return
        
    @classmethod
    def saveChannelInfo(cls, dbc_file: ChannelInfo) -> ChannelInfo:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(dbc_file)
            session.commit()
            session.refresh(dbc_file)
        return dbc_file

    @classmethod
    def updateChannelInfo(cls, id: str, updated_data: dict) -> ChannelInfo:
        """更新单条"""
        with getDatabaseSession(False) as session:
            can_group = session.query(ChannelInfo).filter(ChannelInfo.channel_id == id).first()
            if not can_group:
                raise ValueError(f"ChannelInfo with id {id} not found.")
            for key, value in updated_data.items():
                if hasattr(can_group, key):
                    setattr(can_group, key, value)
            session.commit()
            session.refresh(can_group)
        return can_group

    @classmethod
    def deleteChannelInfoAll(cls) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            query = session.query(ChannelInfo)
            channel_list = query.all()
            for channel in channel_list:
                session.delete(channel)
                session.commit()
            return

    @classmethod
    def saveCanSignal(cls, can_group: CanSignal) -> CanSignal:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(can_group)
            session.commit()
            session.refresh(can_group)
        return can_group

    @classmethod
    def saveCanSignalList(cls, can_group: list[CanSignal]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(can_group)
        return
    
    @classmethod
    def deleteCanSignal(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            can_group = session.query(CanSignal).filter(CanSignal.signal_id == id).first()
            if not can_group:
                return 
            session.delete(can_group)
            session.commit()
            return

    @classmethod
    def updateCanSignal(cls, id: str, updated_data: dict) -> CanSignal:
        """更新单条"""
        with getDatabaseSession(False) as session:
            can_group = session.query(CanSignal).filter(CanSignal.signal_id == id).first()
            if not can_group:
                raise ValueError(f"CanSignal with id {id} not found.")
            for key, value in updated_data.items():
                if hasattr(can_group, key):
                    setattr(can_group, key, value)
            session.commit()
            session.refresh(can_group)
        return can_group

    @classmethod
    def saveCanFrame(cls, can_group: CanFrame) -> CanFrame:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(can_group)
            session.commit()
            session.refresh(can_group)
        return can_group

    @classmethod
    def saveCanFrameList(cls, can_group: list[CanFrame]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(can_group)
        return
    
    @classmethod
    def deleteCanFrame(cls, id: int) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            can_group = session.query(CanFrame).filter(CanFrame.frame_id == id).first()
            if not can_group:
                return 
            session.delete(can_group)
            session.commit()
            return

    @classmethod
    def updateCanFrame(cls, id: int, updated_data: dict) -> CanFrame:
        """更新单条"""
        with getDatabaseSession(False) as session:
            can_group = session.query(CanFrame).filter(CanFrame.frame_id == id).first()
            if not can_group:
                raise ValueError(f"CanFrame with id {id} not found.")
            for key, value in updated_data.items():
                if hasattr(can_group, key):
                    setattr(can_group, key, value)
            session.commit()
            session.refresh(can_group)
        return can_group