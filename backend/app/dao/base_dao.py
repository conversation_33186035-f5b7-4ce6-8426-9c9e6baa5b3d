from sqlalchemy import create_engine
from app.config import globalAppSettings
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from typing import Optional
from .models import Base
# from .models.trigger import create_table_id
from .models.sqlite_gen import TableId

Session: Optional[sessionmaker] = None


def init_db() -> None:
    global Session
    if Session is None:
        # 创建引擎
        engine = create_engine(
            globalAppSettings.db_dsn,
            echo=globalAppSettings.db_echo_sql,  # 是否打印SQL
            pool_size=globalAppSettings.db_pool_size,  # 连接池的大小，指定同时在连接池中保持的数据库连接数，默认:5
            max_overflow=globalAppSettings.db_max_overflow,  # 超出连接池大小的连接数，超过这个数量的连接将被丢弃,默认: 5
        )
        # 初始化表
        Base.metadata.create_all(engine)

        # 封装获取会话
        Session = sessionmaker(bind=engine, expire_on_commit=False)

        create_table_id()


@contextmanager
def getDatabaseSession(autoCommitByExit=True):
    _session = Session()
    try:
        yield _session
        # 退出时，是否自动提交
        if autoCommitByExit:
            _session.commit()
    except Exception as e:
        _session.rollback()
        raise e
    finally:
        _session.close()


def create_table_id():
    table_id = TableId()
    with getDatabaseSession() as session:
        query = session.query(TableId)
        results = query.all()
        if not results:
            session.add(table_id)
            session.commit()
            session.refresh(table_id)

def update_running_project_id(project_id: str):
    with getDatabaseSession() as session:
        query = session.query(TableId)
        result = query.first()
        setattr(result, "running_project_id", project_id)

        session.commit()
        session.refresh(result)

def find_running_project_id():
    with getDatabaseSession() as session:
        query = session.query(TableId)
        result = query.first()
        res = result.running_project_id
        return res

def find_table_id(id: str):
    with getDatabaseSession() as session:
        query = session.query(TableId)
        result = query.first()
        res = 0
        if id == "corpus_audio_id":
            res = result.corpus_audio_id
            setattr(result, "corpus_audio_id", res + 1)
        if id == "test_corpus_id":
            res = result.test_corpus_id
            setattr(result, "test_corpus_id", res + 1)
        if id == "rouse_corpus_id":
            res = result.rouse_corpus_id
            setattr(result, "rouse_corpus_id", res + 1)
        if id == "disturb_corpus_id":
            res = result.disturb_corpus_id
            setattr(result, "disturb_corpus_id", res + 1)
        if id == "background_noise_id":
            res = result.background_noise_id
            setattr(result, "background_noise_id", res + 1)
        if id == "project_plan_id":
            res = result.project_plan_id
            setattr(result, "project_plan_id", res + 1)
        if id == "test_result_id":
            res = result.test_result_id
            setattr(result, "test_result_id", res + 1)
        if id == "multi_result_id":
            res = result.multi_result_id
            setattr(result, "multi_result_id", res + 1)
        if id == "false_rouse_result_id":
            res = result.false_rouse_result_id
            setattr(result, "false_rouse_result_id", res + 1)
        if id == "play_config_id":
            res = result.play_config_id
            setattr(result, "play_config_id", res + 1)
        if id == "test_project_id":
            res = result.test_project_id
            setattr(result, "test_project_id", res + 1)
        session.commit()
        session.refresh(result)
    return str(res)
