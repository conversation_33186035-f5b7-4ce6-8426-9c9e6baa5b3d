from sqlalchemy import func, Integer, desc
from .base_dao import getDatabaseSession
from app.dao.models import TestResult, MultiResult, FalseRouseResult, RerunCorpus
from ..middleware.log import logger as log


class ResultQueryDao(object):

    @classmethod
    def findTestResultById(cls, id: str) -> TestResult:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestResult).filter(TestResult.result_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllTestResult(cls, filter_data: dict = None) -> tuple[list[TestResult], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(TestResult).order_by(desc(TestResult.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(TestResult, key):
                        if key == "signlist":
                            mask = int(value, 2)
                            query = query.filter(
                                func.cast(
                                    func.replace(TestResult.signlist, "0b", ""),  # 去掉 "0b" 前缀
                                    Integer  # 将字符串转换为整数
                                ).op("&")(mask) == mask  # 位运算匹配
                            )
                        else:
                            query = query.filter(getattr(TestResult, key) == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()

        return results, total

    @classmethod
    def showAllMultiResult(cls, filter_data: dict = None) -> tuple[list[MultiResult], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(MultiResult).order_by(desc(MultiResult.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(MultiResult, key):
                        query = query.filter(getattr(MultiResult, key) == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()
        return results, total
    
    @classmethod
    def findMultiResultById(cls, id: str) -> MultiResult:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(MultiResult).filter(MultiResult.multi_result_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def getTurnListBySet(cls, project_id: str):
        with getDatabaseSession() as session:
            query = session.query(TestResult.turn_id).filter(TestResult.project_id == project_id)
            # Fetch all results with applied filters
            turns = query.all()

        return turns

    @classmethod
    def getMultiTurnListBySet(cls, project_id: str):
        with getDatabaseSession() as session:
            query = session.query(MultiResult.turn_id).filter(MultiResult.project_id == project_id)
            # Fetch all results with applied filters
            turns = query.all()
        return turns

    @classmethod
    def getFalseTurnListBySet(cls, project_id: str):
        with getDatabaseSession() as session:
            query = session.query(FalseRouseResult.turn_id).filter(FalseRouseResult.project_id == project_id)
            # Fetch all results with applied filters
            turns = query.all()
        return turns

    @classmethod
    def getMaxTurnForTestResult(cls, project_id: str) -> int:
        with getDatabaseSession() as session:
            max_turn = session.query(func.max(TestResult.turn_id)).filter(TestResult.project_id == project_id).scalar()
        return max_turn

    @classmethod
    def getMaxTurnForFalseResult(cls, project_id: str) -> int:
        with getDatabaseSession() as session:
            max_turn = session.query(func.max(FalseRouseResult.turn_id)).filter(FalseRouseResult.project_id == project_id).scalar()
        return max_turn

    @classmethod
    def getMaxTurnForMultiResult(cls, project_id: str) -> int:
        with getDatabaseSession() as session:
            max_turn = session.query(func.max(MultiResult.turn_id)).filter(MultiResult.project_id == project_id).scalar()
        return max_turn

    @classmethod
    def findFalseRouseResultById(cls, id: str) -> FalseRouseResult:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(FalseRouseResult).filter(FalseRouseResult.result_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllFalseRouseResult(cls, filter_data: dict = None) ->  tuple[list[FalseRouseResult], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(FalseRouseResult)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(FalseRouseResult, key):
                        query = query.filter(getattr(FalseRouseResult, key) == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()
        return results, total

class ResultOperateDao(object):

    @classmethod
    def saveTestResult(cls, result: TestResult) -> TestResult:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(result)
            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveTestResultList(cls, result: list[TestResult]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(result)
        return

    @classmethod
    def deleteTestResult(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            result = session.query(TestResult).filter(TestResult.result_id == id).first()
            if not result:
                raise ValueError(f"TestResult with id {id} not found.")

            session.delete(result)
            session.commit()

    @classmethod
    def deleteManyTestResult(cls, filters: dict) -> None:
        """删除多条"""
        with getDatabaseSession(False) as session:
            query = session.query(TestResult)
            for k, v in filters.items():
                column_attr = getattr(TestResult, k)
                query = query.filter(column_attr == v)

            query.delete()
            session.commit()

    @classmethod
    def updateTestResult(cls, id: str, updated_data: dict) -> TestResult:
        """更新单条"""
        with getDatabaseSession(False) as session:
            result = session.query(TestResult).filter(TestResult.result_id == id).first()
            if not result:
                log.warn(f"TestResult with id {id} not found.")
                return result

            for key, value in updated_data.items():
                if hasattr(result, key):
                    setattr(result, key, value)

            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveMultiResult(cls, result: MultiResult) -> MultiResult:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(result)
            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveMultiResultList(cls, result: list[MultiResult]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(result)
        return

    @classmethod
    def deleteMultiResult(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            result = session.query(MultiResult).filter(MultiResult.id == id).first()
            if not result:
                raise ValueError(f"MultiResult with id {id} not found.")

            session.delete(result)
            session.commit()

    @classmethod
    def updateMultiResult(cls, id: str, updated_data: dict) -> MultiResult:
        """更新单条"""
        with getDatabaseSession(False) as session:
            result = session.query(MultiResult).filter(MultiResult.id == id).first()
            if not result:
                raise ValueError(f"MultiResult with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(result, key):
                    setattr(result, key, value)

            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def updateMultiResultByMid(cls, multi_id: str, updated_data: dict) -> MultiResult:
        """更新单条"""
        with getDatabaseSession(False) as session:
            result = session.query(MultiResult).filter(MultiResult.multi_result_id == multi_id).first()
            if not result:
                raise ValueError(f"MultiResult with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(result, key):
                    setattr(result, key, value)

            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveFalseRouseResult(cls, result: FalseRouseResult) -> FalseRouseResult:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(result)
            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveFalseRouseResultList(cls, result: list[FalseRouseResult]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(result)
        return

    @classmethod
    def deleteFalseRouseResult(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            result = session.query(FalseRouseResult).filter(FalseRouseResult.id == id).first()
            if not result:
                raise ValueError(f"FalseRouseResult with id {id} not found.")

            session.delete(result)
            session.commit()

    @classmethod
    def updateFalseRouseResult(cls, id: str, updated_data: dict) -> FalseRouseResult:
        """更新单条"""
        with getDatabaseSession(False) as session:
            result = session.query(FalseRouseResult).filter(FalseRouseResult.id == id).first()
            if not result:
                raise ValueError(f"FalseRouseResult with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(result, key):
                    setattr(result, key, value)

            session.commit()
            session.refresh(result)
        return result

    @classmethod
    def saveRerunCorpus(cls, result: RerunCorpus) -> RerunCorpus:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(result)
            session.commit()
            session.refresh(result)
        return result