from sqlalchemy import desc

from .base_dao import getDatabaseSession
from app.dao.models import TestLabelGroup, TestLabel


class LabelQueryDao(object):
    """音频查询类dao"""

    @classmethod
    def findTestLabelGroupById(cls, id: str) -> TestLabelGroup:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestLabelGroup).filter(TestLabelGroup.label_group_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllTestLabelGroup(cls, filter_data: dict = None) -> list[TestLabelGroup]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(TestLabelGroup)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(TestLabelGroup, key):
                        column_attr = getattr(TestLabelGroup, key)
                        if isinstance(value, str):  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
            # Fetch all results with applied filters
            results = query.all()
        return results

    @classmethod
    def findTestLabelById(cls, id: str) -> TestLabel:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestLabel).filter(TestLabel.label_id == id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def showAllTestLabel(cls, filter_data: dict = None) -> list[TestLabel]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(TestLabel)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(TestLabel, key):
                        column_attr = getattr(TestLabel, key)
                        if isinstance(value, str):  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
            # Fetch all results with applied filters
            results = query.all()
        return results

class LabelOperateDao(object):
    """操作音频相关dao"""

    @classmethod
    def saveTestLabelGroup(cls, label_group: TestLabelGroup) -> TestLabelGroup:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(label_group)
            session.commit()
            session.refresh(label_group)
        return label_group

    @classmethod
    def saveTestLabelGroupList(cls, label_group: list[TestLabelGroup]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(label_group)
        return
    
    @classmethod
    def deleteTestLabelGroup(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            label_group = session.query(TestLabelGroup).filter(TestLabelGroup.label_group_id == id).first()
            if not label_group:
                return None
            
            session.delete(label_group)
            session.commit()

    @classmethod
    def updateTestLabelGroup(cls, id: str, updated_data: dict) -> TestLabelGroup:
        """更新单条"""
        with getDatabaseSession(False) as session:
            label_group = session.query(TestLabelGroup).filter(TestLabelGroup.label_group_id == id).first()
            if not label_group:
                raise ValueError(f"TestLabelGroup with id {id} not found.")
            
            for key, value in updated_data.items():
                if hasattr(label_group, key):
                    setattr(label_group, key, value)

            session.commit()
            session.refresh(label_group)
        return label_group

    @classmethod
    def saveTestLabel(cls, label: TestLabel) -> TestLabel:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(label)
            session.commit()
            session.refresh(label)
        return label

    @classmethod
    def saveTestLabelList(cls, label: list[TestLabel]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(label)
        return
    
    @classmethod
    def deleteTestLabel(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            label = session.query(TestLabel).filter(TestLabel.label_id == id).first()
            if not label:
                raise ValueError(f"TestLabel with id {id} not found.")
            
            session.delete(label)
            session.commit()

    @classmethod
    def updateTestLabel(cls, id: str, updated_data: dict) -> TestLabel:
        """更新单条"""
        with getDatabaseSession(False) as session:
            label = session.query(TestLabel).filter(TestLabel.label_id == id).first()
            if not label:
                raise ValueError(f"TestLabel with id {id} not found.")
            
            for key, value in updated_data.items():
                if hasattr(label, key):
                    setattr(label, key, value)

            session.commit()
            session.refresh(label)
        return label