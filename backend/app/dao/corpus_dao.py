from typing import List
from sqlalchemy import func, desc
from .base_dao import getDatabaseSession
from app.dao.models import CorpusAudio, TestCorpus, RouseCorpus, DisturbCorpus, BackgroundNoise, MultiCorpus, \
    CorpusLabel
from app.middleware.log import logger as log


class CorpusQueryDao(object):
    """音频查询类dao"""

    @classmethod
    def findTestCorpusById(cls, id: str) -> TestCorpus:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestCorpus).filter(TestCorpus.corpus_id == id)
            result = query.first()
            if not result:
                # raise ValueError(f"TestCorpus with id {id} not found.")
                return None
        return result

    @classmethod
    def findRouseCorpusById(cls, id: str) -> RouseCorpus:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(RouseCorpus).filter(RouseCorpus.corpus_id == id)
            result = query.first()
            if not result:
                # raise ValueError(f"RouseCorpus with id {id} not found.")
                return None
        return result

    @classmethod
    def findDisturbCorpusById(cls, id: str) -> DisturbCorpus:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(DisturbCorpus).filter(DisturbCorpus.corpus_id == id)
            result = query.first()
            if not result:
                raise ValueError(f"DisturbCorpus with id {id} not found.")
        return result

    @classmethod
    def findBackgroundNoiseById(cls, id: str) -> BackgroundNoise:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(BackgroundNoise).filter(BackgroundNoise.corpus_id == id)
            result = query.first()
            if not result:
                raise ValueError(f"BackgroundNoise with id {id} not found.")
        return result

    @classmethod
    def findAudioById(cls, id: str) -> CorpusAudio:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(CorpusAudio).filter(CorpusAudio.aud_id == id)
            result = query.first()
            if not result:
                # raise ValueError(f"CorpusAudio with id {id} not found.")
                return None
        return result

    @classmethod
    def findAudioInCorpus(cls, id: str) -> TestCorpus:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestCorpus).filter(TestCorpus.aud_id == id)
            result = query.all()
        return result

    # @classmethod
    # def showAllTestCorpus(cls, filter_data: dict = None) -> tuple[list[TestCorpus], int]:
    #     """根据 filter_data 查询所有"""
    #     with getDatabaseSession() as session:
    #         query = session.query(TestCorpus).order_by(desc(TestCorpus.id))
    #         if filter_data:
    #             for key, value in filter_data.items():
    #                 if hasattr(TestCorpus, key):
    #                     column_attr = getattr(TestCorpus, key)
    #                     if isinstance(value, str) and key != "speaker" and key != "signal_id":  # 如果值是字符串，则尝试使用模糊匹配
    #                         query = query.filter(column_attr.like(f"%{value}%"))
    #                     else:  # 否则使用精确匹配
    #                         query = query.filter(column_attr == value)
    #         total = query.count()
    #         page = filter_data.get("page", 0)
    #         page_size = filter_data.get("page_size", 0)
    #         if page and page_size:
    #             offset = (page - 1) * page_size
    #             query = query.offset(offset).limit(page_size)
    #         results = query.all()
    #
    #     return results, total

    @classmethod
    def showAllTestCorpus(cls, filter_data: dict = None) -> tuple[list[TestCorpus], int]:
        """根据 filter_data 查询所有，包括关联的 label 数据"""
        with getDatabaseSession() as session:
            query = session.query(TestCorpus).order_by(desc(TestCorpus.id))
            if filter_data:
                label_filter = None
                for key, value in filter_data.items():
                    if key == 'label' and value:
                        label_filter = value
                    elif hasattr(TestCorpus, key):
                        column_attr = getattr(TestCorpus, key)
                        if isinstance(value, str) and key not in ["speaker", "signal_id"]:
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:
                            query = query.filter(column_attr == value)
                if label_filter is not None:
                    query = query.join(CorpusLabel, TestCorpus.corpus_id == CorpusLabel.corpus_id)
                    query = query.filter(CorpusLabel.label_name.like(f"%{label_filter}%"))
                else:
                    query = query.join(CorpusLabel, TestCorpus.corpus_id == CorpusLabel.corpus_id, isouter=True)
            else:
                query = query.join(CorpusLabel, TestCorpus.corpus_id == CorpusLabel.corpus_id, isouter=True)

            total = query.count()
            page = filter_data.get("page", 0) if filter_data else 0
            page_size = filter_data.get("page_size", 0) if filter_data else 0
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()
        return results, total

    @classmethod
    def showAllRouseCorpus(cls, filter_data: dict = None) -> tuple[list[RouseCorpus], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(RouseCorpus).order_by(desc(RouseCorpus.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(RouseCorpus, key):
                        column_attr = getattr(RouseCorpus, key)
                        if isinstance(value, str) and key != "speaker":  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()

        return results, total

    @classmethod
    def showAllDisturbCorpus(cls, filter_data: dict = None) -> tuple[list[DisturbCorpus], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(DisturbCorpus).order_by(desc(DisturbCorpus.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(DisturbCorpus, key):
                        column_attr = getattr(DisturbCorpus, key)
                        if isinstance(value, str) and key != "speaker":  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()

        return results, total

    @classmethod
    def showAllBackgroundNoise(cls, filter_data: dict = None) -> tuple[list[BackgroundNoise], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(BackgroundNoise).order_by(desc(BackgroundNoise.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(BackgroundNoise, key):
                        column_attr = getattr(BackgroundNoise, key)
                        if isinstance(value, str) and key != "speaker":  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()

        return results, total

    @classmethod
    def findMultiCorpusById(cls, id: str) -> list[MultiCorpus]:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(MultiCorpus).filter(MultiCorpus.corpus_id == id)
            result = query.all()
        return result

    @classmethod
    def showAllMultiCorpus(cls, filter_data: dict = None) -> tuple[list[MultiCorpus], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(MultiCorpus).order_by(desc(MultiCorpus.id))
            if filter_data:
                label_filter = None
                for key, value in filter_data.items():
                    if hasattr(MultiCorpus, key):
                        column_attr = getattr(MultiCorpus, key)
                        if isinstance(value, str) and key != "testcorpus_id":  # 如果值是字符串，则尝试使用模糊匹配
                            query = query.filter(column_attr.like(f"%{value}%"))
                        else:  # 否则使用精确匹配
                            query = query.filter(column_attr == value)
                    elif key == 'label' and value:
                        label_filter = value
                if label_filter is not None:
                    query = query.join(CorpusLabel, MultiCorpus.corpus_id == CorpusLabel.corpus_id)
                    query = query.filter(CorpusLabel.label_name.like(f"%{label_filter}%"))
                else:
                    query = query.join(CorpusLabel, MultiCorpus.corpus_id == CorpusLabel.corpus_id, isouter=True)
            else:
                query = query.join(CorpusLabel, MultiCorpus.corpus_id == CorpusLabel.corpus_id, isouter=True)
            total_subquery = query.with_entities(MultiCorpus.corpus_id).distinct().subquery()
            total_corpus = session.query(func.count()).select_from(total_subquery).scalar()

            # total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                unique_corpus_subquery = (
                    query.with_entities(MultiCorpus.corpus_id)
                    .distinct()
                    .offset((page - 1) * page_size)
                    .limit(page_size)
                    .subquery('unique_corpus')
                )
                query = query.join(
                    unique_corpus_subquery,
                    MultiCorpus.corpus_id == unique_corpus_subquery.c.corpus_id
                )

            results = query.all()
        return results, total_corpus

    @classmethod
    def showAllCorpusLabel(cls, filter_data: dict = None) -> list[CorpusLabel]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(CorpusLabel)
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(CorpusLabel, key):
                        column_attr = getattr(CorpusLabel, key)
                        query = query.filter(column_attr == value)
            # Fetch all results with applied filters
            results = query.all()
        return results


class CorpusOperateDao(object):
    """操作音频相关dao"""

    @classmethod
    def saveCorpusLabel(cls, corpus: CorpusLabel) -> CorpusLabel:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveCorpusLabels(cls, corpus_list: List[CorpusLabel]) -> List[CorpusLabel]:
        """批量添加多条CorpusLabel记录"""
        if not corpus_list:
            return []

        with getDatabaseSession(False) as session:
            # 批量添加所有对象
            session.add_all(corpus_list)
            session.commit()

            # 刷新所有对象以获取数据库生成的ID等属性
            for corpus in corpus_list:
                session.refresh(corpus)

        return corpus_list

    @classmethod
    def deleteCorpusLabel(cls, id: str) -> None:
        """删除label"""
        with getDatabaseSession(False) as session:
            label = session.query(CorpusLabel).filter(CorpusLabel.corpus_id == id)
            label_del = label.all()
            for i in label_del:
                session.delete(i)
                session.commit()

    @classmethod
    def deleteCorpusLabelByName(cls, id, name: str) -> None:
        """删除label"""
        with (getDatabaseSession(False) as session):
            label = session.query(CorpusLabel).filter(CorpusLabel.label_name == name).filter(
                CorpusLabel.corpus_id == id)
            label_del = label.all()
            for i in label_del:
                session.delete(i)
                session.commit()

    @classmethod
    def saveTestCorpus(cls, corpus: TestCorpus) -> TestCorpus:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveTestCorpusList(cls, corpus: list[TestCorpus]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(corpus)
        return

    @classmethod
    def deleteTestCorpus(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(TestCorpus).filter(TestCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"TestCorpus with id {id} not found.")

            session.delete(corpus)
            session.commit()

    @classmethod
    def updateTestCorpus(cls, id: str, updated_data: dict) -> TestCorpus:
        """更新单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(TestCorpus).filter(TestCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"TestCorpus with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(corpus, key):
                    setattr(corpus, key, value)

            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def updateTestCorpusBatchByCorpusId(cls, update_data_list: List[dict]) -> List[TestCorpus]:
        if not update_data_list:
            return []

        ids = []
        for data in update_data_list:
            corpus_id = data.get('corpus_id')
            if corpus_id is None:
                log.error("Missing 'corpus_id' in update data")
                return []
            ids.append(corpus_id)

        with getDatabaseSession(False) as session:
            corpora = session.query(TestCorpus).filter(TestCorpus.corpus_id.in_(ids)).all()
            id_to_corpus = {corpus.corpus_id: corpus for corpus in corpora}

            # 验证所有id对应的记录均存在
            missing_ids = [cid for cid in ids if cid not in id_to_corpus]
            if missing_ids:
                log.error(f"TestCorpus not found for ids: {missing_ids}")
                return []
            # 更新每个记录的字段
            for data in update_data_list:
                corpus_id = data['corpus_id']
                corpus = id_to_corpus[corpus_id]
                for key, value in data.items():
                    if key == 'corpus_id':
                        continue
                    if hasattr(corpus, key):
                        setattr(corpus, key, value)

            session.commit()
            for corpus in corpora:
                session.refresh(corpus)

            updated_corpora = [id_to_corpus[data['corpus_id']] for data in update_data_list]

        return updated_corpora

    @classmethod
    def saveRouseCorpus(cls, corpus: RouseCorpus) -> RouseCorpus:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveRouseCorpusList(cls, corpus: list[RouseCorpus]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(corpus)
        return

    @classmethod
    def deleteRouseCorpus(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(RouseCorpus).filter(RouseCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"RouseCorpus with id {id} not found.")

            session.delete(corpus)
            session.commit()

    @classmethod
    def updateRouseCorpus(cls, id: str, updated_data: dict) -> RouseCorpus:
        """更新单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(RouseCorpus).filter(RouseCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"RouseCorpus with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(corpus, key):
                    setattr(corpus, key, value)

            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveDisturbCorpus(cls, corpus: DisturbCorpus) -> DisturbCorpus:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveDisturbCorpusList(cls, corpus: list[DisturbCorpus]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(corpus)
        return

    @classmethod
    def deleteDisturbCorpus(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(DisturbCorpus).filter(DisturbCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"DisturbCorpus with id {id} not found.")

            session.delete(corpus)
            session.commit()

    @classmethod
    def updateDisturbCorpus(cls, id: str, updated_data: dict) -> DisturbCorpus:
        """更新单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(DisturbCorpus).filter(DisturbCorpus.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"DisturbCorpus with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(corpus, key):
                    setattr(corpus, key, value)

            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveBackgroundNoise(cls, corpus: BackgroundNoise) -> BackgroundNoise:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveBackgroundNoiseList(cls, corpus: list[BackgroundNoise]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(corpus)
        return

    @classmethod
    def deleteBackgroundNoise(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(BackgroundNoise).filter(BackgroundNoise.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"BackgroundNoise with id {id} not found.")

            session.delete(corpus)
            session.commit()

    @classmethod
    def updateBackgroundNoise(cls, id: str, updated_data: dict) -> BackgroundNoise:
        """更新单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(BackgroundNoise).filter(BackgroundNoise.corpus_id == id).first()
            if not corpus:
                raise ValueError(f"BackgroundNoise with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(corpus, key):
                    setattr(corpus, key, value)

            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveAudio(cls, audio: CorpusAudio) -> CorpusAudio:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(audio)
            session.commit()
            session.refresh(audio)
        return audio

    @classmethod
    def saveAudioList(cls, audio: list[CorpusAudio]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(audio)
        return

    @classmethod
    def deleteAudio(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            audio = session.query(CorpusAudio).filter(CorpusAudio.aud_id == id).first()
            if not audio:
                pass

            session.delete(audio)
            session.commit()

    @classmethod
    def updateAudio(cls, id: str, updated_data: dict) -> CorpusAudio:
        """更新单条"""
        with getDatabaseSession(False) as session:
            audio = session.query(CorpusAudio).filter(CorpusAudio.aud_id == id).first()
            if not audio:
                raise ValueError(f"Audio with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(audio, key):
                    setattr(audio, key, value)

            session.commit()
            session.refresh(audio)
        return audio

    @classmethod
    def saveMultiCorpus(cls, corpus: MultiCorpus) -> MultiCorpus:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(corpus)
            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def saveMultiCorpusList(cls, corpus: list[MultiCorpus]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(corpus)
        return

    @classmethod
    def deleteMultiCorpus(cls, id: str) -> None:
        """删除所有corpus_id对应的数据"""
        with getDatabaseSession(False) as session:
            corpus = session.query(MultiCorpus).filter(MultiCorpus.corpus_id == id)
            a_del = corpus.all()
            for i in a_del:
                session.delete(i)
                session.commit()

    @classmethod
    def deleteSingleMultiCorpus(cls, id: str) -> None:
        """删除所有corpus_id对应的数据"""
        with getDatabaseSession(False) as session:
            corpus = session.query(MultiCorpus).filter(MultiCorpus.id == id).first()
            if not corpus:
                pass
            session.delete(corpus)
            session.commit()

    @classmethod
    def updateMultiCorpus(cls, id: str, updated_data: dict) -> MultiCorpus:
        """更新单条"""
        with getDatabaseSession(False) as session:
            corpus = session.query(MultiCorpus).filter(MultiCorpus.id == id).first()
            if not corpus:
                raise ValueError(f"MultiCorpus with id {id} not found.")

            for key, value in updated_data.items():
                if hasattr(corpus, key):
                    setattr(corpus, key, value)

            session.commit()
            session.refresh(corpus)
        return corpus

    @classmethod
    def updateMultiCorpusBatchById(cls, update_data_list: List[dict]) -> List[MultiCorpus]:
        if not update_data_list:
            return []

        ids = []
        for data in update_data_list:
            corpus_id = data.get('id')
            if corpus_id is None:
                log.error("Missing 'id' in update data")
                return []
            ids.append(corpus_id)

        with getDatabaseSession(False) as session:
            corpora = session.query(MultiCorpus).filter(MultiCorpus.id.in_(ids)).all()
            id_to_corpus = {corpus.id: corpus for corpus in corpora}

            # 验证所有id对应的记录均存在
            missing_ids = [cid for cid in ids if cid not in id_to_corpus]
            if missing_ids:
                log.error(f"MultiCorpus not found for ids: {missing_ids}")
                return []
            # 更新每个记录的字段
            for data in update_data_list:
                corpus_id = data['id']
                corpus = id_to_corpus[corpus_id]
                for key, value in data.items():
                    if key == 'id':
                        continue
                    if hasattr(corpus, key):
                        setattr(corpus, key, value)

            session.commit()
            for corpus in corpora:
                session.refresh(corpus)

            updated_corpora = [id_to_corpus[data['id']] for data in update_data_list]

        return updated_corpora
