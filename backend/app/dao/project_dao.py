from sqlalchemy import desc

from .base_dao import getDatabaseSession
from app.dao.models import TestProject, ExecuteQueue


class ProjectQueryDao(object):
    """音频查询类dao"""

    @classmethod
    def findTestProjectById(cls, id: str, turn_id=None) -> TestProject:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(TestProject).filter(TestProject.project_id == id)
            if turn_id:
                query = query.filter(TestProject.turn_id == turn_id)
            result = query.first()
            if not result:
                return None
        return result

    @classmethod
    def findTestProjectsByIdList(cls, id_list: list[str]) -> list[TestProject]:
        if not id_list:
            return []

        unique_ids = list(set(id_list))

        with getDatabaseSession() as session:
            query = session.query(TestProject).filter(
                TestProject.project_id.in_(unique_ids)
            )
            results = query.all()
        return results

    @classmethod
    def showAllTestProject(cls, filter_data: dict = None) -> tuple[list[TestProject], int]:
        """根据 filter_data 查询所有"""
        with getDatabaseSession() as session:
            query = session.query(TestProject).order_by(desc(TestProject.id))
            if filter_data:
                for key, value in filter_data.items():
                    if hasattr(TestProject, key):
                        column_attr = getattr(TestProject, key)
                        # if isinstance(value, str):  # 如果值是字符串，则尝试使用模糊匹配
                        #     query = query.filter(column_attr.like(f"%{value}%"))
                        # else:  # 否则使用精确匹配
                        query = query.filter(column_attr == value)
            total = query.count()
            page = filter_data.get("page", 0)
            page_size = filter_data.get("page_size", 0)
            if page and page_size:
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
            results = query.all()
            
        return results, total

    @classmethod
    def findExecuteQueueFirst(cls, id: str) -> ExecuteQueue:
        """单条查询示例"""
        with getDatabaseSession() as session:
            query = session.query(ExecuteQueue).filter(ExecuteQueue.project_id == id)
            result = query.first()
            if not result:
                return None
        return result


class ProjectOperateDao(object):
    """操作音频相关dao"""

    @classmethod
    def saveTestProject(cls, project: TestProject) -> TestProject:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(project)
            session.commit()
            session.refresh(project)
        return project

    @classmethod
    def saveTestProjectList(cls, project: list[TestProject]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(project)
        return
    
    @classmethod
    def deleteTestProject(cls, id: str) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            project = session.query(TestProject).filter(TestProject.project_id == id).first()
            if not project:
                raise ValueError(f"TestProject with id {id} not found.")
            
            session.delete(project)
            session.commit()

    @classmethod
    def updateTestProject(cls, id: str, updated_data: dict) -> TestProject:
        """更新单条"""
        with getDatabaseSession(False) as session:
            project = session.query(TestProject).filter(TestProject.project_id == id).first()
            if not project:
                raise ValueError(f"TestProject with id {id} not found.")
            
            for key, value in updated_data.items():
                if hasattr(project, key):
                    setattr(project, key, value)

            session.commit()
            session.refresh(project)
        return project

    @classmethod
    def saveExecuteQueue(cls, execute_queue: ExecuteQueue) -> ExecuteQueue:
        """添加单条"""
        with getDatabaseSession(False) as session:
            session.add(execute_queue)
            session.commit()
            session.refresh(execute_queue)
        return execute_queue

    @classmethod
    def saveExecuteQueueList(cls, execute_queue: list[ExecuteQueue]):
        """添加多条"""
        with getDatabaseSession() as session:
            session.bulk_save_objects(execute_queue)
        return
    
    @classmethod
    def deleteExecuteQueue(cls, execute_queue: ExecuteQueue) -> None:
        """删除单条"""
        with getDatabaseSession(False) as session:
            session.delete(execute_queue)
            session.commit()

    @classmethod
    def deleteExecuteQueuebyProjectId(cls, id: str) -> None:
        """ 删除 project_id 对应的 execute_queue """
        with getDatabaseSession() as session:
            delete_query = session.query(ExecuteQueue).filter(ExecuteQueue.project_id == id)
            delete_query.delete(synchronize_session=False)
            session.commit()
            # query = session.query(ExecuteQueue).filter(ExecuteQueue.project_id == id)
            # for execute_queue in query:
            #     session.delete(execute_queue)
            #     session.commit()

    @classmethod
    def truncateExecuteQueue(cls) -> None:
        """ 清空整个 ExecuteQueue 表 """
        with getDatabaseSession() as session:
            session.query(ExecuteQueue).delete()
            session.commit()
