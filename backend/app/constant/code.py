from enum import Enum


class StatusCode(Enum):
    OK = (0, '成功')
    PARAM_ERROR = (1, "参数错误")
    EXECUTE_FAIL = (2, "执行失败")

    PROJECT_INIT_FAIL = (10, '项目初始化失败')
    PROJECT_INIT_HAVE_CONFIG = (11, '项目初始化失败, 项目里已经有配置')
    PROJECT_INIT_NO_ROUSE = (12, '项目初始化没有填写唤醒或者退出语音')

    # 语料管理相关错误
    CORPUS_IN_USE = (20, "语料在配置中")

    # 播放配置相关错误
    PLAY_CONFIG_IN_USE = (20, "播放配置在使用中")

    @property
    def code(self):
        return self.value[0]

    @property
    def errmsg(self):
        return self.value[1]
