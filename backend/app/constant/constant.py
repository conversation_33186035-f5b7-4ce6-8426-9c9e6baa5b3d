from app.dao.models import PlayConfig


class Vocalists:
    male = 1
    female = 2
    boy = 3
    girl = 4

class Language:
    mandarin = 1        # 普通话
    cantonese = 2       # 粤语
    taiwanese = 3       # 台湾话
    northeastern = 4    # 东北话
    shaanxi = 5         # 陕西话

class SynthesizeType:
    test = 1
    rouse = 2

class SignType:
    # 唤醒异常
    rouse_asr_abnormal = 1   # 唤醒应答异常
    rouse_time_abnormal = 2    # 唤醒时间异常
    # 交互异常
    result_need_reviewed = 3   # 需复核结果
    result_need_attention = 4  # 重点关注
    # 其他异常
    response_time_abnormal = 5 # 响应时间异常
    asr_empty = 6              # asr输出为空
    word_accuracy_abnormal = 7 # 字识别准确率异常
    manual_review_correction = 8 # 人工复核修正


class PlayConfigItemsType:
    start = "开始"

class PlayConfigType:
    interaction = "interaction"  # 单论
    rouse = "rouse"              # 唤醒
    false_rouse = "false-rouse"  # 误唤醒
    multi = "interaction-multi"  # 多轮

# 单轮播放配置
class SinglePlayConfig:
    # start
    start_interval_wait_time = "interval_wait_time" # 间隔等待时间

# 唤醒配置
class RousePlayConfig:
    start_type = "开始"

# 误唤醒配置
class FalseRousePlayConfig:
    start_type = "开始"

# 多轮播放配置
class MultiPlayConfig:
    start_type = "开始"
