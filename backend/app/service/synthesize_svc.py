import os.path
import librosa
import soundfile as sf
import pyloudnorm as pyln
from app.config import globalAppSettings
from app.middleware.log import logger as log
from app.utils.tts.dialects_tts import DialectsTTS
from app.constant import SynthesizeType


def normalize_loudness(input_path, target_lufs=-23.0, output_path=None):
    # 读取音频数据
    data, rate = librosa.load(input_path, sr=None, mono=True)

    # 创建响度测量器
    meter = pyln.Meter(rate)

    # 测量当前响度（LUFS）
    current_loudness = meter.integrated_loudness(data)

    # 计算增益差值
    gain_diff = target_lufs - current_loudness

    # 应用增益调整
    normalized_data = pyln.normalize.loudness(data, current_loudness, target_lufs)

    # 保存结果（默认覆盖原文件）
    if output_path is None:
        output_path = input_path
    sf.write(output_path, normalized_data, rate)
    return output_path


class SynthesizeSvc:
    synthesize_corpus_path = globalAppSettings.synthesize_corpus_path
    rouse_corpus_path = globalAppSettings.rouse_corpus_path
    # tts = AlTTS()
    # chat_tts = Chattts()
    dialects_tts = DialectsTTS()

    def judge(self, voice, language):
        # if voice == Vocalists.female and language >= 8:
        #     return self.dialects_tts
        # if voice == Vocalists.male and language >= 7:
        #     return self.dialects_tts
        # 先使用阿里tts,后续考虑切换
        return self.dialects_tts

    def synthesize(self, text, name, voice, language, synthesize_type, tts_tone=False, is_normalize_loudness=False):
        if '.' not in name:
            name = name + ".mp3"
        if synthesize_type == SynthesizeType.rouse:
            audio_path = os.path.join(self.rouse_corpus_path, name)
        else:
            audio_path = os.path.join(self.synthesize_corpus_path, name)
        if os.path.exists(audio_path):
            log.warn("synthesize fail. name is exist")
            return 1, ""
        tts = self.judge(voice, language)
        ret = tts.convert_with_voice(text, voice, language, audio_path, tts_tone)
        if ret != 0:
            log.warn("synthesize fail, ret={}".format(ret))
        if not os.path.exists(audio_path):
            log.warn(f"synthesize fail not audio file, path:{audio_path}, text:{text}")
        if is_normalize_loudness:
            normalize_loudness(audio_path)
        return ret, audio_path
