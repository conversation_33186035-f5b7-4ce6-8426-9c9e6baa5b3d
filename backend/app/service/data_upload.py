from app.dao.models.sqlite_gen import TestResult
from minio import Minio
import zipfile
import shutil
from app.config.app_config import globalAppSettings
from app.utils.utils import get_mac_address
import os
import time
import hashlib
from app.dao.result_dao import Result<PERSON>ueryDao, ResultOperateDao
from datetime import datetime, timedelta
import random
import string
import threading
import queue
import requests
from app.middleware.log import logger as log
from app.service.minio_svc import put_file, get_app_start_id

DataUpload_Queue = queue.Queue()
Upload_Queue_lock = threading.Lock()

MINIO_URL = f"http://{globalAppSettings.MINIO_ENDPOINT}"

client = Minio(
    endpoint=globalAppSettings.MINIO_ENDPOINT,
    access_key=globalAppSettings.MINIO_ACCESS_KEY,
    secret_key=globalAppSettings.MINIO_SECRET_KEY,
    secure=False,
)


def send_post_request(data):
    url = f"{globalAppSettings.CVA_DATA_SERVER}/cva/update_data"

    try:
        response = requests.post(url, json=data)
        # 记录响应状态
        log.debug(
            f"data={data} 状态码={response.status_code} # 响应数据={response.json()}"
        )
    except requests.exceptions.RequestException as e:
        log.error(f" 请求发生错误:{e}")


def generate_random_string16():
    # 获取当前时间戳作为随机种子
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random.seed(timestamp)  # 设置随机种子
    characters = string.ascii_letters + string.digits  # 字母和数字
    random_string = ''.join(random.choices(characters, k=16))
    return random_string


def compress_folder_to_zip(folder_path, output_zip_path=None):
    if not os.path.exists(folder_path):
        return None

    if not os.path.isdir(folder_path):
        return None

    # 如果未指定输出路径，则默认使用文件夹名生成zip文件
    if output_zip_path is None:
        output_zip_path = f"{folder_path.rstrip(os.sep)}.zip"

    with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                # 将文件相对于folder_path的相对路径写入压缩包
                arcname = os.path.relpath(file_path, folder_path)
                zipf.write(file_path, arcname)

    return output_zip_path


# 结合上述三条
def upload_data(test_result_data, aud_url, config_data):
    pic_path = os.path.dirname(test_result_data.image)
    mic_path = os.path.dirname(test_result_data.mic_audio_url)
    # shutil.copy(aud_url, mic_path)
    pic_zip_path = compress_folder_to_zip(pic_path)
    mic_zip_path = compress_folder_to_zip(mic_path)
    pic_rel_path = os.path.relpath(pic_path, globalAppSettings.base_dir)
    mic_rel_path = os.path.relpath(mic_path, globalAppSettings.base_dir)
    # 调服务器接口传输数据
    data_id = get_data_id()
    data = {
        "data_id": data_id,
        "result_id": test_result_data.result_id,
        "project_id": test_result_data.project_id,
        "plan_id": test_result_data.plan_id,
        "turn_id": test_result_data.turn_id,
        "test_scenario": test_result_data.test_scenario,
        "text": test_result_data.text,
        "ocr_result": test_result_data.ocr_result,
        "ocr_accuracy_rate": test_result_data.ocr_accuracy_rate,
        "response_time": test_result_data.response_time,
        "expect_result": test_result_data.expect_result,
        "image_confidence": test_result_data.image_confidence,
        "config_data": config_data
    }
    pic_url = put_file(pic_zip_path, "picture", pic_rel_path)
    if pic_url:
        data["pic_url"] = f"{MINIO_URL}/{pic_url}"
    mic_url = put_file(mic_zip_path, "audio", mic_rel_path)
    if mic_url:
        data["mic_url"] = f"{MINIO_URL}/{mic_url}"
    send_post_request(data)
    os.remove(pic_zip_path)
    os.remove(mic_zip_path)


def need_upload(test_result_data):
    if globalAppSettings.data_upload == 1:
        return 1
    elif globalAppSettings.data_upload == 2:
        if not test_result_data.image_confidence and not test_result_data.llm_ui_confidence and not test_result_data.llm_voice_confidence:
            return 1

        if test_result_data.image_confidence and test_result_data.image_confidence < globalAppSettings.ocr_confidence_threshold:
            return 1
        if test_result_data.llm_ui_confidence and test_result_data.llm_ui_confidence < globalAppSettings.llm_confidence_threshold:
            return 1
        if test_result_data.llm_voice_confidence and test_result_data.llm_voice_confidence < globalAppSettings.llm_confidence_threshold:
            return 1
        return 0
    else:
        return 0


def upload_data_thread():
    id_save_path = os.path.join(globalAppSettings.base_dir, "scripts", "cva_data_id.txt")
    data_id = get_data_id()
    with open(id_save_path, "a+") as f:
        f.writelines(data_id + "\n")
    while True:
        try:
            if not DataUpload_Queue.empty():
                data = DataUpload_Queue.get()  # 从队列中取出 Data 对象
                if isinstance(data[0], TestResult):
                    if need_upload(data[0]) and len(data) >= 3:
                        upload_data(data[0], data[1], data[2])
                DataUpload_Queue.task_done()  # 标记任务已完成
        except Exception as e:
            log.warn(f"upload data error: {e}")
        finally:
            time.sleep(1)


def get_data_id():
    dir_path = globalAppSettings.base_dir
    abs_path = os.path.abspath(dir_path)
    normalized_path = abs_path.replace(os.path.sep, '/').lower()
    path_hash = hashlib.md5(normalized_path.encode('utf-8')).hexdigest()
    data_id = f"{get_mac_address()}_{path_hash}_{get_app_start_id()}"
    return data_id


# 结果分析完成后上传结果，比如计算的 asr结果、响应时间，和模型分析结果等
def upload_result(result_id):
    result = ResultQueryDao.findTestResultById(result_id)
    if not need_upload(result):
        return
    if result is not None:
        # 调服务器接口传输数据
        data_id = get_data_id()
        data = {
            "data_id": data_id,
            "result_id": result.result_id,
            "project_id": result.project_id,
            "plan_id": result.plan_id,
            "turn_id": result.turn_id,
            "result": result.result,
            "score": result.score,
            "reason": result.reason,
            "llm_ui_confidence": result.llm_ui_confidence,
            "llm_voice_confidence": result.llm_voice_confidence,
            "response_time": result.response_time,
            "asr_result": result.asr_result,
        }
        send_post_request(data)
