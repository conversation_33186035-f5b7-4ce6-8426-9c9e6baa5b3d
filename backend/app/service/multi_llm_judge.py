from datetime import datetime
import json
import dspy
from typing import Iterable, Literal, List, Dict, Any
from minio import Minio
from openai.types.chat.chat_completion_message_param import ChatCompletionMessageParam
import os
from app.middleware.log import logger as log
from app.dao.result_dao import ResultQueryDao, ResultOperateDao
from app.dao.models.sqlite_gen import MultiResult
from app.service.llm_judge import EvaluateSmartCockpit

client = Minio(
    endpoint="183.66.251.10:39000",
    access_key="qHGNbLRWtJcfJsDkl3LK",
    secret_key="qmkA57ujunxw2cEpmPCVfsHKtXdbRAfv0SYvtdQC",
    secure=False,
)

def put_file(file, bucket_name, level1_dir, level2_dir, level3_dir):
    """
    上传文件到指定的 MinIO bucket
    """
    file_name = os.path.basename(file)  # 获取文件名
    found = client.bucket_exists(bucket_name)  # 检查 bucket 是否存在
    if not found:
        client.make_bucket(bucket_name)  # 创建 bucket

    # 生成包含两级目录的路径
    object_name = f"{level1_dir}/{level2_dir}/{level3_dir}/{file_name}"

    # 上传文件到指定路径
    client.fput_object(
        bucket_name=bucket_name,
        object_name=object_name,
        file_path=file
    )
    return f"{bucket_name}/{object_name}"

class DialogueSessionEvalSignature(dspy.Signature):
    """评估整个多轮（至少1轮）对话会话是否通过.请注意:只有所有单轮对话都通过，才可以判断该多轮对话为通过。

        # 在依据 认知操作步骤 和 裁决规则 的基础上多考虑多轮对话的合理性，遵从下方综合分析以及注意事项并详细输出分析原因
        ##综合分析:

        指令理解和执行的准确率，包括是否准确理解用户的需求和正确执行操作。

        多轮对话的连贯性，检查系统是否能够持续保持对话的上下文，理解用户的连续请求。

        系统响应的一致性，确保在不同轮次中系统的响应逻辑一致，尤其是同一主题下的连续对话。

        整体交互体验，评估对话的自然流畅程度，以及系统与用户的互动是否令人满意。

        ##注意事项:

        多轮对话中的指令遵循：多轮对话中的指令遵循准确率是否随着轮次的增加保持稳定，是否存在显著下降或偏差。

        多轮对话中的指令保持：后续轮次中是否能继续遵循前一轮成功执行的指令或主题，确保对话的连续性和一致性。

        多轮对话中的自我纠正：如果之前的回答中存在错误，系统是否能够在后续对话中主动进行自我纠正。

        用户意图的正确追踪：在多轮对话中，系统是否能够持续准确地追踪用户意图，避免误解或偏离。

        动态调整：系统在面对用户需求变化时，是否能够灵活调整响应策略并继续保持对话流畅。
    """

    single_turn_evals: List[Dict[str, Any]] = dspy.InputField(desc="各轮对话的评估结果")

    session_result: Literal["通过", "失败"] = dspy.OutputField(desc="会话整体评估结果")
    session_reason: str = dspy.OutputField(desc="总体评价理由")
    command_success_rate: float = dspy.OutputField(desc="成功执行指令的比率：成功执行的轮次/总轮次")

class CockpitAssistantEvaluator(dspy.Module):
    def __init__(self):
        super().__init__()
        self.single_turn_eval = EvaluateSmartCockpit()
        self.session_eval = dspy.ChainOfThought(DialogueSessionEvalSignature)

    def forward(self, messages: Iterable[ChatCompletionMessageParam]):
        single_turn_evals = []
        session_results = "通过"
        session_reasons = ""
        single_nums = len(messages) // 2
        fail_nums = 0

        # 逐轮评估
        for i in range(0, len(messages) - 1, 2):
            # 获取对话历史
            dialogue_history = messages[:i] if i > 0 else []

            # 获取当前轮次的用户和系统消息
            user_message = messages[i]
            assistant_message = messages[i + 1]

            try:
                # 提取文本内容
                user_text = next(
                    (item["text"] for item in user_message["content"] if item["type"] == "text"), "无文本内容"
                )
                assistant_text = next(
                    (item["text"] for item in assistant_message["content"] if item["type"] == "text"), "无文本内容"
                )

                expected_behavior = next(
                    (
                        item["text"]
                        for item in user_message["content"][1:]
                        if item["type"] == "text"
                    ),
                    "无文本内容",
                )

                # 提取界面截图URL
                image_urls = [
                    item["image_url"]["url"]
                    for item in assistant_message["content"]
                    if item["type"] == "image_url"
                ]

                if len(image_urls) < 2:
                    raise ValueError("缺少必要的两张界面截图")

                pre_interaction_screenshot = dspy.Image.from_url(image_urls[0])
                post_interaction_screenshot = dspy.Image.from_url(image_urls[1])

                # 执行单轮评估
                single_result = self.single_turn_eval.forward(
                    user_question=user_text,
                    expected_behavior=expected_behavior,
                    asr_response=assistant_text,
                    pre_interaction_screenshot=pre_interaction_screenshot,
                    post_interaction_screenshot=post_interaction_screenshot,
                )

                # 转换结果
                eval_result = "通过" if single_result.judgment == "Correct" else "失败"

                # 累积失败统计
                if eval_result == "失败":
                    session_results = "失败"
                    fail_nums += 1

                session_reasons += single_result.reasoning

            except Exception as e:
                raise Exception(f"评估第{i // 2 + 1}轮对话时出错: {str(e)}")

            single_turn_evals.append(
                {
                    "turn_num": (i // 2) + 1,
                    "eval_result": eval_result,
                    "eval_reason": single_result.reasoning,
                    "eval_confidence": 1.0,
                }
            )

        # 会话整体评估
        command_success_rate = (single_nums - fail_nums) / single_nums

        if session_results == "失败":
            session_eval_result = dspy.Prediction(
                session_result=session_results,
                session_reason=session_reasons,
                command_success_rate=command_success_rate,
            )
        else:
            session_eval_result = self.session_eval(single_turn_evals=single_turn_evals)

        save_evaluation_results(messages, single_turn_evals, session_eval_result)

        return single_turn_evals, session_eval_result


def save_evaluation_results(
    messages, single_turn_evals, session_eval_result, output_path=os.path.join(os.path.expanduser("~"), ".cache", "evaluation_results.jsonl")
):
    """保存评估结果到文件，以jsonl格式追加写入"""
    results = {
        "messages": messages,
        "single_turn_evaluations": single_turn_evals,
        "session_evaluation": {
            "result": session_eval_result.session_result,
            "reason": session_eval_result.session_reason,
            "success_rate": session_eval_result.command_success_rate,
        },
        "timestamp": datetime.now().isoformat(),
    }

    with open(output_path, "a", encoding="utf-8") as f:
        f.write(json.dumps(results, ensure_ascii=False) + "\n")

def print_evaluation_results(result_list, multi_result, single_turn_evals, session_eval_result):
    """格式化打印评估结果"""
    print("\n=== 智慧座舱大模型指令遵循能力评估 ===")

    print("\n--- 各轮对话评估 ---")
    num = 0
    for eval_result in single_turn_evals:
        # print(f"\n第 {eval_result['turn_num']} 轮:")
        # print(f"结果: {eval_result['eval_result']}")
        # print(f"理由: {eval_result['eval_reason']}")
        # print(f"置信度: {eval_result['eval_confidence']:.2f}")
        res = eval_result['eval_result']
        if res == "通过":
            update_data = {"result": "通过", "score": 10, "reason": eval_result['eval_reason']}
        elif res == "失败":
            update_data = {"result": "不通过", "score": 0, "reason": eval_result['eval_reason']}
        else:
            update_data = {"result": "不确定", "score": 5, "reason": eval_result['eval_reason']}
        ResultOperateDao.updateTestResult(result_list[num]["result_id"], update_data)
        num += 1
        log.info(f"\n第 {eval_result['turn_num']} 轮: 结果: {res} 理由: {eval_result['eval_reason']} 置信度: {eval_result['eval_confidence']:.2f}")

    log.info("\n--- 整体会话评估 ---")
    # print(f"最终结果: {session_eval_result.session_result}")
    # print(f"评估理由: {session_eval_result.session_reason}")
    # print(f"指令执行成功率: {session_eval_result.command_success_rate:.2%}")
    update_multi_res = {
        "result": session_eval_result.session_result,
        "reason": session_eval_result.session_reason,
        "success_rate": session_eval_result.command_success_rate
    }
    ResultOperateDao.updateMultiResult(multi_result.id, update_multi_res)
    
    log.info(f"最终结果: {session_eval_result.session_result} 评估理由: {session_eval_result.session_reason} 指令执行成功率: {session_eval_result.command_success_rate:.2%}")


def multi_llm_judge_result(result_list, multi_result):
    """  
    输入为结果列表
    [{},{}]
    """  
    try:
        lm = dspy.LM(
            "openai/WestGenesis-LLM",
            cache=False,
            api_key="4030cf3413434fbad5c7563005b73a",
            api_base="http://183.66.251.10:38000/v1",
            seed=200
        )
        dspy.configure(lm=lm)

        # 初始化评估器
        evaluator = CockpitAssistantEvaluator()
    except Exception as e:
        return 'Connect Error'

    messages = []
    for result in result_list:
        user_question = result["user_question"]
        expected_behavior = result["expected_behavior"]
        asr_response = result["asr_response"]
        pre_interaction_screenshot = result["pre_interaction_screenshot"]
        post_interaction_screenshot = result["post_interaction_screenshot"]
        project_id = result["project_id"]
        turn_id = result["turn_id"]
        corpus_id = result["corpus_id"]
        image_url1 = put_file(pre_interaction_screenshot, "picture", project_id, turn_id, corpus_id)
        image_url2 = put_file(post_interaction_screenshot, "picture", project_id, turn_id, corpus_id)
        log.info(f" 项目id={project_id} # 轮次={turn_id} # 语料文本/用户指令={user_question} TEXT send to llm : 车机回复={asr_response}, 期望结果={expected_behavior}, pre_image={pre_interaction_screenshot}, post_image={post_interaction_screenshot}")
        user = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": user_question,
                },
                {
                    "type": "text",
                    "text": expected_behavior,
                }
            ],
        }
        car = {
            "role": "assistant",
            "content": [
                {
                    "type": "text",
                    "text": asr_response,
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"http://183.66.251.10:39000/{image_url1}",
                    },
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"http://183.66.251.10:39000/{image_url2}",
                    },
                }
            ],
        }
        messages.append(user)
        messages.append(car)

    # 执行评估
    single_turn_evals, session_eval_result = evaluator.forward(messages)

    # 输出结果
    print_evaluation_results(result_list, multi_result, single_turn_evals, session_eval_result)
    return ''