import os
import time
from pathlib import Path
from minio import Minio
from app.utils.utils import get_mac_address

app_start_id = ""


def get_app_start_id():
    global app_start_id
    if app_start_id == "":
        app_start_id = str(int(time.time()))
    return app_start_id


MINIO_URL = "http://*************:39000"
MINIO_CLIENT = Minio(
    endpoint="*************:39000",
    access_key="qHGNbLRWtJcfJsDkl3LK",
    secret_key="qmkA57ujunxw2cEpmPCVfsHKtXdbRAfv0SYvtdQC",
    secure=False,
)


def path_to_underscore_str(path):
    normalized_path = os.path.normpath(path)
    parts = normalized_path.split(os.sep)
    filtered_parts = [part for part in parts if part]
    return "-".join(filtered_parts)


def underscore_str_to_path(s, is_absolute=True):
    parts = s.split("-")
    if is_absolute:
        path = Path(os.path.sep).joinpath(*parts)
    else:
        path = Path().joinpath(*parts)
    return os.path.normpath(str(path))


def put_file(file, bucket_name, rel_path):
    """
    上传文件到指定的 MinIO bucket
    """
    if not file or not os.path.exists(file):
        return ""
    file_name = os.path.basename(file)  # 获取文件名
    found = MINIO_CLIENT.bucket_exists(bucket_name)  # 检查 bucket 是否存在
    if not found:
        MINIO_CLIENT.make_bucket(bucket_name)  # 创建 bucket

    mac_address = get_mac_address()
    app_start_id = get_app_start_id()
    # 生成包含两级目录的路径
    object_name = f"{mac_address}/{app_start_id}/{path_to_underscore_str(rel_path)}/{file_name}"

    # 上传文件到指定路径
    MINIO_CLIENT.fput_object(bucket_name=bucket_name, object_name=object_name, file_path=file)
    return f"{bucket_name}/{object_name}"
