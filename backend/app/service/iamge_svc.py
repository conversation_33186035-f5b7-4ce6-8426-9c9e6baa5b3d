import json
import os.path
import threading
import time

import jieba
import Levenshtein
import cv2
import re
import numpy as np
import glob
import xiangsi as xs

from PIL import Image
from matplotlib import pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from app.config import globalAppSettings
from app.utils.ocr.paddleocr import PadOcr
from app.utils.camera import Camera
from app.middleware.log import set_request_id, logger as log
from app.config.device_config import deviceConfig


FULL_MATCH_MAP = {}

def arabic_to_chinese(s):
    # 定义数字和中文数字的映射
    arabic_numerals = "0123456789"
    chinese_numerals = "零一二三四五六七八九"

    # 创建一个字典来映射阿拉伯数字到中文数字
    num_dict = {arabic_numerals[i]: chinese_numerals[i] for i in range(10)}

    # 结果字符串
    result = []

    # 遍历输入字符串中的每个字符
    for char in s:
        if char in num_dict:
            result.append(num_dict[char])
        else:
            result.append(char)

    # 将结果列表合并为字符串并返回
    return ''.join(result)


def calculate_difference_rate(image1, image2, threshold=50):
    if isinstance(image1, str):
        image1 = cv2.imread(image1)
    if isinstance(image2, str):
        image2 = cv2.imread(image2)
    # 将图像转换为灰度图
    gray1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)

    # 计算图像的绝对差异
    diff = cv2.absdiff(gray1, gray2)

    # 将差异图像进行阈值处理
    _, diff_thresholded = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)

    # 计算差异率
    changed_pixels = np.count_nonzero(diff_thresholded)
    total_pixels = diff_thresholded.size
    difference_rate = (changed_pixels / total_pixels) * 100
    return difference_rate


def remove_punctuation_by_re(text):
    # 定义一个正则表达式，匹配所有标点符号
    pattern = r'[^\w]'
    # 使用re.sub()替换掉标点符号
    return re.sub(pattern, '', text)


# 车机图片相关服务
def calculate_car(reference, hypothesis):
    """
    计算字准确率 (CAR)

    参数:
    - reference: 实际文本
    - hypothesis: 识别文本

    返回:
    - car: 字准确率
    """
    if len(reference) == 0 or len(hypothesis) == 0:
        return 0.0
    edit_distance = Levenshtein.distance(reference.lower(), hypothesis.lower())

    # 计算 CAR
    car = (len(reference) - edit_distance) / len(reference)
    return round(car, 2)


# 计算两个文本的中文相似度
def calculate_similarity(text1, text2):
    tmp1 = text1.lower()
    tmp2 = text2.lower()
    if not tmp1 or not tmp2:
        return 0.0

    def chinese_tokenizer(text):
        return jieba.lcut(text, cut_all=True)

    # 创建 TfidfVectorizer 对象
    vectorizer = TfidfVectorizer(tokenizer=chinese_tokenizer)
    # 将两行文字转换为 TF-IDF 矩阵
    tfidf_matrix = vectorizer.fit_transform([tmp1, tmp2])
    # 计算余弦相似度
    similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
    return round(similarity[0][0], 2)


def get_ocr_wdt_hgt(item):
    # [[1356, 174], [1592, 174], [1592, 222], [1356, 222]], ...
    # there we need 1592 - 1356 and 222 - 174
    return item[0][2][0] - item[0][0][0], item[0][2][1] - item[0][0][1]


def is_sim(initial_text, chinese_text):
    initial_length, chinese_length = len(initial_text), len(chinese_text)
    if chinese_length >= initial_length + 3:
        return False, 0.0
    sim = xs.cossim(arabic_to_chinese(initial_text), arabic_to_chinese(chinese_text))
    if chinese_length >= 4 and initial_text.startswith(chinese_text):
        return True, sim
    # 最低相似度因随着文本长度而变化
    if initial_length <= 10:
        real_sim = 0.4
    elif 10 < initial_length < 20:
        # 线性插值公式：y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
        real_sim = 0.4 + (initial_length - 10) * (0.15 - 0.4) / (20 - 10)
    else:
        real_sim = 0.15

    # 长文本识别
    if initial_length >= 10:
        initial_length = 10
    if chinese_length >= 10:
        chinese_length = 10
    length_weight = abs(chinese_length - initial_length) / initial_length
    # 引入长度作为权重
    sim = sim / (1 + length_weight)
    return sim >= real_sim, sim


def delete_result_char_confidence(result, pattern, line, idx, i):
    if len(result[0][0][1]) == 3:
        delete_list = []
        for j, char in enumerate(line):
            if not pattern.match(char):
                delete_list.append(j)
        for index in sorted(delete_list, reverse=True):
            del result[idx][i][1][2][index]


class Recognition:
    def __init__(self, source, match, ocr, project_id="", project_init=False) -> None:
        self.ocr = ocr
        self.source = source
        self.minimum_width = 200
        self.minimum_height = 40
        self.min_quality = 0.8
        self.confidence = 0.9
        self.target_area = None
        self.match = match
        self.result = ''
        self.candidate = ''
        self.char_confidence_map = {}
        self.chinese_char_map = {}
        self.char_confidence_list = []
        self.project_id = project_id
        self.project_init = project_init
        self.str_confidence = 0
        self.return_image = source[-1]
        # 禁止选择的区域 ['text', x, y]
        self.blacklist = None
        self.false_drop_list = []

    def use_confidence_map(self):
        return len(self.char_confidence_map) != 0

    def select_char(self, diff1, diff2, str1_start, str2_start, map_key):
        diff_str = ""
        min_length = min(len(diff1), len(diff2))
        for idx in range(min_length):
            if diff1[idx] == diff2[idx]:
                diff_str += diff1[idx]
            else:
                if len(self.char_confidence_map) != 0:
                    if len(self.char_confidence_list) <= str1_start + idx:
                        log.warn(
                            f"select_char index out of range. {diff1 = } {idx = } length:{len(self.char_confidence_list)}")
                        return diff1 if len(diff1) > len(diff2) else diff2
                    char1_confidence = self.char_confidence_list[str1_start + idx]
                    char2_confidence = self.char_confidence_map[map_key][str2_start + idx]
                    if char2_confidence > self.confidence or char2_confidence > char1_confidence:
                        diff_str += diff2[idx]
                        self.char_confidence_list[str1_start + idx] = self.char_confidence_map[map_key][
                            str2_start + idx]
                    else:
                        diff_str += diff1[idx]
                else:
                    diff_str += diff2[idx]
        return diff_str

    def get_diff_str(self, diff1, diff2, str1_start, str2_start, map_key):
        idx = 0
        diff2_offset = 0
        for i in range(3, len(diff2)):
            if diff2[:i] in diff1:
                idx = diff1.find(diff2[:i])
                diff2_offset = i
                break

        diff_str = self.select_char(diff1[idx:], diff2, str1_start + idx, str2_start, map_key)
        if idx > 0:
            diff_str = diff1 + diff2[diff2_offset:]
            for j in range(diff2_offset, len(diff2)):
                self.char_confidence_list.append(self.char_confidence_map[map_key][str2_start + j])
        if len(diff2) > len(diff1) + idx:
            for idx in range(len(diff1) + idx, len(diff2)):
                diff_str += diff2[idx]
                if len(self.char_confidence_map) != 0:
                    self.char_confidence_list.append(self.char_confidence_map[map_key][str2_start + idx])

        return diff_str, diff2_offset

    def reconstruct_affix(self, str1, str2, str1_start, str2_start, map_key):
        diff_str = ""
        # if len(str1) < 2 or len(str2) < 2:
        #     return str1 + str2
        if len(str1) == len(str2):
            diff_str = self.select_char(str1, str2, str1_start, str2_start, map_key)
        elif len(str1) > len(str2):
            flag = False
            idx = 0
            for i in range(len(str2) - 2):
                if str2[:len(str2) - i] in str1:
                    idx = str1.find(str2[:len(str2) - i])
                    break
            if not flag:
                idx = len(str1) - len(str2)
            diff_str = self.select_char(str1[idx:], str2, str1_start + idx, str2_start, map_key)
            diff_str = str1[:idx] + diff_str
        elif len(str1) < len(str2):
            flag = False
            idx = 0
            for i in range(len(str1) - 2):
                if str1[:len(str1) - i] in str2:
                    idx = str2.find(str1[:len(str1) - i])
                    break
            if not flag:
                idx = 0
            diff_str = self.select_char(str1, str2[idx:], str1_start, str2_start + idx, map_key)
            diff_str = str2[:idx] + diff_str + str2[idx + len(str1):]
            self.char_confidence_list = self.char_confidence_list[:str1_start + len(str1)]
            for i in range(str2_start + idx + len(str1), len(self.char_confidence_map[map_key])):
                self.char_confidence_list.append(self.char_confidence_map[map_key][i])
        return diff_str

    def filter_print_ocr(self, result, initial_text, all_search=False):
        initial_text = initial_text.lower()
        max_sim = 0
        max_sim_area = None
        max_sim_text = ""

        def is_area(x, y, area):
            if x - 10 < area[0] < x + 10 and y - 10 < area[1] < y + 10:
                return True
            return False

        for idx in range(len(result)):
            res = result[idx]
            if not res:
                continue
            for i, line in enumerate(res):
                pattern = re.compile(r'[\u4e00-\u9fa5a-zA-Z0-9]')
                chinese_text = "".join(re.findall(pattern, line[1][0]))
                chinese_text = chinese_text.lower()
                str_confidence = line[1][1]
                if self.blacklist and self.blacklist[0] == chinese_text and is_area(int(line[0][0][0]), int(line[0][0][1]), self.blacklist[1:]):
                    continue
                if str_confidence < 0.7 and len(chinese_text) < 2:
                    continue
                if chinese_text == initial_text:
                    if self.target_area is None:
                        self.target_area = int(line[0][0][0]), int(line[0][0][1])
                    delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                    return chinese_text, idx, i

                sim_flag, sim = is_sim(initial_text, chinese_text)
                if self.target_area is None:
                    if sim_flag:
                        if all_search:
                            if sim > max_sim:
                                max_sim = max(max_sim, sim)
                                max_sim_area = int(line[0][0][0]), int(line[0][0][1])
                                max_sim_text = chinese_text
                        else:
                            self.target_area = int(line[0][0][0]), int(line[0][0][1])
                            delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                            return chinese_text, idx, i
                    continue
                if sim_flag:
                    delete_result_char_confidence(result, pattern, line[1][0], idx, i)
                    return chinese_text, idx, i
            if all_search:
                self.target_area = max_sim_area
        return '', 0, 0

    def compare_seq(self, str1, str2, idx):
        m = len(str1)
        n = len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        length = 0
        end_pos1 = 0
        end_pos2 = 0

        if not str1 or not str2 or str1 == str2:
            return str1

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i - 1] == str2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                    if dp[i][j] > length:
                        length = dp[i][j]
                        end_pos1 = i - 1
                        end_pos2 = j - 1
                else:
                    dp[i][j] = 0
        if length < 2 and end_pos1 != m - 1:
            self.char_confidence_list += self.char_confidence_map[idx]
            return str1 + str2
        # 特殊处理，都是数字的情况下车机可能会直接进行跳转 例子：['帮我呼叫座机023', '帮我呼叫座机023', '帮我呼叫座机023', '6623123']
        if str2.isdigit() and end_pos2 - length + 1 != 0:
            self.char_confidence_list += self.char_confidence_map[idx]
            return str1 + str2
        if end_pos2 - length + 1 == 0 and end_pos1 + 1 == m:
            if len(self.char_confidence_map) != 0:
                self.char_confidence_list += self.char_confidence_map[idx][length:]
            return str1 + str2[length:]
        elif end_pos2 - length + 1 > 0 and end_pos1 + 1 == m:
            diff_str2 = str2[0:end_pos2 - length + 1]
            diff_str1_start = m - length - len(diff_str2)
            diff_str1 = str1[diff_str1_start:diff_str1_start + len(diff_str2)]
            diff_str, d2_idx = self.get_diff_str(diff_str1, diff_str2, diff_str1_start, 0, idx)
            self.char_confidence_list += self.char_confidence_map[idx][end_pos2 + 1:]
            return str1[:diff_str1_start] + diff_str + str2[end_pos2 - length + 1:]
        elif end_pos2 - length + 1 == 0 and end_pos1 + 1 < m:
            diff_str1 = str1[end_pos1 + 1:]
            diff_str2 = str2[length:length + len(diff_str1)]
            diff_str, d2_idx = self.get_diff_str(diff_str1, diff_str2, end_pos1 + 1, length, idx)
            if len(self.char_confidence_map) != 0:
                self.char_confidence_list += self.char_confidence_map[idx][length + len(diff_str):]
            return str1[:end_pos1 + 1] + diff_str + str2[length + len(diff_str):]

        # 公共字串字串在两个字符串的中间
        common_str = str1[end_pos1 - length + 1:end_pos1 + 1]
        str1_prefix, str1_suffix = str1[0: end_pos1 - length + 1], str1[end_pos1 + 1:]
        str2_prefix, str2_suffix = str2[0: end_pos2 - length + 1], str2[end_pos2 + 1:]
        diff_prefix = self.reconstruct_affix(str1_prefix, str2_prefix, 0, 0, idx)
        diff_suffix = self.reconstruct_affix(str1_suffix, str2_suffix, end_pos1 + 1, end_pos2 + 1, idx)
        return diff_prefix + common_str + diff_suffix

    def process_ocr_output(self, ocr_output):
        set_output = set(ocr_output)
        if len(set_output) <= 2:
            return
        for i in range(1, len(ocr_output)):
            # 倒序搜索
            i = len(ocr_output) - i
            if not ocr_output[i]:
                continue
            count = self.chinese_char_map.get(ocr_output[i][0], 0)
            if count < 2:
                ocr_output[i] = ocr_output[i][1:]
                del self.char_confidence_map[i][0]
                continue
            if len(ocr_output[i]) >= 3:
                sub_str = ocr_output[i][:3]
                is_delete = True
                for j in range(i):
                    j = i - j
                    idx = ocr_output[j].find(sub_str[1:])
                    if idx > 0:
                        tmp_char = ocr_output[j][idx - 1]
                        if ocr_output[i][0] == tmp_char:
                            is_delete = False
                            break
                if is_delete:
                    ocr_output[i] = ocr_output[i][1:]
                    del self.char_confidence_map[i][0]

    def run(self):
        ocr_output = []
        init_photo_path = os.path.join(globalAppSettings.photo_project_init_dir, f"{self.project_id}.jpg")
        init_area_txt = os.path.join(globalAppSettings.photo_project_init_dir, f"{self.project_id}.txt")
        init_offset_left = 200
        init_offset_right = 600
        init_move_up = 40
        init_move_down = 100
        all_search = False
        false_drop = int(len(self.source)/2) + 1
        global FULL_MATCH_MAP
        if FULL_MATCH_MAP.get(self.project_id, None):
            self.target_area = FULL_MATCH_MAP[self.project_id]
        if len(self.source) == 0 or not os.path.exists(self.source[0]):
            log.warn(f"not path {self.source[0]}")
            return
        # 暂时取消ocr使用初始化标定的坐标
        # if os.path.exists(init_photo_path) and os.path.exists(init_area_txt) and not self.project_init:
        #     with open(init_area_txt, 'r') as file:
        #         area = json.load(file)
        #     self.target_area = int(area[2]) + init_offset_left, int(area[0]) + init_move_up
        for i, image_file in enumerate(self.source):
            if not os.path.exists(image_file):
                log.warn(f"not path {image_file}")
                continue
            if self.target_area is not None:
                # 初始化相关
                if self.project_init:
                    if not os.path.exists(init_photo_path):
                        tmp_gary = cv2.imread(self.source[0])
                        tmp_gary = cv2.cvtColor(tmp_gary, cv2.COLOR_BGR2RGB)
                        height, width, _ = tmp_gary.shape
                        up_coordinate = max(0, self.target_area[1] - init_move_up)
                        down_coordinate = min(height, self.target_area[1] + init_move_down)
                        left_coordinate = max(0, self.target_area[0] - init_offset_left)
                        right_coordinate = min(width, self.target_area[0] + init_offset_right)
                        init_image = tmp_gary[up_coordinate:down_coordinate, left_coordinate:right_coordinate]
                        with open(init_area_txt, 'w') as f:
                            json.dump([up_coordinate, down_coordinate, left_coordinate, right_coordinate], f)
                        plt.imsave(init_photo_path, init_image)

                img = cv2.imread(image_file)
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                height, width, _ = img.shape
                up_coordinate = max(0, self.target_area[1] - init_move_up)
                down_coordinate = min(height, self.target_area[1] + init_move_down)
                left_coordinate = max(0, self.target_area[0] - init_offset_left)
                right_coordinate = min(width, self.target_area[0] + init_offset_right)

                cropped_image = gray[up_coordinate:down_coordinate, left_coordinate:right_coordinate]
                result = self.ocr.identify(cropped_image)
            else:
                result = self.ocr.identify(image_file)
            ocr_item, idx, idy = self.filter_print_ocr(result, self.match, all_search)
            if all_search:
                all_search = False
            # 判断区域是否选错
            if i < false_drop and len(ocr_item) != 0:
                self.false_drop_list.append(ocr_item)
                if len(self.false_drop_list) == false_drop and len(set(self.false_drop_list)) == 1:
                    if self.target_area:
                        self.blacklist = [ocr_item, int(self.target_area[0]), int(self.target_area[1])]
                    ocr_output = []
                    self.false_drop_list = []
                    self.target_area = None
                    # 重选区域全搜索
                    all_search = True
            if ocr_item == self.match.lower():
                self.result = ocr_item
                self.str_confidence = result[idx][idy][1][1]
                self.return_image = image_file
                return
            if self.target_area is not None and len(ocr_item) != 0:
                ocr_output.append(ocr_item)
                self.char_confidence_map[len(ocr_output) - 1] = result[idx][idy][1][2]
                for char in ocr_item:
                    self.chinese_char_map[char] = self.chinese_char_map.get(char, 0) + 1
                self.return_image = image_file
        sequence = ''
        # 对ocr_output处理，主要目的为了识别头字符是否是被截断识别的字符
        self.process_ocr_output(ocr_output)
        for i, current in enumerate(ocr_output):
            if i == 0:
                sequence = current
                if len(self.char_confidence_map) != 0:
                    self.char_confidence_list = self.char_confidence_map[0]
            else:
                sequence = self.compare_seq(sequence, current, i)
                if sequence == self.match.lower():
                    break
        self.result = sequence
        if len(self.char_confidence_list) != 0:
            self.str_confidence = sum(self.char_confidence_list) / len(self.char_confidence_list)


class QUBEImageSvc:
    ocr = PadOcr()
    camera = Camera()

    def __init__(self):
        self.interval = 0.5
        self.times = 8
        self.start_wait = 0.5
        self.take_result_timeout = 15
        self.false_awake_list = []
        self.false_awake_flag = True
        self.lock = threading.Lock()

    def image_recognize(self, image) -> list:
        result = self.ocr.identify(image)
        text_list = []
        for idx in range(len(result)):
            res = result[idx]
            if not res:
                continue
            for line in res:
                text_list.append(line[1][0])
        return text_list

    # 返回 选择图片路径 识别的文本 识别正确率
    def image_select_by_text(self, path_list, audio_text) -> (str, str, float):
        sorted(path_list, reverse=True)
        audio_text = remove_punctuation_by_re(audio_text)
        max_similarity = 0.0
        max_car = 0.0
        result_path = ""
        result_text = ""
        for i, image_path in enumerate(path_list):
            text_list = self.image_recognize(image_path)
            for text in text_list:
                text = remove_punctuation_by_re(text)
                similarity = calculate_similarity(audio_text, text)
                if int(similarity) == 1 or audio_text in text:
                    car = calculate_car(audio_text, text)
                    return image_path, text, car
                if similarity > max_similarity:
                    max_similarity = similarity
                    result_path = image_path
                    result_text = text
                    max_car = calculate_car(audio_text, text)
        return result_path, result_text, max_car

    def image_select_by_multi_recognition(self, path_list, audio_text, project_id, project_init=False) -> (
            str, str, float, float):
        if len(path_list) == 0:
            return "", "", 0.0, None
        glob_path = re.sub(r"-\d+\.jpg$", "-*.jpg", path_list[0])

        def extract_number(file_name):
            match = re.search(r'-(\d+)\.jpg$', file_name)
            if match:
                return int(match.group(1))
            return 0

        source = sorted(glob.glob(glob_path), key=extract_number)
        audio_text = remove_punctuation_by_re(audio_text)
        rec = Recognition(source, audio_text, self.ocr, project_id=project_id, project_init=project_init)
        rec.run()
        global FULL_MATCH_MAP
        if rec.result == audio_text.lower():
            FULL_MATCH_MAP[project_id] = rec.target_area
        elif FULL_MATCH_MAP.get(project_id, None) and rec.result != audio_text.lower():
            del FULL_MATCH_MAP[project_id]
            rec = Recognition(source, audio_text, self.ocr, project_id=project_id, project_init=project_init)
            rec.run()
            if rec.result == audio_text.lower():
                FULL_MATCH_MAP[project_id] = rec.target_area
        result_text = rec.result
        max_car = calculate_car(audio_text, result_text)
        # 置信度
        confidence = rec.str_confidence
        return rec.return_image, result_text, max_car, confidence

    # 从多张采集的图片中选择响应最完善的图片
    def image_select(self, path_list) -> str:
        text_lists = []
        text_length_map = {}
        different_idx = -1
        for image_path in path_list:
            text_list = self.image_recognize(image_path)
            for i, text in enumerate(text_list):
                if not text_length_map.get(i):
                    text_length_map[i] = len(text)
                else:
                    if text_length_map.get(i) != len(text):
                        different_idx = i
                        text_length_map[i] = len(text)
            text_lists.append(text_list)

        if different_idx == -1 or not text_lists:
            return ""
        max_text_list = 0
        max_length = 0
        for i, text_list in enumerate(text_lists):
            if len(text_list[different_idx]) > max_length:
                max_length = len(text_list[different_idx])
                max_text_list = i
        return path_list[max_text_list]

    def photograph(self, image_dir, name):
        ret = self.camera.camera_init()
        if ret:
            log.warn("QUBEImageSvc camera_init fail")
            return
        image_name = self.camera.take_photo(image_dir, name)
        self.camera.camera_close()
        return image_name

    # 一段时间内多次采集图片，保存多张
    # 参数 保存图像目录, 图像名字, 音频时间单位为秒
    # 在目录下以 name-1, name-2... 名字保存
    async def photograph_plan(self, image_dir, name, audio_length):
        audio_length = float(audio_length)
        start_wait, interval, times = deviceConfig.get_camera()
        ret = self.camera.camera_init()
        if ret:
            log.warn("QUBEImageSvc camera_init fail")
            return
        if audio_length > times * interval:
            time.sleep((audio_length + start_wait) - times * interval)
        else:
            time.sleep(start_wait)
        await self.camera.take_photos(image_dir, name, interval, times)
        self.camera.camera_close()
        return

    def photograph_plan_sync(self, image_dir, name, audio_length):
        audio_length = float(audio_length)
        start_wait, interval, times = deviceConfig.get_ocr_config()
        # log.info(f"{start_wait} , {interval} , {times} photograph_plan_sync")
        ret = self.camera.camera_init()
        if ret:
            log.warn("QUBEImageSvc camera_init fail")
            return ret
        # if audio_length > times * interval:
        #     time.sleep((audio_length + start_wait) - times * interval)
        # else:
        #     time.sleep(start_wait)
        times = min(times, (audio_length + start_wait) // interval + 1)
        self.camera.take_photos_sync(image_dir, name, interval, int(times))
        self.camera.camera_close()
        return

    def photograph_process(self, request_id, image_dir, name, audio_length, result_id, is_ocr=True):
        set_request_id(request_id)
        try:
            if is_ocr:
                ret = self.photograph_plan_sync(image_dir, name, audio_length)
                if ret:
                    return
            else:
                time.sleep(float(audio_length))
            start_time = time.time()
            index = 2
            path_list = []
            start_wait, interval, diff_rate, result_timeout = deviceConfig.get_result_photo_config()
            if start_wait:
                time.sleep(start_wait)
            while time.time() - start_time < result_timeout:
                image_name = self.photograph(image_dir, f"{result_id}_{index}")
                if not image_name:
                    log.warn(f"photograph fail, name:{result_id}_{index}")
                    break
                path_list.append(os.path.join(image_dir, image_name))
                if len(path_list) < 3:
                    index += 1
                    time.sleep(interval)
                    continue
                diff1 = calculate_difference_rate(path_list[-1], path_list[-2])
                diff2 = calculate_difference_rate(path_list[-2], path_list[-3])

                # 检查变化率
                if diff1 < diff_rate and diff2 < diff_rate:
                    break
                index += 1
                time.sleep(interval)
            # 保留所有结果图片,暂不删除
            # if len(path_list) >= 2:
            #     for i in range(len(path_list) - 1):
            #         if os.path.exists(path_list[i]):
            #             os.remove(path_list[i])
            if len(path_list) < 1:
                log.info(f"photograph_image index({index}), no result photo")
                return
            basename = os.path.basename(path_list[-1])
            result_name = re.sub(r"_\d+", "_result", basename)
            result_path = os.path.join(os.path.dirname(path_list[-1]), result_name)
            if os.path.exists(path_list[-1]):
                if os.path.exists(result_path):
                    os.remove(result_path)
                os.rename(path_list[-1], result_path)
            log.info(f"photograph_image index({index}), result photo:{result_name}")
        except Exception as e:
            log.error(f"photograph_process fail: {str(e)}")

    def false_awake_start(self, project_id, image_dir, name):
        time_interval = 0.5
        offset = 200
        init_area_txt = os.path.join(globalAppSettings.photo_project_init_dir,
                                     f"{project_id}.txt")
        background_photo = os.path.join(image_dir, name)
        if "." not in background_photo:
            background_photo += ".jpg"

        def recognize_text_combo(photo):
            background_text = ""
            tmp_texts = self.image_recognize(photo)
            for text in tmp_texts:
                background_text += text
            return remove_punctuation_by_re(background_text).lower()

        def process_texts_to_sim(init_texts, n_texts):
            init_texts = remove_punctuation_by_re(init_texts)
            n_texts = remove_punctuation_by_re(n_texts)
            if init_texts == n_texts:
                return 1

            if not init_texts or not n_texts:
                return 0

            if len(n_texts) > len(init_texts) * 3:
                return 0

            if init_texts in n_texts:
                n_texts = n_texts.replace(init_texts, "")
            similar = calculate_similarity(arabic_to_chinese(init_texts), arabic_to_chinese(n_texts))
            return similar

        try:
            with open(init_area_txt, 'r') as file:
                area = json.load(file)
            tmp_background_photo = cv2.imread(str(background_photo))
            tmp_background_photo = cv2.cvtColor(tmp_background_photo, cv2.COLOR_BGR2RGB)
            if area[3] - area[2] >= 700:
                area[3] -= offset
            texts = recognize_text_combo(tmp_background_photo[area[0]: area[1], area[2]:area[3]])
            is_awake = False
            self.false_awake_flag = True
            idx = 0
            judge_diff_rate = 0.5
            ret = self.camera.camera_init()
            sim_array, sim_len = [], 2
            text_array, text_array_len = [], 10
            if ret:
                log.warn("QUBEImageSvc camera_init fail")
                return
            while self.false_awake_flag:
                numpy_image = self.camera.take_photo_no_save()
                if numpy_image.size == 0:
                    time.sleep(time_interval)
                    continue
                image = Image.fromarray(numpy_image, 'RGB')
                np_image = np.array(image)
                gray = np_image[area[0]: area[1], area[2]:area[3]]
                now_texts = recognize_text_combo(gray)
                if is_awake:
                    sim = process_texts_to_sim(texts, now_texts)
                    if len(sim_array) == sim_len:
                        sim_array.append(sim)
                        sim_array.pop(0)
                        # print(f"{texts =}, {now_texts =}, {sim_array =}")
                        if sim_array[0] > judge_diff_rate and sim_array[1] > judge_diff_rate:
                            is_awake = False
                    else:
                        sim_array.append(sim)
                    # 判断背景是否变化
                    if len(text_array) == text_array_len:
                        text_array.append(now_texts)
                        text_array.pop(0)
                        if all(x == text_array[0] for x in text_array):
                            texts = now_texts
                            is_awake = False
                            # print(f"背景变化 {text_array =}")
                    else:
                        text_array.append(now_texts)
                else:
                    sim = process_texts_to_sim(texts, now_texts)
                    if len(sim_array) == sim_len:
                        sim_array.append(sim)
                        sim_array.pop(0)
                        # print(f"{texts =}, {now_texts =}, {sim_array =}")
                        if sim_array[0] < judge_diff_rate and sim_array[1] < judge_diff_rate:
                            self.lock.acquire()
                            self.false_awake_list.append(time.time())
                            self.lock.release()
                            is_awake = True
                            image.save(os.path.join(image_dir, "%s-%d.jpg" % (name, idx)), format='JPEG')
                            idx += 1
                    else:
                        sim_array.append(sim)
                time.sleep(time_interval)
            self.camera.camera_close()
        except Exception as e:
            log.error(f"false awake error:{str(e)}")

    def false_awake_stop(self):
        self.false_awake_flag = False
        self.lock.acquire()
        false_awake_list = self.false_awake_list
        self.false_awake_list = []
        self.lock.release()
        return false_awake_list
