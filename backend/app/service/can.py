from app.utils.utils import *
from app.dao.can_dao import CanQueryDao, CanOperateDao
from app.dao.corpus_dao import CorpusOperateDao, CorpusQueryDao
from app.dao.models.sqlite_gen import CanSignal, DbcFile, ChannelInfo, CanFrame
import os
from app.utils.etsdk import *
from app.utils.etsdk.dk_cffi import *
import time
from app.service.can_dk import global_channel, bit_table

def save_dbc(file, file_data, data):
    file_name, file_extension = os.path.splitext(file.filename)
    file_dir = get_dbc_dir()
    filepath = os.path.join(file_dir, file.filename)
    if os.path.exists(filepath):
        return 1, "A dbc file with the same name exists"
    with open(filepath, "wb") as file:
        file.write(file_data)
    
    dbc_id = generate_random_string()
    dbc = DbcFile(
        file_id = dbc_id,
        file_name = file_name,
        file_path = filepath,
    )
    
    # 解析
    try:
        dbc_config = dbc_to_json(filepath)
    except Exception as e:
        os.remove(filepath)
        return 1, "Parsing the dbc file failed"
    for frame in dbc_config["messages"]:
        frame_obj = CanFrame(
            file_id = dbc_id,
            frame_id = frame["frame_id"],
            frame_name = frame["name"],
            length = frame["length"],
            senders = ",".join(frame["senders"]),
            attributes = str(frame["attributes"]),
        )
        num = 1
        for signal in frame["signals"]:
            signal_obj = CanSignal(
                file_id = dbc_id,
                frame_id = frame["frame_id"],
                signal_id = dbc_id + "_" + str(frame["frame_id"]) + "_" + str(num),
                signal_name = signal["name"],
                signal_info = str(signal),
            )
            CanOperateDao.saveCanSignal(signal_obj)
            num += 1
        CanOperateDao.saveCanFrame(frame_obj)
    CanOperateDao.saveDbcFile(dbc)
    return 0, None
    
def dbc_file_delete(file_id):
    dbc_obj = CanQueryDao.findDbcFileById(file_id)
    if dbc_obj is None:
        return 1, "No valid dbc file found"
    check_data = {"file_id": file_id}
    signal_list = CanQueryDao.showAllCanSignal(check_data)
    for signal in signal_list:
        check_signal = {"signal_id": signal}
        corpus_list, _ = CorpusQueryDao.showAllTestCorpus(check_signal)
        if len(corpus_list) > 0:
            return 1, "Error: Can't delete this dbc_file, some corpus config dbc signal data."
    for signal in signal_list:
        CanOperateDao.deleteCanSignal(signal.signal_id)
    frame_list = CanQueryDao.showAllCanFrame(check_data)
    for frame in frame_list:
        CanOperateDao.deleteCanFrame(frame.frame_id)
    if os.path.exists(dbc_obj.file_path):
        os.remove(dbc_obj.file_path)
    CanOperateDao.deleteDbcFile(file_id)
    return 0, None
    
def get_dbc_info(file_id):
    dbc_obj = CanQueryDao.findDbcFileById(file_id)
    if dbc_obj is None:
        return 1, "No valid dbc file found"
    dbc_config = dbc_to_json(dbc_obj.file_path)
    for frame in dbc_config["messages"]:
        num = 1
        for signal in frame["signals"]:
            signal["signal_id"] = dbc_obj.file_id + "_" + str(frame["frame_id"]) + "_" + str(num)
            num += 1
    ret = {
        "name": dbc_obj.file_name,
        "protocol": "",
        "comment": ",",
        "messages": dbc_config["messages"],
        "nodes": dbc_config["nodes"],
        "attribute_definitions": dbc_config["attribute_definitions"],
    }
    return 0 ,ret
    
def get_dbc_list():
    dbc_obj_list = CanQueryDao.showAllDbcFile()
    ret = []
    num = 0
    for dbc_obj in dbc_obj_list:
        obj_temp = {}
        obj_temp["file_id"] = dbc_obj.file_id
        obj_temp["name"] = dbc_obj.file_name
        obj_temp["file_path"] = dbc_obj.file_path
        ret.append(obj_temp)
        num += 1
    return ret, num

def save_channel_info():
    dk = DKRuntime()
    res = dk.startup()
    if res < 0:
        quit()
    time.sleep(3)
    # 查询接口描述
    ifdescs = dk.query_channels()
    global_channel.set_value(ifdescs)
    # if len(ifdescs) > 0:
    #     # 删除
    #     CanOperateDao.deleteChannelInfoAll()
    channel_obj = ifdescs[0]
    obj = CanQueryDao.findChannelInfoById(str(channel_obj.ch_idx))
    if obj is None:
        channel = ChannelInfo(
            channel_id = str(channel_obj.ch_idx),
            channel_name = convert_c_buffer_to_string(channel_obj.ch_name, len(channel_obj.ch_name)),
            dev_idx = str(channel_obj.dev_idx),
            channel_seat = 0,
            mode = str(channel_obj.type),
        )
        CanOperateDao.saveChannelInfo(channel)
    channel_obj = ifdescs[1]
    obj = CanQueryDao.findChannelInfoById(str(channel_obj.ch_idx))
    if obj is None:
        channel = ChannelInfo(
            channel_id = str(channel_obj.ch_idx),
            channel_name = convert_c_buffer_to_string(channel_obj.ch_name, len(channel_obj.ch_name)),
            dev_idx = str(channel_obj.dev_idx),
            channel_seat = 1,
            mode = str(channel_obj.type),
        )
        CanOperateDao.saveChannelInfo(channel)
    return 0, None

def channel_config(channel_id):
    channel_obj = CanQueryDao.findChannelInfoById(channel_id)
    if channel_obj is None :
        return 1, "No valid channel config found"
    arbitration = {
        "bitrate": channel_obj.arbitration_bitrate, 
        "sampling_point": channel_obj.arbitration_sampling_point,
        "sjw": channel_obj.arbitration_sjw
        }
    config_data = {
        "bitrate": channel_obj.data_bitrate,
        "sampling_point": channel_obj.data_sampling_point,
        "sjw": channel_obj.data_sjw
        }
    config = {
        "channel_id": channel_obj.channel_id, 
        "channel_name": channel_obj.channel_name, 
        "dev_idx": channel_obj.dev_idx, 
        "mode": channel_obj.mode, 
        "dbc_file_id": channel_obj.dbc_file_id, 
        "bitrate": channel_obj.bitrate, 
        "sampling_point": channel_obj.sampling_point,
        "sjw": channel_obj.sjw,
        "arbitration": arbitration, 
        "data": config_data
        }

    return 0, config

def channel_list():
    channels = CanQueryDao.showAllChannelInfo()
    ret = []
    num = 0
    for channel_obj in channels:
        res, channel_info = channel_config(channel_obj.channel_id)
        if res == 0:
            ret.append(channel_info)
        else:
            return 1, channel_info
        num += 1
    return ret, num

def channel_config_save(data):
    if "channel_ids" not in data or "config" not in data:
        return 1, "Error input data"
    for channel_id in data["channel_ids"]:
        channel = None
        temp = CanQueryDao.findChannelInfoById(channel_id)
        id = temp.channel_seat
        ifdescs = global_channel.get_value()
        channel = ifdescs[id]
        config = data["config"]
        if "mode" not in config or "dbc_file_id" not in config:
            return 1, "Error input data config"
        dbc_obj = CanQueryDao.findDbcFileById(config["dbc_file_id"])
        if dbc_obj is None:
            return 1, "No valid dbc file found"
        channel_obj = {
            "channel_id" : channel_id,
            "mode" : config["mode"],
            "dbc_file_id" : config["dbc_file_id"],
        }
        can_channel_1 = DKCanChannel(channel, my_recv_callback)
        if config["mode"] == "ISO CAN FD" or config["mode"] == "NON ISO CAN FD":
            if "arbitration" in config:
                arbitration = config["arbitration"]
                channel_obj["arbitration_bitrate"] = arbitration["bitrate"]
                # channel_obj["arbitration_sampling_point"] = arbitration["sampling_point"]
                # channel_obj["arbitration_sjw"] = arbitration["sjw"]
            else:
                return 1, "Missing arbitration parameter "
            if "data" in config:
                config_data = config["data"]
                channel_obj["data_bitrate"] = config_data["bitrate"]
                # channel_obj["data_sampling_point"] = config_data["sampling_point"]
                # channel_obj["data_sjw"] = config_data["sjw"]
            else:
                return 1, "Missing data parameter "
            if config["mode"] == "ISO CAN FD":
                can_channel_1._config.mode = CanMode.DK_ISO_CANFD
            elif config["mode"] == "NON ISO CAN FD":
                can_channel_1._config.mode = CanMode.DK_NONE_ISO_CANFD
            can_channel_1._config.run_mode = CanRunMode.DK_CAN_RUN_NORMAL
            can_channel_1.apply_config()
            can_channel_1.set_dbc_file(dbc_obj.file_path)
            a1, a2, a3, a4, a5, a6, a7 = bit_table[channel_obj["data_bitrate"]]
            can_channel_1.set_dbit_timing(bitrate=channel_obj["data_bitrate"]*1000, presc=a1, sjw=a5, propseg=a2, pseg1=a3, pseg2=a4) # 数据位
            b1, b2, b3, b4, b5, b6, b7 = bit_table[channel_obj["arbitration_bitrate"]]
            can_channel_1.set_arbit_timing(bitrate=channel_obj["arbitration_bitrate"]*1000, presc=b1, sjw=b5, propseg=b2, pseg1=b3, pseg2=b4) # 仲裁位
            can_channel_1.apply_bit_timing()
        else:
            channel_obj["bitrate"] = config["bitrate"]
            # channel_obj["sampling_point"] = config["sampling_point"]
            # channel_obj["sjw"] = config["sjw"]
            can_channel_1._config.mode = CanMode.DK_STANDARD_CAN
            can_channel_1._config.run_mode = CanRunMode.DK_CAN_RUN_NORMAL
            can_channel_1.apply_config()
            can_channel_1.set_dbc_file(dbc_obj.file_path)
            a1, a2, a3, a4, a5, a6, a7 = bit_table[channel_obj["bitrate"]]
            can_channel_1.set_arbit_timing(bitrate=channel_obj["bitrate"]*1000, presc=a1, sjw=a5, propseg=a2, pseg1=a3, pseg2=a4) # 数据位
            can_channel_1.apply_bit_timing()
        # can_channel_1.enable()
        channel_temp = {"channel_id": channel_id, "channel_obj": can_channel_1}
        global_channel.del_configed_channel(channel_id)
        global_channel.set_configed_channel(channel_temp)
        CanOperateDao.updateChannelInfo(channel_id, channel_obj)
        return 0, ""

def channel_enable(channel_id):
    channel = None
    channel_queue = global_channel.get_configed_channel()
    if len(channel_queue) > 0:
        for temp in channel_queue:
            if int(temp["channel_id"]) == int(channel_id):
                channel = temp["channel_obj"]
                global_channel.set_enable_channel(temp)
                break
    else:
        return 1, "There are no available channels"
    channel.enable()
    return 0, None

def channel_disable(channel_id):
    channel = None
    channel_queue = global_channel.get_enable_channel()
    if len(channel_queue) > 0:
        for temp in channel_queue:
            if int(temp["channel_id"]) == int(channel_id):
                channel = temp["channel_obj"]
                global_channel.del_enable_channel(temp["id"])
                break
    else:
        return 1, "There are no available channels"
    channel.close()
    return 0, None