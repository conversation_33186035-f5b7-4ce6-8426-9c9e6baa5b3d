import os
import ast

from app.utils.utils import (
    chinese_to_pinyin,
    get_file_dir,
    get_audio_duration,
    generate_random_string,
    move_file_to_folder,
    analysis_excel,
    get_excel_dir,
    corpus_excel,
    copy_file_to_modifyfolder,
)
from app.dao.corpus_dao import CorpusOperateDao, CorpusQueryDao
from app.dao.project_dao import ProjectQueryDao, ProjectOperateDao
from app.dao.plan_dao import PlanQueryDao
from app.service.synthesize_svc import SynthesizeSvc
from app.constant import Vocalists
from app.dao.models.sqlite_gen import (
    TestCorpus,
    CorpusAudio,
    RouseCorpus,
    DisturbCorpus,
    BackgroundNoise,
    MultiCorpus,
    CorpusLabel,
)
from app.dao.base_dao import find_table_id


def save_audio_file(file, file_data, data):
    # print(data)
    file_name, file_extension = os.path.splitext(file.filename)
    file_name_pinyin = chinese_to_pinyin(data.get("text", ""))
    file_dir = get_file_dir()
    filepath = os.path.join(file_dir, file.filename)
    with open(filepath, "wb") as file:
        file.write(file_data)
    duration = get_audio_duration(filepath)
    if duration["status"] == "error":
        if os.path.exists(filepath):
            os.remove(filepath)
        return 1, "", "", ""
    else:
        audio_id = find_table_id("corpus_audio_id")
        audio = CorpusAudio(
            aud_id="audio_" + str(audio_id),
            aud_url=filepath,
            pinyin=file_name_pinyin,
            audio_duration=duration["data"],
        )
        save_audio = CorpusOperateDao.saveAudio(audio)
        return 0, save_audio.aud_id, file_name_pinyin, duration["data"]


def excel_create_corpus(file, file_data, data):
    # print(data)
    file_name, file_extension = os.path.splitext(file.filename)
    excel_dir = get_excel_dir()
    storage_import_excel = os.path.join(excel_dir, "import")
    if not os.path.exists(storage_import_excel):
        os.makedirs(storage_import_excel)
    filepath = os.path.join(storage_import_excel, file.filename)
    with open(filepath, "wb") as file:
        file.write(file_data)
    excel_type, corpuslist = corpus_excel(filepath)
    # print(corpuslist)
    need_sync = []
    if excel_type == "rouse":
        for cor in corpuslist:
            file_name_pinyin = chinese_to_pinyin(cor["A"])
            aud_id_random = find_table_id("corpus_audio_id")
            # cor_random_str = generate_random_string()
            rousecorpus = create_rousecorpus(
                cor["A"], cor["B"], cor["C"], cor["D"], cor["E"]
            )
            if cor["F"] is not None:
                new_path = copy_file_to_modifyfolder(cor["F"], "rouse_corpus", cor["D"], cor["E"])
                duration = get_audio_duration(new_path)
                name = os.path.basename(new_path)
                if duration["status"] == "error":
                    continue
                file_name_pinyin = chinese_to_pinyin(cor["A"])
                audio = CorpusAudio(
                    aud_id="audio_" + str(aud_id_random),
                    corpus_id=rousecorpus.corpus_id,
                    aud_url=new_path,
                    pinyin=file_name_pinyin,
                    audio_duration=duration["data"],
                )
                save_audio = CorpusOperateDao.saveAudio(audio)
                corpus_data = {
                    "aud_id": save_audio.aud_id,
                    "audio_url": name,
                    "audio_path": new_path,
                    "audio_duration": duration["data"],
                }
                update_corpus = CorpusOperateDao.updateRouseCorpus(
                    rousecorpus.corpus_id, corpus_data
                )
            else:
                need_sync.append(rousecorpus.corpus_id)
                synthesize_rousecorpus(need_sync, cor["E"])
                need_sync = []
    elif excel_type == "test":
        for cor in corpuslist:
            testcorpus = create_testcorpus(
                cor["A"],
                cor["B"],
                cor["C"],
                cor["D"],
                cor["E"],
                cor["F"],
                cor["G"],
                cor["H"],
            )
            if cor["I"] is not None:
                new_path = copy_file_to_modifyfolder(cor["I"], "test_corpus", cor["D"], cor["E"])
                duration = get_audio_duration(new_path)
                name = os.path.basename(new_path)
                if duration["status"] == "error":
                    continue
                file_name_pinyin = chinese_to_pinyin(cor["A"])
                aud_id_random = find_table_id("corpus_audio_id")
                audio = CorpusAudio(
                    aud_id="audio_" + str(aud_id_random),
                    corpus_id=testcorpus.corpus_id,
                    aud_url=new_path,
                    pinyin=file_name_pinyin,
                    audio_duration=duration["data"],
                )
                save_audio = CorpusOperateDao.saveAudio(audio)
                corpus_data = {
                    "aud_id": save_audio.aud_id,
                    "audio_url": name,
                    "audio_path": new_path,
                    "audio_duration": duration["data"],
                }
                update_corpus = CorpusOperateDao.updateTestCorpus(
                    testcorpus.corpus_id, corpus_data
                )
            else:
                need_sync.append(testcorpus.corpus_id)
                synthesize_testcorpus(need_sync, "", cor["J"])
                need_sync = []
    elif excel_type == "multi1":  # 合成
        for cor in corpuslist:
            aud_id_random = find_table_id("corpus_audio_id")
            corpus_Items = []
            for t_corpus in cor["corpus_list"]:
                c_items = {}
                file_name_pinyin = chinese_to_pinyin(t_corpus["text"])
                c_items["aud_id"] = ""
                c_items["audio_duration"] = ""
                c_items["audio_url"] = ""
                c_items["expect_result"] = t_corpus["expect_result"]
                c_items["pinyin"] = file_name_pinyin
                c_items["text"] = t_corpus["text"]
                corpus_Items.append(c_items)
            mulcorpus_id = generate_random_string()
            create_multi_corpus(
                cor["A"],
                "intelligent-interaction",
                "continuous-dialogue-interation",
                cor["D"],
                cor["E"],
                cor["B"],
                cor["F"],
                corpus_Items,
                mulcorpus_id,
            )
            corpus_ids = [mulcorpus_id]
            synthesize_multicorpus(corpus_ids, "", cor["H"])

    elif excel_type == "multi2":  # 导入
        for cor in corpuslist:
            corpus_Items = []
            for t_corpus in cor["corpus_list"]:
                c_items = {}
                aud_id_random = find_table_id("corpus_audio_id")
                if t_corpus["audio_url"] is not None:
                    new_path = copy_file_to_modifyfolder(
                        t_corpus["audio_url"], speaker=cor["D"], language=cor["E"]
                    )  # 放进中间状态
                    duration = get_audio_duration(new_path)
                    name = os.path.basename(new_path)
                    if duration["status"] == "error":
                        continue
                    file_name_pinyin = chinese_to_pinyin(t_corpus["text"])
                    audio = CorpusAudio(
                        aud_id="audio_" + str(aud_id_random),
                        aud_url=new_path,
                        pinyin=file_name_pinyin,
                        audio_duration=duration["data"],
                    )
                    save_audio = CorpusOperateDao.saveAudio(audio)
                    c_items["audio_duration"] = duration["data"]
                    c_items["audio_url"] = name
                    c_items["expect_result"] = t_corpus["expect_result"]
                    c_items["pinyin"] = file_name_pinyin
                else:
                    c_items["audio_duration"] = ""
                    c_items["audio_url"] = ""
                    c_items["expect_result"] = ""
                    c_items["pinyin"] = ""
                c_items["aud_id"] = "audio_" + str(aud_id_random)
                # c_items["audio_duration"] = duration["data"]
                # c_items["audio_url"] = name
                c_items["expect_result"] = t_corpus["expect_result"]
                # c_items["pinyin"] = file_name_pinyin
                c_items["text"] = t_corpus["text"]
                corpus_Items.append(c_items)
                # else:
                #     continue
            create_multi_corpus(
                cor["A"],
                "intelligent-interaction",
                "continuous-dialogue-interation",
                cor["D"],
                cor["E"],
                cor["B"],
                cor["F"],
                corpus_Items,
            )

    return


def excel_create_syncorpus(file, file_data, data):
    # print(data)
    file_name, file_extension = os.path.splitext(file.filename)
    excel_dir = get_excel_dir()
    storage_import_excel = os.path.join(excel_dir, "import")
    if not os.path.exists(storage_import_excel):
        os.makedirs(storage_import_excel)
    filepath = os.path.join(storage_import_excel, file.filename)
    with open(filepath, "wb") as file:
        file.write(file_data)
    corpuslist = analysis_excel(filepath)
    for corp in corpuslist:
        svc = SynthesizeSvc()
        audio_id = find_table_id("corpus_audio_id")
        audio_id_str = "audio_" + str(audio_id)
        # name = generate_random_string()
        ret, url = svc.synthesize(
            corp["A"], audio_id_str, corp["B"], corp["C"], corp["D"], corp["G"]
        )
        if corp["A"] is None:
            continue
        file_name_pinyin = chinese_to_pinyin(corp["A"])
        duration = get_audio_duration(url)
        if duration["status"] == "error":
            if os.path.exists(url):
                os.remove(url)
            continue
        audio = CorpusAudio(
            aud_id=audio_id_str,
            aud_url=url,
            pinyin=file_name_pinyin,
            audio_duration=duration["data"],
        )
        save_audio = CorpusOperateDao.saveAudio(audio)
        name = os.path.basename(url)
        if corp["B"] == Vocalists.female:
            voice = "female"
        elif corp["B"] == Vocalists.male:
            voice = "male"
        else:
            voice = ""
        # language = "mandarin"
        if corp["D"] == 2:
            corpus_id = find_table_id("rouse_corpus_id")
            corpus = RouseCorpus(
                corpus_id="rousecorpus_" + str(corpus_id),
                aud_id=save_audio.aud_id,
                text=corp["A"],
                pinyin=file_name_pinyin,
                audio_url=name,
                audio_duration=duration["data"],
                speaker=voice,
                language=corp["C"],
                test_scenario="wake-up",
                # label=corp["E"],
                audio_path=url,
                is_tts=True,
            )
            label_temp = CorpusLabel(
                corpus_id="rousecorpus_" + str(corpus_id),
                label_name=corp["E"],
            )
            CorpusOperateDao.saveCorpusLabel(label_temp)
        elif corp["D"] == 3:
            corpus_id = find_table_id("disturb_corpus_id")
            corpus = DisturbCorpus(
                corpus_id="disturbcorpus_" + str(corpus_id),
                aud_id=save_audio.aud_id,
                text=corp["A"],
                pinyin=file_name_pinyin,
                audio_url=name,
                audio_duration=duration["data"],
                speaker=voice,
                language=corp["C"],
                label=corp["E"],
                audio_path=url,
                is_tts=True,
            )
            label_temp = CorpusLabel(
                corpus_id="disturbcorpus_" + str(corpus_id),
                label_name=corp["E"],
            )
            CorpusOperateDao.saveCorpusLabel(label_temp)
        else:
            corpus_id = find_table_id("test_corpus_id")
            corpus = TestCorpus(
                corpus_id="testcorpus_" + str(corpus_id),
                aud_id=save_audio.aud_id,
                text=corp["A"],
                pinyin=file_name_pinyin,
                audio_url=name,
                audio_duration=duration["data"],
                speaker=voice,
                language=corp["C"],
                # label=corp["E"],
                expect_result=corp["F"],
                audio_path=url,
                is_tts=True,
            )
            label_temp = CorpusLabel(
                corpus_id="testcorpus_" + str(corpus_id),
                label_name=corp["E"],
            )
            CorpusOperateDao.saveCorpusLabel(label_temp)
        save_corpus = CorpusOperateDao.saveTestCorpus(corpus)
        CorpusOperateDao.updateAudio(
            save_audio.aud_id, {"corpus_id": save_corpus.corpus_id}
        )
    return {"success": True}


def test_corpus_create(data):
    if "aud_id" not in data or data["aud_id"] == "":
        return {}
    filepath = get_file_dir()
    file_path = os.path.join(filepath, data["audio_url"])
    new_path = move_file_to_folder(file_path, "test_corpus")
    corpus_id = find_table_id("test_corpus_id")
    corpus = TestCorpus(
        corpus_id="testcorpus_" + str(corpus_id),
        aud_id=data["aud_id"],
        text=data["text"],
        pinyin=data["pinyin"],
        test_scenario=data["test_scenario"],
        test_type=data["test_type"],
        evaluation_metric=data["evaluation_metric"],
        audio_url=data["audio_url"],
        audio_duration=data["audio_duration"],
        speaker=data["speaker"],
        language=data["language"],
        car_function=data["car_function"],
        expect_result=data["expect_result"],
        audio_path=new_path,
    )
    save_corpus = CorpusOperateDao.saveTestCorpus(corpus)
    updated_data = {"corpus_id": save_corpus.corpus_id, "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(data["aud_id"], updated_data)
    return {"status": "success", "error_msg": ""}


def upload_testcorpus(corpus_id, aud_id, audio_url, pinyin, audio_duration, text):
    if aud_id == "" or aud_id is None or audio_url is None:
        return "aud_id is null"
    filepath = get_file_dir()
    file_path = os.path.join(filepath, audio_url)
    new_path = ""
    if os.path.exists(file_path):
        check_path = os.path.join(filepath, "test_corpus")
        check_file = os.path.join(check_path, audio_url)
        if os.path.exists(check_file):
            original_file_name = os.path.basename(file_path)
            file_name, file_extension = os.path.splitext(original_file_name)
            new_file_name = ""
            new_file_path = ""
            counter = 1
            while os.path.exists(check_file):
                new_file_name = f"{file_name}_{counter}_{file_extension}"
                new_file_path = os.path.join(filepath, new_file_name)
                check_file = os.path.join(check_path, new_file_name)
                counter += 1
            os.rename(file_path, new_file_path)
            file_path = new_file_path
            audio_url = new_file_name
        new_path = move_file_to_folder(file_path, "test_corpus")
    else:
        filepath1 = os.path.join(filepath, "test_corpus")
        filepath2 = os.path.join(filepath1, audio_url)
        if os.path.exists(filepath2):
            new_path = filepath2
        else:
            return "No available audio file could be found"
    corpus_data = {
        "text": text,
        "pinyin": pinyin,
        "aud_id": aud_id,
        "audio_url": audio_url,
        "audio_path": new_path,
        "audio_duration": audio_duration,
    }
    # 删除老音频
    corpus = CorpusQueryDao.findTestCorpusById(corpus_id)
    if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
        os.remove(corpus.audio_path)

    update_corpus = CorpusOperateDao.updateTestCorpus(corpus_id, corpus_data)
    audio_data = {"corpus_id": corpus_id, "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(aud_id, audio_data)
    return 0


def create_testcorpus(
        text,
        test_type,
        test_scenario,
        speaker,
        language,
        car_function,
        labels,
        expect_result,
        is_multi=False,
        target_signal=None,
):
    if target_signal is None:
        target_signal = []
    file_name_pinyin = chinese_to_pinyin(text)
    corpus_id = find_table_id("test_corpus_id")
    signal = []
    for i in target_signal:
        a = {
            "title": i["title"],
            "values": i["values"]
        }
        signal.append(a)
    corpus = TestCorpus(
        corpus_id="testcorpus_" + str(corpus_id),
        text=text,
        pinyin=file_name_pinyin,
        test_scenario=test_scenario,
        test_type=test_type,
        speaker=speaker,
        language=language,
        car_function=car_function,
        # label=label,
        expect_result=expect_result,
        target_signal=str(signal),
        is_multi=is_multi,
    )
    save_corpus = CorpusOperateDao.saveTestCorpus(corpus)
    label_list = []
    for l in labels:
        label_temp = CorpusLabel(
            corpus_id="testcorpus_" + str(corpus_id),
            label_name=l
        )
        label_list.append(label_temp)
    CorpusOperateDao.saveCorpusLabels(label_list)
    return save_corpus


def synthesize_testcorpus(corpus_ids, labels, is_tone, is_normalize_loudness=False):
    svc = SynthesizeSvc()
    for corpus_id in corpus_ids:
        corpus = CorpusQueryDao.findTestCorpusById(corpus_id)
        audio_id = find_table_id("corpus_audio_id")
        audio_id_str = "audio_" + str(audio_id)
        voice = -1
        if corpus.speaker == "male":
            voice = 1
        elif corpus.speaker == "female":
            voice = 2
        ret, url = svc.synthesize(
            corpus.text, audio_id_str, voice, int(corpus.language), 1, is_tone, is_normalize_loudness
        )
        if ret:
            return "synthesize_testcorpus fail"
        duration = get_audio_duration(url)
        if duration["status"] == "error":
            if os.path.exists(url):
                os.remove(url)
            return duration["data"]

        audio = CorpusAudio(
            aud_id=audio_id_str,
            corpus_id=corpus_id,
            aud_url=url,
            pinyin=corpus.pinyin,
            audio_duration=duration["data"],
        )
        save_audio = CorpusOperateDao.saveAudio(audio)
        name = os.path.basename(url)
        # 删除老音频
        if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
            os.remove(corpus.audio_path)
            if corpus.aud_id is not None:
                CorpusOperateDao.deleteAudio(corpus.aud_id)
        updated_data = {
            "aud_id": save_audio.aud_id,
            "audio_url": name,
            "audio_duration": duration["data"],
            "audio_path": url,
            # "label": label,
            "is_tts": True,
        }
        # CorpusOperateDao.deleteCorpusLabel(corpus_id)
        label_list = []
        for l in labels:
            label_temp = CorpusLabel(
                corpus_id=corpus_id,
                label_name=l
            )
            label_list.append(label_temp)
        CorpusOperateDao.saveCorpusLabels(label_list)
        CorpusOperateDao.updateTestCorpus(corpus_id, updated_data)
    return 0


def test_corpus_update(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return {"status": "error", "error_msg": "the test_corpus is in use"}
    updated_data = {
        "text": data["text"],
        "pinyin": data["pinyin"],
        "test_scenario": data["test_scenario"],
        "test_type": data["test_type"],
        "evaluation_metric": data["evaluation_metric"],
        "audio_url": data["audio_url"],
        "audio_duration": data["audio_duration"],
        "speaker": data["speaker"],
        "language": data["language"],
        "car_function": data["car_function"],
        "expect_result": data["expect_result"],
    }

    update_corpus = CorpusOperateDao.updateTestCorpus(data["corpus_id"], updated_data)
    return {"status": "success", "error_msg": ""}


def update_testcorpus(
        corpus_id,
        text,
        test_type,
        test_scenario,
        speaker,
        language,
        car_function,
        labels,
        expect_result,
        target_signal=None
):
    if target_signal is None:
        target_signal = []
    find_corpus = {"corpus_id": corpus_id}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return f"语料{corpus_id}在配置中"
    file_name_pinyin = chinese_to_pinyin(text)
    signal = []
    for i in target_signal:
        a = {
            "title": i["title"],
            "values": i["values"]
        }
        signal.append(a)
    updated_data = {
        "text": text,
        "pinyin": file_name_pinyin,
        "test_scenario": test_scenario,
        "test_type": test_type,
        "speaker": speaker,
        "language": language,
        "car_function": car_function,
        # "label": label,
        "expect_result": expect_result,
        "target_signal": str(signal),
    }
    CorpusOperateDao.deleteCorpusLabel(corpus_id)
    label_list = []
    for l in labels:
        label_temp = CorpusLabel(
            corpus_id=corpus_id,
            label_name=l
        )
        label_list.append(label_temp)
    CorpusOperateDao.saveCorpusLabels(label_list)
    CorpusOperateDao.updateTestCorpus(corpus_id, updated_data)
    return 0


def test_corpus_delete(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    if len(in_use) != 0:
        return {"status": "error", "error_msg": "the corpus is in use"}
    corpus = CorpusQueryDao.findTestCorpusById(data["corpus_id"])
    audio = CorpusQueryDao.findAudioById(corpus.aud_id)
    if audio is not None:
        if os.path.exists(audio.aud_url):
            os.remove(audio.aud_url)
        CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteTestCorpus(data["corpus_id"])
    return {"status": "success", "error_msg": ""}


def delete_testcorpus(corpus_id):
    find_corpus = {"corpus_id": corpus_id}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    if len(in_use) != 0:
        return f"语料{corpus_id}在配置中"
    corpus = CorpusQueryDao.findTestCorpusById(corpus_id)
    if corpus is None:
        return 0
    if corpus.aud_id is not None:
        check_corpus = CorpusQueryDao.findAudioInCorpus(corpus.aud_id)
        if len(check_corpus) > 1:
            pass
        else:
            audio = CorpusQueryDao.findAudioById(corpus.aud_id)
            if audio is not None:
                if os.path.exists(audio.aud_url):
                    os.remove(audio.aud_url)
                CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteTestCorpus(corpus_id)
    return 0


def test_corpus_list(data):
    corpus_list, total = CorpusQueryDao.showAllTestCorpus(data)
    res = []
    for corpus in corpus_list:
        temp = {}
        if corpus.is_multi:
            continue
        temp["corpus_id"] = corpus.corpus_id
        temp["aud_id"] = corpus.aud_id
        temp["text"] = corpus.text
        temp["pinyin"] = corpus.pinyin
        temp["test_scenario"] = corpus.test_scenario
        temp["test_type"] = corpus.test_type
        temp["evaluation_metric"] = corpus.evaluation_metric
        temp["audio_url"] = corpus.audio_url
        temp["audio_duration"] = corpus.audio_duration
        temp["speaker"] = corpus.speaker
        temp["language"] = corpus.language
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            if label.label_name is not None:
                label_list.append(label.label_name)
        if "label" in data and data['label'] != "" and len(label_list) >= 1:
            got_it = False
            for ll in label_list:
                if data["label"] in ll:
                    got_it = True
                    break
            if not got_it:
                continue
        elif "label" in data and data['label'] != "":
            continue
        temp["label"] = label_list
        temp["operation"] = corpus.operation
        temp["car_function"] = corpus.car_function
        temp["expect_result"] = corpus.expect_result
        if corpus.target_signal is not None:
            temp["target_signal"] = ast.literal_eval(corpus.target_signal)
        else:
            temp["target_signal"] = ""
        temp["audio_path"] = corpus.audio_path
        temp["is_tts"] = corpus.is_tts

        res.append(temp)
    # res.reverse()
    return res, total


def rouse_corpus_create(data):
    if "aud_id" not in data or data["aud_id"] == "":
        return {}
    corpus_id = find_table_id("rouse_corpus_id")
    filepath = get_file_dir()
    file_path = os.path.join(filepath, data["audio_url"])
    new_path = move_file_to_folder(file_path, "rouse_corpus")
    corpus = RouseCorpus(
        corpus_id="rousecorpus_" + str(corpus_id),
        aud_id=data["aud_id"],
        text=data["text"],
        pinyin=data["pinyin"],
        test_scenario=data["test_scenario"],
        test_object=data["test_object"],
        audio_url=data["audio_url"],
        audio_duration=data["audio_duration"],
        speaker=data["speaker"],
        language=data["language"],
        audio_path=new_path,
    )
    save_corpus = CorpusOperateDao.saveRouseCorpus(corpus)
    updated_data = {"corpus_id": save_corpus.corpus_id, "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(data["aud_id"], updated_data)
    return {"status": "success", "error_msg": ""}


def create_rousecorpus(text, test_scenario, speaker, language, labels):
    file_name_pinyin = chinese_to_pinyin(text)
    corpus_id = find_table_id("rouse_corpus_id")
    corpus = RouseCorpus(
        corpus_id="rousecorpus_" + str(corpus_id),
        text=text,
        pinyin=file_name_pinyin,
        test_scenario=test_scenario,
        speaker=speaker,
        language=language,
        # label=label,
    )
    label_list = []
    for l in labels:
        label_temp = CorpusLabel(
            corpus_id="rousecorpus_" + str(corpus_id),
            label_name=l
        )
        label_list.append(label_temp)
    CorpusOperateDao.saveCorpusLabels(label_list)
    save_corpus = CorpusOperateDao.saveRouseCorpus(corpus)
    return save_corpus


def update_rousecorpus(corpus_id, text, test_scenario, speaker, language, labels):
    find_corpus = {"corpus_id": corpus_id}
    in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return f"语料{corpus_id}在配置中"
    file_name_pinyin = chinese_to_pinyin(text)
    updated_data = {
        "text": text,
        "pinyin": file_name_pinyin,
        "test_scenario": test_scenario,
        "speaker": speaker,
        "language": language,
        # "label": label,
    }
    CorpusOperateDao.deleteCorpusLabel(corpus_id)
    label_list = []
    for l in labels:
        label_temp = CorpusLabel(
            corpus_id=corpus_id,
            label_name=l
        )
        label_list.append(label_temp)
    CorpusOperateDao.saveCorpusLabels(label_list)
    CorpusOperateDao.updateRouseCorpus(corpus_id, updated_data)
    return 0


def upload_rousecorpus(corpus_id, aud_id, audio_url, pinyin, audio_duration, text):
    if aud_id == "" or aud_id is None or audio_url is None:
        return "aud_id is null"
    filepath = get_file_dir()
    file_path = os.path.join(filepath, audio_url)
    new_path = ""
    if os.path.exists(file_path):
        check_path = os.path.join(filepath, "rouse_corpus")
        check_file = os.path.join(check_path, audio_url)
        if os.path.exists(check_file):
            original_file_name = os.path.basename(file_path)
            file_name, file_extension = os.path.splitext(original_file_name)
            new_file_name = ""
            new_file_path = ""
            counter = 1
            while os.path.exists(check_file):
                new_file_name = f"{file_name}_{counter}_{file_extension}"
                new_file_path = os.path.join(filepath, new_file_name)
                check_file = os.path.join(check_path, new_file_name)
                counter += 1
            os.rename(file_path, new_file_path)
            file_path = new_file_path
            audio_url = new_file_name
        new_path = move_file_to_folder(file_path, "rouse_corpus")
    else:
        filepath1 = os.path.join(filepath, "rouse_corpus")
        filepath2 = os.path.join(filepath1, audio_url)
        if os.path.exists(filepath2):
            new_path = filepath2
        else:
            return "No available audio file could be found"
    corpus_data = {
        "text": text,
        "pinyin": pinyin,
        "aud_id": aud_id,
        "audio_url": audio_url,
        "audio_path": new_path,
        "audio_duration": audio_duration,
    }
    # 删除老音频
    corpus = CorpusQueryDao.findRouseCorpusById(corpus_id)
    if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
        os.remove(corpus.audio_path)
    update_corpus = CorpusOperateDao.updateRouseCorpus(corpus_id, corpus_data)
    audio_data = {"corpus_id": corpus_id, "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(aud_id, audio_data)
    return 0


def delete_rousecorpus(corpus_id):
    find_corpus = {"corpus_id": corpus_id}
    in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
    if len(in_use) != 0:
        return f"语料{corpus_id}在配置中"
    corpus = CorpusQueryDao.findRouseCorpusById(corpus_id)
    if corpus is None:
        return 0
    if corpus.aud_id is not None:
        audio = CorpusQueryDao.findAudioById(corpus.aud_id)
        if audio is not None:
            if os.path.exists(audio.aud_url):
                os.remove(audio.aud_url)
            CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteRouseCorpus(corpus_id)
    return 0


def rouse_corpus_update(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return {"status": "error", "error_msg": f"语料{data['corpus_id']}在配置中"}
    updated_data = {
        "text": data["text"],
        "pinyin": data["pinyin"],
        "test_scenario": data["test_scenario"],
        "test_object": data["test_object"],
        "audio_url": data["audio_url"],
        "audio_duration": data["audio_duration"],
        "speaker": data["speaker"],
        "language": data["language"],
    }

    CorpusOperateDao.updateRouseCorpus(data["corpus_id"], updated_data)
    return {"status": "success", "error_msg": ""}


def rouse_corpus_delete(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
    if len(in_use) != 0:
        return {"status": "error", "error_msg": "the corpus is in use"}
    corpus = CorpusQueryDao.findRouseCorpusById(data["corpus_id"])
    audio = CorpusQueryDao.findAudioById(corpus.aud_id)
    if audio is not None:
        if os.path.exists(audio.aud_url):
            os.remove(audio.aud_url)
        CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteRouseCorpus(data["corpus_id"])
    return {"status": "success", "error_msg": ""}


def synthesize_rousecorpus(corpus_ids, labels):
    svc = SynthesizeSvc()
    for corpus_id in corpus_ids:
        corpus = CorpusQueryDao.findRouseCorpusById(corpus_id)
        audio_id = find_table_id("corpus_audio_id")
        audio_id_str = "audio_" + str(audio_id)
        voice = -1
        if corpus.speaker == "male":
            voice = 1
        elif corpus.speaker == "female":
            voice = 2
        ret, url = svc.synthesize(
            corpus.text, audio_id_str, voice, int(corpus.language), 2
        )
        if ret:
            return "synthesize_rousecorpus fail"
        duration = get_audio_duration(url)
        if duration["status"] == "error":
            if os.path.exists(url):
                os.remove(url)
            return duration["data"]
        audio_id = find_table_id("corpus_audio_id")
        audio = CorpusAudio(
            aud_id=audio_id_str,
            corpus_id=corpus_id,
            aud_url=url,
            pinyin=corpus.pinyin,
            audio_duration=duration["data"],
        )
        save_audio = CorpusOperateDao.saveAudio(audio)
        name = os.path.basename(url)
        # 删除老音频
        if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
            os.remove(corpus.audio_path)
            if corpus.aud_id is not None:
                CorpusOperateDao.deleteAudio(corpus.aud_id)
        updated_data = {
            "aud_id": save_audio.aud_id,
            "audio_url": name,
            "audio_duration": duration["data"],
            "audio_path": url,
            # "label": label,
            "is_tts": True,
        }
        # CorpusOperateDao.deleteCorpusLabel(corpus_id)
        label_list = []
        for l in labels:
            label_temp = CorpusLabel(
                corpus_id=corpus_id,
                label_name=l
            )
            label_list.append(label_temp)
        CorpusOperateDao.saveCorpusLabels(label_list)
        CorpusOperateDao.updateRouseCorpus(corpus_id, updated_data)
    return 0


def rouse_corpus_list(data):
    corpus_list, total = CorpusQueryDao.showAllRouseCorpus(data)
    res = []
    for corpus in corpus_list:
        temp = {"corpus_id": corpus.corpus_id, "aud_id": corpus.aud_id, "text": corpus.text, "pinyin": corpus.pinyin,
                "test_scenario": corpus.test_scenario, "test_object": corpus.test_object, "audio_url": corpus.audio_url,
                "audio_duration": corpus.audio_duration, "speaker": corpus.speaker, "language": corpus.language}
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            if label.label_name is not None:
                label_list.append(label.label_name)
        if "label" in data and data['label'] != "" and len(label_list) >= 1:
            got_it = False
            for ll in label_list:
                if data["label"] in ll:
                    got_it = True
                    break
            if not got_it:
                continue
        elif "label" in data and data['label'] != "":
            continue
        temp["label"] = label_list
        temp["operation"] = corpus.operation
        temp["audio_path"] = corpus.audio_path
        temp["is_tts"] = corpus.is_tts
        res.append(temp)
    return res, total


def disturb_corpus_create(data):
    # if "aud_id" not in data or data["aud_id"] == "":
    #     return ""
    corpus_id = find_table_id("disturb_corpus_id")
    filepath = get_file_dir()
    # new_path = move_file_to_folder(file_path, "disturb_corpus")
    new_path = ""
    audio_url = data["audio_url"]
    if data["audio_url"] != "":
        file_path = os.path.join(filepath, data["audio_url"])
        if os.path.exists(file_path):
            check_path = os.path.join(filepath, "disturb_corpus")
            check_file = os.path.join(check_path, audio_url)
            if os.path.exists(check_file):
                original_file_name = os.path.basename(file_path)
                file_name, file_extension = os.path.splitext(original_file_name)
                new_file_name = ""
                new_file_path = ""
                counter = 1
                while os.path.exists(check_file):
                    new_file_name = f"{file_name}_{counter}_{file_extension}"
                    new_file_path = os.path.join(filepath, new_file_name)
                    check_file = os.path.join(check_path, new_file_name)
                    counter += 1
                os.rename(file_path, new_file_path)
                file_path = new_file_path
                audio_url = new_file_name
            new_path = move_file_to_folder(file_path, "disturb_corpus")
        else:
            filepath1 = os.path.join(filepath, "disturb_corpus")
            filepath2 = os.path.join(filepath1, audio_url)
            if os.path.exists(filepath2):
                new_path = filepath2
            else:
                return "No available audio file could be found"
    corpus = DisturbCorpus(
        corpus_id="disturbcorpus_" + str(corpus_id),
        aud_id=data["aud_id"],
        text=data["text"],
        pinyin=data["pinyin"],
        audio_url=audio_url,
        audio_duration=data["audio_duration"],
        speaker=data["speaker"],
        language=data["language"],
        audio_path=new_path,
    )
    save_corpus = CorpusOperateDao.saveDisturbCorpus(corpus)
    if data["audio_url"] != "":
        updated_data = {"corpus_id": save_corpus.corpus_id, "aud_url": new_path}
        update_audio = CorpusOperateDao.updateAudio(data["aud_id"], updated_data)
    return 0


def disturb_corpus_update(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllDCorpusTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return "the disturb_corpus is in use"
    file_name_pinyin = chinese_to_pinyin(data["text"])
    filepath = get_file_dir()
    audio_url = data["audio_url"]
    file_path = os.path.join(filepath, audio_url)
    new_path = ""
    if os.path.exists(file_path):
        check_path = os.path.join(filepath, "disturb_corpus")
        check_file = os.path.join(check_path, audio_url)
        if os.path.exists(check_file):
            original_file_name = os.path.basename(file_path)
            file_name, file_extension = os.path.splitext(original_file_name)
            new_file_name = ""
            new_file_path = ""
            counter = 1
            while os.path.exists(check_file):
                new_file_name = f"{file_name}_{counter}_{file_extension}"
                new_file_path = os.path.join(filepath, new_file_name)
                check_file = os.path.join(check_path, new_file_name)
                counter += 1
            os.rename(file_path, new_file_path)
            file_path = new_file_path
            audio_url = new_file_name
        new_path = move_file_to_folder(file_path, "disturb_corpus")
    else:
        filepath1 = os.path.join(filepath, "disturb_corpus")
        filepath2 = os.path.join(filepath1, audio_url)
        if os.path.exists(filepath2):
            new_path = filepath2
        else:
            return "No available audio file could be found"
    updated_data = {
        "aud_id": data["aud_id"],
        "text": data["text"],
        "pinyin": file_name_pinyin,
        "audio_url": audio_url,
        "audio_path": new_path,
        "audio_duration": data["audio_duration"],
        "speaker": data["speaker"],
        "language": data["language"],
    }
    # 删除老音频
    corpus = CorpusQueryDao.findDisturbCorpusById(data["corpus_id"])
    if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
        os.remove(corpus.audio_path)
    update_corpus = CorpusOperateDao.updateDisturbCorpus(
        data["corpus_id"], updated_data
    )
    audio_data = {"corpus_id": data["corpus_id"], "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(data["aud_id"], audio_data)
    return 0


def disturb_corpus_delete(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllDCorpusTree(find_corpus)
    if len(in_use) != 0:
        return 1, "the corpus is in use"
    corpus = CorpusQueryDao.findDisturbCorpusById(data["corpus_id"])
    audio = CorpusQueryDao.findAudioById(corpus.aud_id)
    if audio is not None:
        if os.path.exists(audio.aud_url):
            os.remove(audio.aud_url)
        CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteDisturbCorpus(data["corpus_id"])
    return 0, ""


def disturb_corpus_list(data):
    corpus_list, total = CorpusQueryDao.showAllDisturbCorpus(data)
    res = []
    for corpus in corpus_list:
        temp = {}
        temp["corpus_id"] = corpus.corpus_id
        temp["aud_id"] = corpus.aud_id
        temp["text"] = corpus.text
        temp["pinyin"] = corpus.pinyin
        temp["audio_url"] = corpus.audio_url
        temp["audio_duration"] = corpus.audio_duration
        temp["speaker"] = corpus.speaker
        temp["language"] = corpus.language
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            if label.label_name is not None:
                label_list.append(label.label_name)
        if "label" in data and data['label'] != "" and len(label_list) >= 1:
            got_it = False
            for ll in label_list:
                if data["label"] in ll:
                    got_it = True
                    break
            if got_it == False:
                continue
        elif "label" in data and data['label'] != "":
            continue
        temp["label"] = label_list
        temp["operation"] = corpus.operation
        temp["audio_path"] = corpus.audio_path
        temp["is_tts"] = corpus.is_tts
        res.append(temp)
    return res, total


def background_noise_create(data):
    corpus_id = find_table_id("background_noise_id")
    filepath = get_file_dir()
    new_path = ""
    audio_url = data["audio_url"]
    if data["audio_url"] != "":
        file_path = os.path.join(filepath, data["audio_url"])
        if os.path.exists(file_path):
            check_path = os.path.join(filepath, "background_noise")
            check_file = os.path.join(check_path, audio_url)
            if os.path.exists(check_file):
                original_file_name = os.path.basename(file_path)
                file_name, file_extension = os.path.splitext(original_file_name)
                new_file_name = ""
                new_file_path = ""
                counter = 1
                while os.path.exists(check_file):
                    new_file_name = f"{file_name}_{counter}_{file_extension}"
                    new_file_path = os.path.join(filepath, new_file_name)
                    check_file = os.path.join(check_path, new_file_name)
                    counter += 1
                os.rename(file_path, new_file_path)
                file_path = new_file_path
                audio_url = new_file_name
            new_path = move_file_to_folder(file_path, "background_noise")
        else:
            filepath1 = os.path.join(filepath, "background_noise")
            filepath2 = os.path.join(filepath1, audio_url)
            if os.path.exists(filepath2):
                new_path = filepath2
            else:
                return "No available audio file could be found"
    corpus = BackgroundNoise(
        corpus_id="backgroundnoise_" + str(corpus_id),
        aud_id=data["aud_id"],
        text=data["text"],
        pinyin=data.get("pinyin", ""),
        noise_environ=data.get("noise_environ", ""),
        audio_url=audio_url,
        audio_duration=data["audio_duration"],
        audio_path=new_path,
    )
    save_corpus = CorpusOperateDao.saveBackgroundNoise(corpus)
    if data["audio_url"] != "":
        updated_data = {"corpus_id": save_corpus.corpus_id, "aud_url": new_path}
        update_audio = CorpusOperateDao.updateAudio(data["aud_id"], updated_data)
    return 0


def background_noise_update(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllBNoiseTree(find_corpus)
    for ii in in_use:
        plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
        check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
        if check_status.project_status == "progressing":
            return "the background noise is in use"
    file_name_pinyin = chinese_to_pinyin(data["text"])
    filepath = get_file_dir()
    audio_url = data["audio_url"]
    file_path = os.path.join(filepath, audio_url)
    new_path = ""
    if os.path.exists(file_path):
        check_path = os.path.join(filepath, "background_noise")
        check_file = os.path.join(check_path, audio_url)
        if os.path.exists(check_file):
            original_file_name = os.path.basename(file_path)
            file_name, file_extension = os.path.splitext(original_file_name)
            new_file_name = ""
            new_file_path = ""
            counter = 1
            while os.path.exists(check_file):
                new_file_name = f"{file_name}_{counter}_{file_extension}"
                new_file_path = os.path.join(filepath, new_file_name)
                check_file = os.path.join(check_path, new_file_name)
                counter += 1
            os.rename(file_path, new_file_path)
            file_path = new_file_path
            audio_url = new_file_name
        new_path = move_file_to_folder(file_path, "background_noise")
    else:
        filepath1 = os.path.join(filepath, "background_noise")
        filepath2 = os.path.join(filepath1, audio_url)
        if os.path.exists(filepath2):
            new_path = filepath2
        else:
            return "No available audio file could be found"
    updated_data = {
        "aud_id": data["aud_id"],
        "text": data["text"],
        "pinyin": file_name_pinyin,
        "noise_environ": data.get("noise_environ", ""),
        "audio_url": audio_url,
        "audio_path": new_path,
    }
    # 删除老音频
    corpus = CorpusQueryDao.findBackgroundNoiseById(data["corpus_id"])
    if corpus.audio_path is not None and os.path.exists(corpus.audio_path):
        os.remove(corpus.audio_path)
    update_corpus = CorpusOperateDao.updateBackgroundNoise(
        data["corpus_id"], updated_data
    )
    audio_data = {"corpus_id": data["corpus_id"], "aud_url": new_path}
    update_audio = CorpusOperateDao.updateAudio(data["aud_id"], audio_data)
    return 0


def background_noise_delete(data):
    find_corpus = {"corpus_id": data["corpus_id"]}
    in_use = PlanQueryDao.showAllBNoiseTree(find_corpus)
    if len(in_use) != 0:
        return 1, "the corpus is in use"
    corpus = CorpusQueryDao.findBackgroundNoiseById(data["corpus_id"])
    audio = CorpusQueryDao.findAudioById(corpus.aud_id)
    if audio is not None:
        if os.path.exists(audio.aud_url):
            os.remove(audio.aud_url)
        CorpusOperateDao.deleteAudio(audio.aud_id)
    CorpusOperateDao.deleteBackgroundNoise(data["corpus_id"])
    return 0, ""


def background_noise_list(data):
    corpus_list, total = CorpusQueryDao.showAllBackgroundNoise(data)
    res = []
    for corpus in corpus_list:
        temp = {}
        temp["corpus_id"] = corpus.corpus_id
        temp["aud_id"] = corpus.aud_id
        temp["text"] = corpus.text
        temp["pinyin"] = corpus.pinyin
        temp["noise_environ"] = corpus.noise_environ
        temp["audio_url"] = corpus.audio_url
        temp["audio_duration"] = corpus.audio_duration
        labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": corpus.corpus_id})
        label_list = []
        for label in labels:
            if label.label_name is not None:
                label_list.append(label.label_name)
        if "label" in data and data['label'] != "" and len(label_list) >= 1:
            got_it = False
            for ll in label_list:
                if data["label"] in ll:
                    got_it = True
                    break
            if got_it == False:
                continue
        elif "label" in data and data['label'] != "":
            continue
        temp["label"] = label_list
        temp["operation"] = corpus.operation
        temp["audio_path"] = corpus.audio_path
        res.append(temp)
    return res, total


def test_corpus_batch_delete(data):
    if "corpus_ids" not in data and len(data["corpus_ids"]) == 0:
        return "No corpus was found to delete"
    for corpus_temp in data["corpus_ids"]:
        find_corpus = {"corpus_id": corpus_temp}
        in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
        if len(in_use) != 0:
            continue
        corpus = CorpusQueryDao.findTestCorpusById(corpus_temp)
        audio = CorpusQueryDao.findAudioById(corpus.aud_id)
        if audio is not None:
            if os.path.exists(audio.aud_url):
                os.remove(audio.aud_url)
            CorpusOperateDao.deleteAudio(audio.aud_id)
        delete_corpus = CorpusOperateDao.deleteTestCorpus(corpus_temp)
    return {"status": "success", "error_msg": ""}


def batch_delete_testcorpus(corpus_ids):
    delete_error = []
    if len(corpus_ids) == 0:
        return "No corpus was found to delete"
    for corpus_temp in corpus_ids:
        find_corpus = {"corpus_id": corpus_temp}
        in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
        if len(in_use) != 0:
            delete_error.append(corpus_temp)
            continue
        corpus = CorpusQueryDao.findTestCorpusById(corpus_temp)
        if corpus is None:
            continue
        if corpus.aud_id is not None:
            audio = CorpusQueryDao.findAudioById(corpus.aud_id)
            if audio is not None:
                if os.path.exists(audio.aud_url):
                    os.remove(audio.aud_url)
                CorpusOperateDao.deleteAudio(audio.aud_id)
        CorpusOperateDao.deleteTestCorpus(corpus_temp)
    return delete_error


def batch_delete_rousecorpus(corpus_ids):
    delete_error = []
    if len(corpus_ids) == 0:
        return "No corpus was found to delete"
    for corpus_temp in corpus_ids:
        find_corpus = {"corpus_id": corpus_temp}
        in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
        if len(in_use) != 0:
            delete_error.append(corpus_temp)
            continue
        corpus = CorpusQueryDao.findRouseCorpusById(corpus_temp)
        if corpus is None:
            continue
        if corpus.aud_id is not None:
            audio = CorpusQueryDao.findAudioById(corpus.aud_id)
            if audio is not None:
                if os.path.exists(audio.aud_url):
                    os.remove(audio.aud_url)
                CorpusOperateDao.deleteAudio(audio.aud_id)
        delete_corpus = CorpusOperateDao.deleteRouseCorpus(corpus_temp)
    return delete_error


def rouse_corpus_batch_delete(data):
    if "corpus_ids" not in data and len(data["corpus_ids"]) == 0:
        return "No corpus was found to delete"
    for corpus_temp in data["corpus_ids"]:
        find_corpus = {"corpus_id": corpus_temp}
        in_use = PlanQueryDao.showAllRCorpusTree(find_corpus)
        if len(in_use) != 0:
            continue
        corpus = CorpusQueryDao.findRouseCorpusById(corpus_temp)
        audio = CorpusQueryDao.findAudioById(corpus.aud_id)
        if audio is not None:
            if os.path.exists(audio.aud_url):
                os.remove(audio.aud_url)
            CorpusOperateDao.deleteAudio(audio.aud_id)
        delete_corpus = CorpusOperateDao.deleteRouseCorpus(corpus_temp)
    return {"status": "success", "error_msg": ""}


# 多伦对话语料
def create_multi_corpus(
        corpus_name,
        test_type,
        test_scenario,
        speaker,
        language,
        car_function,
        label,
        corpus_items,
        multi_corpus_id="",
        is_update=False,
):
    if multi_corpus_id == "":
        multi_corpus_id = generate_random_string()
    corpus_list = []
    for i, audio in enumerate(corpus_items):
        text = ""
        pinyin = ""
        if audio.get("text"):
            text = audio["text"]
            pinyin = chinese_to_pinyin(text)
        audio_url = audio.get("audio_url", "")
        audio_duration = audio.get("audio_duration", "")
        expect_result = audio.get("expect_result", "")
        aud_id = audio.get("aud_id", "")
        audio_path = audio.get("audio_path", "")
        n_audio_path, n_audio_file = "", ""
        if audio_path:
            n_audio_path, n_audio_file = os.path.split(audio_path)

        # corpus_id = "mul_" + generate_random_string()
        save_corpus = create_testcorpus(
            text,
            test_type,
            test_scenario,
            speaker,
            language,
            car_function,
            [],
            expect_result,
            True,
        )
        if audio_url != "" and is_update == False or (n_audio_file != audio_url and is_update):
            ret = upload_testcorpus(
                save_corpus.corpus_id, aud_id, audio_url, pinyin, audio_duration, text
            )
            # if ret ==  0:
        elif aud_id != "" and is_update:
            audio_path = ""
            aud = CorpusQueryDao.findAudioById(aud_id)
            if aud is not None:
                audio_path = aud.aud_url
            corpus_data = {
                "text": text,
                "pinyin": pinyin,
                "aud_id": aud_id,
                "audio_url": audio_url,
                "audio_path": audio_path,
                "audio_duration": audio_duration,
            }
            update_corpus = CorpusOperateDao.updateTestCorpus(
                save_corpus.corpus_id, corpus_data
            )
            audio_data = {"corpus_id": save_corpus.corpus_id}
            update_audio = CorpusOperateDao.updateAudio(aud_id, audio_data)
        corpus = MultiCorpus(
            corpus_id=multi_corpus_id,
            testcorpus_id=save_corpus.corpus_id,
            corpus_name=corpus_name,
            is_delete=False,
            order=i + 1,
        )
        corpus_list.append(corpus)
    CorpusOperateDao.saveMultiCorpusList(corpus_list)

    label_list = []
    for l in label:
        label_temp = CorpusLabel(corpus_id=multi_corpus_id, label_name=l)
        label_list.append(label_temp)
    CorpusOperateDao.saveCorpusLabels(label_list)
    return 0


def synthesize_multicorpus(corpus_ids, label, is_tone):
    svc = SynthesizeSvc()
    for multi_id in corpus_ids:
        test_config_list = get_multi_testcorpus(multi_id)
        if len(test_config_list) != 0:
            labels = []
            if label != "":
                labels.append(label)
            ret = synthesize_testcorpus(test_config_list, label, is_tone)
            if ret:
                return ret
    return 0


def update_multi_corpus(
        multi_corpus_name,
        multi_corpus_id,
        test_type,
        test_scenario,
        speaker,
        language,
        car_function,
        label,
        corpus_items,
):
    find_corpus = {"corpus_id": multi_corpus_id}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    plan_id_list = []
    project_list_id = []
    for ii in in_use:
        plan_id_list.append(ii.plan_id)
    plan_list = PlanQueryDao.findProjectPlansByIdList(plan_id_list)
    for tmp_plan in plan_list:
        project_list_id.append(tmp_plan.project_id)
    check_status = ProjectQueryDao.findTestProjectsByIdList(project_list_id)
    for status in check_status:
        if status.project_status == "progressing":
            return "the multi_corpus is in use"

    update_data_list = []
    test_croups_list = []
    for i, audio in enumerate(corpus_items):
        text = ""
        pinyin = ""
        if audio.get("id"):
            update_data = {"id": audio.get("id"), "order": i + 1, "corpus_name": multi_corpus_name}
            update_data_list.append(update_data)
        if audio.get("text"):
            text = audio["text"]
            pinyin = chinese_to_pinyin(text)
        audio_url = audio.get("audio_url", "")
        audio_duration = audio.get("audio_duration", "")
        expect_result = audio.get("expect_result", "")
        aud_id = audio.get("aud_id", "")
        audio_path = audio.get("audio_path", "")
        corpus_id = audio.get("corpus_id", "")
        test_update_data = {"corpus_id": corpus_id, "text": text, "pinyin": pinyin, "audio_url": audio_url,
                            "audio_duration": audio_duration, "expect_result": expect_result, "audio_path": audio_path}
        if not corpus_id:
            corpus = create_testcorpus(
                text,
                test_type,
                test_scenario,
                speaker,
                language,
                car_function,
                [],
                expect_result,
                True,
            )
            mul_corpus = MultiCorpus(
                corpus_id=multi_corpus_id,
                testcorpus_id=corpus.corpus_id,
                corpus_name=multi_corpus_name,
                is_delete=False,
                order=i + 1,
            )
            CorpusOperateDao.saveMultiCorpus(mul_corpus)
        else:
            test_croups_list.append(test_update_data)
        if audio_url != "":
            upload_testcorpus(corpus_id, aud_id, audio_url, pinyin, audio_duration, text)
    CorpusOperateDao.updateMultiCorpusBatchById(update_data_list)
    CorpusOperateDao.updateTestCorpusBatchByCorpusId(test_croups_list)

    # 处理标签
    label_list = CorpusQueryDao.showAllCorpusLabel({"corpus_id": multi_corpus_id})
    label_name_map = {item.label_name: 1 for item in label_list}
    if len(label) == 0:
        CorpusOperateDao.deleteCorpusLabel(multi_corpus_id)
    else:
        save_label_list = []
        for l in label:
            if l in label_name_map:
                label_name_map[l] = 0
                continue
            label_temp = CorpusLabel(corpus_id=multi_corpus_id, label_name=l)
            save_label_list.append(label_temp)
        CorpusOperateDao.saveCorpusLabels(save_label_list)
    for label_name, is_delete in label_name_map.items():
        if is_delete:
            CorpusOperateDao.deleteCorpusLabelByName(multi_corpus_id, label_name)
    return 0


def delete_mulcorpus(corpus_id):
    find_corpus = {"corpus_id": corpus_id}
    in_use = PlanQueryDao.showAllTCorpusTree(find_corpus)
    if len(in_use) != 0:
        return f"语料{corpus_id}在配置中"
    # for ii in in_use:
    #     plan = PlanQueryDao.findProjectPlanById(ii.plan_id)
    #     check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
    #     if check_status.project_status == "progressing":
    #         return "the multi_corpus is in use"
    # 删数据库，并删除对应的音频 和 audio_id
    mult_corpus_list = CorpusQueryDao.findMultiCorpusById(corpus_id)
    for cor in mult_corpus_list:
        delete_testcorpus(cor.testcorpus_id)
    CorpusOperateDao.deleteMultiCorpus(corpus_id)
    return 0


def batch_delete_mulcorpus(corpus_ids):
    fail_list = []
    for corpus in corpus_ids:
        ret = delete_mulcorpus(corpus)
        if ret != 0:
            fail_list.append(ret)
    return fail_list


def search_corpus(data, corpus):
    for key, value in data.items():
        if hasattr(TestCorpus, key):
            column_attr = getattr(corpus, key, None)
            if isinstance(value, str) and column_attr is not None:  # 如果值是字符串，则尝试使用模糊匹配
                if value != "" and value not in column_attr:
                    return False
            else:  # 否则使用精确匹配
                if value is not None and value != column_attr:
                    return False
    return True


def mul_corpus_list(data):
    temp_list, total = CorpusQueryDao.showAllMultiCorpus(data)
    res = []
    seen = set()
    corpus_list = []
    for i in temp_list:
        cid = i.corpus_id
        name = i.corpus_name
        if cid not in seen:
            cor = {"id": cid, "name": name}
            corpus_list.append(cor)
            seen.add(cid)
    for cor in corpus_list:
        corpus_id = cor["id"]
        corpus_name = cor["name"]
        temp = {}
        first = True
        audio_list = []
        for ii in temp_list:
            if ii.corpus_id == corpus_id and ii.is_delete == False:
                corpus = CorpusQueryDao.findTestCorpusById(ii.testcorpus_id)
                if corpus is None:
                    continue
                if not search_corpus(data, corpus):
                    continue
                if first:
                    temp["corpus_id"] = corpus_id
                    temp["corpus_name"] = corpus_name
                    temp["test_scenario"] = corpus.test_scenario
                    temp["test_type"] = corpus.test_type
                    temp["speaker"] = corpus.speaker
                    temp["language"] = corpus.language
                    labels = CorpusQueryDao.showAllCorpusLabel({"corpus_id": ii.corpus_id})
                    label_list = []
                    for label in labels:
                        if label.label_name is not None:
                            label_list.append(label.label_name)
                    temp["label"] = label_list
                    temp["car_function"] = corpus.car_function
                    first = False

                audio = {"aud_id": corpus.aud_id, "text": corpus.text, "audio_url": corpus.audio_url,
                         "audio_path": corpus.audio_path, "audio_duration": corpus.audio_duration,
                         "expect_result": corpus.expect_result, "order": ii.order, "corpus_id": corpus.corpus_id,
                         "id": ii.id}
                audio_list.append(audio)
        if "corpus_id" in temp:
            temp["corpusItems"] = sorted(audio_list, key=lambda x: x["order"])
            if len(audio_list) > 0:
                res.append(temp)
    return res, total


def get_multi_testcorpus(multi_id):
    temp_list = CorpusQueryDao.findMultiCorpusById(multi_id)
    res = []
    for ii in temp_list:
        if not ii.is_delete:
            res.append(ii.testcorpus_id)
    return res
