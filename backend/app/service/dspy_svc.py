import json
import os
from datetime import datetime
from urllib.parse import urlparse
import dspy
from app.middleware.log import set_request_id, logger as log
from app.config import globalAppSettings
from openai import OpenAI
from typing import Literal, List, Dict, Any, Optional
from app.service.data_upload import upload_result
import time
import datetime
import re

parsed_url = urlparse(globalAppSettings.OPENAI_API_BASE)
LLM_PORT = parsed_url.port
LLM_HOST = parsed_url.hostname


def init_llm():
    try:
        llm_client = OpenAI(
            api_key=globalAppSettings.OPENAI_API_KEY,
            base_url=globalAppSettings.OPENAI_API_BASE,
        )
        models = llm_client.models.list()
        model = models.data[0].id

        lm = dspy.LM(
            f"openai/{model}",
            cache=False,
            api_key=globalAppSettings.OPENAI_API_KEY,
            api_base=globalAppSettings.OPENAI_API_BASE,
            seed=200,
        )
        dspy.configure(lm=lm)
    except Exception as e:
        log.warn("init llm fail")


class EvaluateSmartCockpitSignatureBase(dspy.Signature):
    conversation_history: str = dspy.InputField(
        prefix="上文问答历史", desc="之前的对话历史（最近几轮），用于保持上下文连贯性"
    )

    user_question: str = dspy.InputField(prefix="用户问题", desc="用户提问或指令或闲聊，表明了希望达成的目标")

    expected_behavior: str = dspy.InputField(
        prefix="预期结果", desc="预期的车机系统行为或响应。预期结果有可能等同于目标，也有可能是为了来达成目标的行为"
    )


class VoiceEvaluateSmartCockpitSignature(EvaluateSmartCockpitSignatureBase):
    """
    您是中国汽车技术研究中心的首席技术官，请作为一个公正的裁判，评估智能车机系统对用户提问或指令的回答质量。您应根据**裁决规则**，结合给定的**预期结果**评估系统是否通过有效且高效的互动提供了正确的回答或响应。
        请遵循以下认知步骤，系统地评估智能车机系统对用户提问或指令的回答质量。在进入每个新步骤之前，请提供详细的推理和解释。
        ## 认知操作步骤
        1. 目标明确: 清晰定义目标或预期的常识性结果
        2. 问题分解: 将问题分解为关键组成部分和相关因素
        3. 信息过滤: 分析文本和图片,专注于最重要的常识要素,忽略无关细节
        4. 重新组织: 重新安排事实和视角以澄清冲突或模糊之处
        5. 模式识别: 识别与其他常识场景或先例的相似之处
        6. 抽象提取: 提取可应用于此情况的更广泛常识原则
        7. 归纳推广: 将识别的原则应用于当前场景和潜在的未来案例
        8. 整合总结: 将所有视角和原则整合为最终的常识性决定,并由中文输出总结结果。
        9. 判断评分：根据输入的信息和输出的结果给出整个判断的置信度，置信度越高代表判断对错的准确度越高。

        ## 裁决规则
        1.明确裁决原则：
            1. 避免位置偏差：独立评估每个回答，确保回答顺序不影响判断。
            2. 情境适应性：判断系统是否能因应具体情境调整响应，尤其在复杂或多重指令下，确保具备合理应变能力。
        2. 分析用户意图：
           - 情感分析：当用户问题无明确指令，为情感表达时，结合情感语境给予积极回复即可倾向于车机已正确响应，车机前后截图无明显变化，为正常现象。
        3。逐步执行裁决细则：
           1. 目标状态和预期结果存在无法容错的矛盾，判定为不通过。
           2. 预期行为质量优先原则：注重回答的相关性和质量，不以回答长度为标准。一条有效回答应准确满足用户需求，而非单纯详尽。需求涉及具体明确程度（如调整次数、数值变化、“最大”/“最小”）时，则预期响应必须符合该标准。如果已经命中列举的预期行为，判定为通过，不必检查下面各项。但如果需求不涉及具体明确程度，响应未满足预期行为，符合以下条件，仍然可以判断为通过：
                - 重点关注目标状态是否达成，不一定要执行中间过程。
                   如何判断“预期行为已执行”且“无法再执行”
                  - 车机回答：“已经是”/“已经”/“当前是”/“目前处于”[目标状态]类似表述，说明目标状态已达到，直接判定为通过。
                - 如果当前目标状态并未达成，车机给出不能执行的理由是前置条件不满足，导致无法满足预期行为的，则判定为不通过
                - 系统回复“好的 ...”、“OK”、“收到”、“没问题”等类似应答，或表明正在处理中，应倾向于认为它已正确响应并在积极推动用户需求的实现，可视为合格响应。
                - 容错与纠错：确认系统是否准确回应提问或正确执行指令。如有轻微误差，考察系统是否能通过后续互动快速弥补，例如在初次误解后及时调整，则视为合格。系统的语音响应由 ASR（自动语音识别）生成，可能会出现同音字或其他细微偏差。分析时，应忽略这些偏差，并基于最佳解释原则进行推断，以准确评估系统的实际行为与预期行为的一致性。
           3. 功能支持性判定：若车机系统明确反馈不支持某项功能、未提供有效响应、语音无相关功能实现迹象，直接判定结果为不通过
           4. 如果当前状态不是预期结果，车机委婉的要求用户只能手动操作，不支持语音控制，判定结果为不通过
        4. 置信度评分：给出分析结果的置信度评分，
        5. 输出裁决逻辑的执行路径，在每个步骤后，请提供具体的解释，说明该步骤如何影响最终评分，并详细说明评分依据。


        <特殊知识>
            空调AC打开 代表车机开启空调
            切换自然风 代表空调切换外循环
            xx没有关着 代表xx已经打开
            
        </特殊知识>

        <example>
        问题：打开倒车模式
        车机反馈: 请您确认是否要打开倒车模式
        判断: Correct
        </example>
        <example>
        问题：打开洗车模式
        车机反馈: 请用户手动调节
        判断: 
        </example>
        <example>
        问题：打开XX模式
        车机反馈: 开启后会造成xx影响，告诉我开启还是取消吧
        判断: Correct
        </example>
        <example>
        问题：我冷了/我热了
        车机反馈: 开启后会造成xx影响，告诉我开启还是取消吧
        判断: Correct
        </example>
    """

    asr_response: str = dspy.InputField(
        prefix="车机回答", desc="语音识别的智能驾驶系统语音回复"
    )

    voice_analysis: str = dspy.OutputField(
        prefix="系统语音分析过程", desc="系统语音分析过程")

    voice_result: Literal["Correct", "Incorrect"] = dspy.OutputField(
        prefix="系统语音分析结果", desc="系统语音分析结果，由用户问题、预期结果、车机回答分析得出。"
    )

    voice_confidence: int = dspy.OutputField(
        prefix="系统语音分析置信度",
        desc="由用户问题、车机回答、预期结果、系统语音分析过程、系统语音分析结果给出置信度.范围在0-10分，0-3分表示裁决结果不可信，4-7分表示裁决结果不一定准确，8 分：高度可信，车机回答完全符合需求，9 分：极高可信度， 车机回答完美符合需求。10 分：完全可信，车机回答无可挑剔且提供额外价值"
    )


class ImageEvaluateSmartCockpitSignature(EvaluateSmartCockpitSignatureBase):
    """
    您是中国汽车技术研究中心的首席技术官，请作为一个公正的裁判，评估智能车机系统对用户提问或指令的回答质量。您应根据**裁决规则**，结合给定的**预期结果**评估系统是否通过有效且高效的互动提供了正确的回答或响应。
        请遵循以下认知步骤，系统地评估智能车机系统对用户提问或指令的回答质量。在进入每个新步骤之前，请提供详细的推理和解释。
        ## 认知操作步骤
        1. 目标明确: 清晰定义目标或预期的常识性结果
        2. 问题分解: 将问题分解为关键组成部分和相关因素
        3. 信息过滤: 分析文本和图片,专注于最重要的常识要素,忽略无关细节
        4. 重新组织: 重新安排事实和视角以澄清冲突或模糊之处
        5. 模式识别: 识别与其他常识场景或先例的相似之处
        6. 抽象提取: 提取可应用于此情况的更广泛常识原则
        7. 归纳推广: 将识别的原则应用于当前场景和潜在的未来案例
        8. 整合总结: 将所有视角和原则整合为最终的常识性决定,并由中文输出总结结果。
        9. 判断评分：根据输入的信息和输出的结果给出整个判断的置信度，置信度越高代表判断对错的准确度越高。

        ## 裁决规则
        1.明确裁决原则：
            1. 避免位置偏差：独立评估每个回答，确保回答顺序不影响判断。
            2. 情境适应性：判断系统是否能因应具体情境调整响应，尤其在复杂或多重指令下，确保具备合理应变能力。
            3. 图片分析准则：
                - **图标知识**: 通用空调图标内部有几条呈扇叶状或波浪状的线条，形状类似花朵，对称分布组成。
                - **准确性**: 系统能否准确描述图片中的内容及其变化。
                - **一致性**: 描述的内容是否与图片一致，无明显错误。
                - **详细程度**: 系统提供的描述是否足够详细，能够帮助理解图片的变化。
                - **相关性**: 描述的内容是否与用户的问题或指令直接相关。
        2. 分析用户意图：
           - 情感分析：当用户问题无明确指令，为情感表达时，结合情感语境给予积极回复即可倾向于车机已正确响应，车机前后截图无明显变化，为正常现象。
        3。逐步执行裁决细则：
           1. 目标状态和预期结果存在无法容错的矛盾，判定为不通过。
           2. 预期行为质量优先原则：注重回答的相关性和质量，不以回答长度为标准。一条有效回答应准确满足用户需求，而非单纯详尽。需求涉及具体明确程度（如调整次数、数值变化、“最大”/“最小”）时，则预期响应必须符合该标准。如果已经命中列举的预期行为，判定为通过，不必检查下面各项。但如果需求不涉及具体明确程度，响应未满足预期行为，符合以下条件，仍然可以判断为通过：
                - 容错与纠错：确认系统是否准确回应提问或正确执行指令。如有轻微误差，考察系统是否能通过后续互动快速弥补，例如在初次误解后及时调整，则视为合格。
           3. 功能支持性判定：若车机系统明确反馈不支持某项功能、未提供有效响应、界面或语音无相关功能实现迹象，直接判定结果为不通过
        4. 置信度评分：给出分析结果的置信度评分，
        5. 输出裁决逻辑的执行路径，在每个步骤后，请提供具体的解释，说明该步骤如何影响最终评分，并详细说明评分依据。


        <特殊知识>
            空调AC打开 代表车机开启空调
            切换自然风 代表空调切换外循环
            xx没有关着 代表xx已经打开
        </特殊知识>
    """
    pre_interaction_screenshot: Optional[dspy.Image] = dspy.InputField(
        prefix="指令前屏幕截图", desc="指令前屏幕截图"
    )

    post_interaction_screenshot: Optional[dspy.Image] = dspy.InputField(
        prefix="指令后屏幕截图", desc="指令前屏幕截图"
    )

    ui_analysis: str = dspy.OutputField(
        prefix="系统界面分析过程", desc="系统界面分析过程")

    ui_result: Literal["Correct", "Incorrect"] = dspy.OutputField(
        prefix="系统界面分析结果", desc="系统界面分析结果，由用户问题、指令前屏幕截图、指令后屏幕截图分析得出。"
    )

    ui_confidence: int = dspy.OutputField(
        prefix="系统界面分析置信度",
        desc="由用户问题、预期结果、指令前屏幕截图、指令后屏幕截图、系统界面分析过程、系统界面分析结果给出置信度，由用户问题、预期结果、指令前屏幕截图、指令后屏幕截图、系统界面分析过程、系统界面分析结果给出置信度。如果只需要指令后屏幕截图就能得出置信度，则请忽略指令前屏幕截图,范围在0-10分，0-3分表示裁决结果不可信，4-7分表示裁决结果不一定准确，8 分：高度可信，UI 变化完全符合预期，9 分：极高可信度， UI 变化完全匹配预期。10 分：完全可信，UI 变化符合最佳体验。"
    )


def extract_behaviors(text):
    # 使用正则 split 以 "###检查点" 作为分隔符
    sections = re.split(r"###检查点\s*\d+：", text)

    voice_behavior = []
    image_behavior = []

    for section in sections:
        section = section.strip()
        if section.startswith("语音回复"):
            voice_behavior.append(section.replace("语音回复", "").strip())
        elif section.startswith("屏幕显示"):
            image_behavior.append(section.replace("屏幕显示", "").strip())

    return "\n".join(voice_behavior), "\n".join(image_behavior)


class EvaluateSmartCockpit(dspy.Module):
    def __init__(self):
        super().__init__()

    def forward(
            self,
            user_question,
            expected_behavior,
            asr_response,
            pre_interaction_screenshot=None,
            post_interaction_screenshot=None,
            conversation_history="",
    ):
        start_time = time.time()
        time_delta = datetime.datetime.fromtimestamp(start_time)  # 转换为可读时间
        log.info(f"执行语音裁决开始: {time_delta}")
        no_image = False
        if asr_response == "获取失败":
            asr_response = ""
        if not expected_behavior:
            expected_behavior = "无"
            # 1. 先进行语音评估
        else:
            lines = expected_behavior.split("###")
            for line in lines:
                if "屏幕显示" in line and line.strip().endswith("无"):
                    no_image = True
                    if not asr_response:
                        pre_result = dspy.Prediction(judgment="Incorrect",
                                                     reasoning="没有语音结果并且不参考屏幕显示结果，判断为不通过",
                                                     voice_confidence=10, ui_confidence=10)
                        return pre_result
        voice_behavior, image_behavior = extract_behaviors(expected_behavior)
        print("voice_behavior:", voice_behavior)
        print("image_behavior:", image_behavior)
        voice_judge = dspy.ChainOfThought(VoiceEvaluateSmartCockpitSignature)
        voice_result = voice_judge(
            user_question=user_question,
            expected_behavior=voice_behavior,
            asr_response=asr_response,
            conversation_history=conversation_history,
        )
        end_time = time.time()
        response_time = end_time - start_time
        log.info(f"语音模型响应时间: {response_time:.3f} 秒")
        # 语音评估结果
        v_result = voice_result.voice_result
        v_analysis = voice_result.voice_analysis
        v_confidence = voice_result.voice_confidence
        final_result = v_result
        final_reasoning = v_analysis
        ui_confidence = None
        # 2. 语音置信度足够高，跳过界面分析
        if v_result == "Correct" and v_confidence >= 9:
            final_result = "Correct"
            final_reasoning = v_analysis
            ui_confidence = None
        else:
            if not pre_interaction_screenshot or not post_interaction_screenshot:
                no_image = True
            if not no_image:
                # 3. 进行界面评估
                ui_judge = dspy.ChainOfThought(ImageEvaluateSmartCockpitSignature)
                ui_result_obj = ui_judge(
                    user_question=user_question,
                    expected_behavior=image_behavior,
                    pre_interaction_screenshot=pre_interaction_screenshot,
                    post_interaction_screenshot=post_interaction_screenshot,
                    conversation_history=conversation_history,
                )

                # 界面评估结果
                u_result = ui_result_obj.ui_result
                u_analysis = ui_result_obj.ui_analysis
                ui_confidence = ui_result_obj.ui_confidence

                # 4. 结合语音和界面结果判断最终裁决
                if v_result == "Incorrect" and u_result == "Incorrect":
                    final_result = "Incorrect"
                else:
                    final_result = "Correct"
                final_reasoning = f"{v_analysis}\n{u_analysis}" if u_analysis else v_analysis

        # 计算响应时间
        end_time = time.time()
        response_time = end_time - start_time
        log.info(f"模型响应时间: {response_time:.3f} 秒")

        return dspy.Prediction(
            judgment=final_result,
            reasoning=final_reasoning,
            voice_confidence=v_confidence,
            ui_confidence=ui_confidence,
        )


class DialogueSessionEvalSignature(dspy.Signature):
    """评估整个多轮（至少1轮）对话会话是否通过.请注意:只有所有单轮对话都通过，才可以判断该多轮对话为通过。

        # 在依据 认知操作步骤 和 裁决规则 的基础上多考虑多轮对话的合理性，遵从下方综合分析以及注意事项并详细输出分析原因
        ##综合分析:

        指令理解和执行的准确率，包括是否准确理解用户的需求和正确执行操作。

        多轮对话的连贯性，检查系统是否能够持续保持对话的上下文，理解用户的连续请求。

        系统响应的一致性，确保在不同轮次中系统的响应逻辑一致，尤其是同一主题下的连续对话。

        整体交互体验，评估对话的自然流畅程度，以及系统与用户的互动是否令人满意。

        ##注意事项:

        多轮对话中的指令遵循：多轮对话中的指令遵循准确率是否随着轮次的增加保持稳定，是否存在显著下降或偏差。

        多轮对话中的指令保持：后续轮次中是否能继续遵循前一轮成功执行的指令或主题，确保对话的连续性和一致性。

        多轮对话中的自我纠正：如果之前的回答中存在错误，系统是否能够在后续对话中主动进行自我纠正。

        用户意图的正确追踪：在多轮对话中，系统是否能够持续准确地追踪用户意图，避免误解或偏离。

        动态调整：系统在面对用户需求变化时，是否能够灵活调整响应策略并继续保持对话流畅。
    """

    single_turn_evals: List[Dict[str, Any]] = dspy.InputField(desc="各轮对话的评估结果")

    session_result: Literal["通过", "失败"] = dspy.OutputField(desc="会话整体评估结果")
    session_reason: str = dspy.OutputField(desc="总体评价理由")
    command_success_rate: float = dspy.OutputField(
        desc="成功执行指令的比率：成功执行的轮次/总轮次"
    )


class CockpitAssistantEvaluator(dspy.Module):
    def __init__(self, evaluate_program):
        super().__init__()
        self.single_turn_eval = evaluate_program
        self.session_eval = dspy.ChainOfThought(DialogueSessionEvalSignature)

    def forward(self, result_list):
        single_turn_evals = []
        session_reasons = ""
        fail_nums = 0
        single_nums = len(result_list)
        history_array = []
        for i, test_result in enumerate(result_list):
            pre_interaction_screenshot, post_interaction_screenshot = None, None
            if os.path.exists(test_result["pre_interaction_screenshot"]):
                pre_interaction_screenshot = dspy.Image.from_file(test_result["pre_interaction_screenshot"])
                post_interaction_screenshot = dspy.Image.from_file(test_result["post_interaction_screenshot"])
            single_result = self.single_turn_eval.forward(
                user_question=test_result["user_question"],
                expected_behavior=test_result["expected_behavior"],
                asr_response=test_result["asr_response"],
                pre_interaction_screenshot=pre_interaction_screenshot,
                post_interaction_screenshot=post_interaction_screenshot,
                conversation_history=json.dumps(history_array, ensure_ascii=False)
            )

            # 转换结果
            eval_result = "通过" if single_result.judgment == "Correct" else "失败"

            # 累积失败统计
            if eval_result == "失败":
                fail_nums += 1

            session_reasons += single_result.reasoning
            if not pre_interaction_screenshot:
                ui_confidence = None
            else:
                ui_confidence = single_result.ui_confidence
            single_turn_evals.append(
                {
                    "turn_num": i,
                    "eval_result": eval_result,
                    "eval_reason": single_result.reasoning,
                    "eval_ui_confidence": ui_confidence,
                    "eval_voice_confidence": single_result.voice_confidence,
                }
            )
            history = {
                "第几轮": i + 1,
                "用户问题": test_result["user_question"],
                "预期结果": test_result["expected_behavior"],
                "车机回答": test_result["asr_response"],
                "模型判断结果": eval_result,
                "模型判断理由": single_result.reasoning,
            }
            history_array.append(history)
            upload_result(test_result["result_id"])
        # 会话整体评估
        command_success_rate = (single_nums - fail_nums) / single_nums

        # if session_results == "失败":
        #     session_eval_result = dspy.Prediction(
        #         session_result=session_results,
        #         session_reason=session_reasons,
        #         command_success_rate=command_success_rate,
        #     )
        # else:
        #     session_eval_result = self.session_eval(single_turn_evals=single_turn_evals)
        session_eval_result = "通过"
        if fail_nums != 0:
            session_eval_result = "不通过"
        # save_evaluation_results(result_list, single_turn_evals, session_eval_result)

        return single_turn_evals, session_eval_result


def save_evaluation_results(
        messages,
        single_turn_evals,
        session_eval_result,
        output_path=os.path.join(
            os.path.expanduser("~"), ".cache", "evaluation_results.jsonl"
        ),
):
    """保存评估结果到文件，以jsonl格式追加写入"""
    results = {
        "messages": messages,
        "single_turn_evaluations": single_turn_evals,
        "session_evaluation": {
            "result": session_eval_result.session_result,
            "reason": session_eval_result.session_reason,
            "success_rate": session_eval_result.command_success_rate,
        },
        "timestamp": datetime.now().isoformat(),
    }

    with open(output_path, "a", encoding="utf-8") as f:
        f.write(json.dumps(results, ensure_ascii=False) + "\n")


class ASRCorrectSignature(dspy.Signature):
    """
    你是一名车机交互领域的文本纠错专家，请按以下规则修正ASR转换文本中的同音/近音错误：
    【核心原则】
    1. 仅替换同音/近音字，禁止增删字符（即使存在其他错误）
    2. 修正后语义和字数必须与原句完全一致
    3. 无法修正时输出原句

    【优先级】
    1️.指令状态词（已/正在/请/将/未）
       ▶ 新增状态词替换表：
          {以→已, 情→请, 正再→正在, 卫→未, zai→在}
       ▶ 强制检测：当原句含有关闭/打开/设置等动词时，必须检测前导状态词
    2. 车机控制指令（空调/导航/音乐/座椅/除雾/除霜/雨刮/制冷/制热/近光/远光等）
       ▶ 示例：空条→空调 | 除双→除霜 | 雨刷→雨刮
    3️. 数字/单位/方位词
       ▶ 示例：二挡→二档 | 十风量→十档风量（需保持字数故不修正）
    4️. 保留口语化特征（嗯/啊/哦等）

    修正步骤：

    1. 【字数基准计算】
        - 统计原句纯文本字数（去除所有标点符号后的中文字符数量）
        - 示例：原句"温度，调高！" → 实际字数4（"温度调高"）

    2. 【错误检测优先级】
       (1) 指令状态词：已/正在/请/将/未（强制优先检测）
       (2) 车控核心名词：空调/除霜/雨刮等（按当前指令类型加权）
       (3) 数值单位：档/度/公里等
       (4) 方位词：左前/右后等

    3. 【替换可行性判断】
       - 建立同音字替换对照表：
         空调相关：{条:调, 冻:动, 双:霜...}
         导航相关：{撸:路, 像:向...}
       - 检查是否存在可替换且不改变字数的方案：
         ▶ 合格替换："三档"→"三档"（同字数）
         ▶ 无效替换："二挡"→"二档"（需保持原字数）

    4. 【安全替换执行】
       for 每个候选错误词:
         if 替换后字数相同 AND 符合当前指令场景优先级:
           执行替换并记录修改点
         else:
           保留原词
       完成所有可能替换后，重新计算纯文本字数

    5. 【最终校验】
       if 新字数 == 原基准字数 AND 至少修正一个有效错误：
         输出修正句
       else：
         维持原句
    """
    # user_question: str = dspy.InputField(prefix="指令", desc="用户指令")
    asr_text: str = dspy.InputField(prefix="原句", desc="通过ASR转换得到的文本")
    text: str = dspy.OutputField(prefix="修正", desc="纠正后的文本")


class GeneralizateSignature(dspy.Signature):
    """
    # 角色设定
    你是一位专业的车载语音系统语料工程师，擅长用多种自然语言表达方式描述同一意图。请根据用户输入的原始指令，生成10种语义相同但表述不同的中文指令变体。

    # 任务要求
    1. **多样性维度**：
       - 使用不同的动词（如"打开"→"开启"/"调低"）
       - 变换句式（陈述句/疑问句/省略句）
       - 添加礼貌词（请/麻烦/帮）
       - 改变语序结构
       - 包含口语化表达（如"能不能"→"可不可以"）
       - 句式结构（至少包含以下类型）：
        ▢ 基础指令式（"打开车窗"）
        ▢ 条件触发式（"如果太热，请开窗"）
        ▢ 参数调节式（"车窗开一半"）
        ▢ 场景适配式（"下雨了，关上车窗"）
        ▢ 状态查询式（"车窗现在是开着的吗？"）
        ▢ 渐进控制式（"把车窗再开大一点"）
       - 参数维度（需差异化组合）：
        ▢ 开度控制（全开/半开/三分之一等）
        ▢ 目标车窗（左前/右后/全部等）
        ▢ 环境因素（温度/空气质量/天气等）
        ▢ 时间因素（立即/稍后/持续等）
       - 表达方式（需交替使用）：
        ▢ 直接命令（"打开车窗"）
        ▢ 委婉请求（"请打开车窗"）
        ▢ 智能建议（"检测到空气质量良好，建议开窗"）
        ▢ 状态触发（"车内温度过高，正在打开车窗"）

    2. **生成规则**：
       - 保持核心意图：控制车窗开合状态
       - 所有变体必须符合中文表达习惯
       - 避免使用专业术语（如"启动车窗电机"）
       - 保证生成的指令都不一样
       - 严格避免重复用词组合
       - 句式长度差异需≥30%

    3. **避免事项**：
       - 不改变原始指令的设备对象（保持"车窗"）
       - 不添加额外功能（如调节开合幅度）
       - 不使用方言词汇
    """
    input: str = dspy.InputField(prefix="输入", desc="输入,需要泛化的语料")
    output: str = dspy.OutputField(prefix="输出", desc="输出,根据要求返回的列表")


init_llm()
