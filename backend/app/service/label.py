from app.utils.utils import generate_random_string
from app.dao.label_dao import LabelQueryDao, LabelOperateDao
from app.dao.models.sqlite_gen import TestLabelGroup, TestLabel


def create_labelgroup(label_group_name, label_group_type):
    random_str = generate_random_string()
    label_group = TestLabelGroup(
        label_group_id=random_str,
        label_group_name=label_group_name,
        label_group_type=label_group_type,
    )
    save_label_group = LabelOperateDao.saveTestLabelGroup(label_group)
    return 0


def delete_labelgroup(label_group_id):
    LabelOperateDao.deleteTestLabelGroup(label_group_id)
    return 0


def update_labelgroup(label_group_id, label_group_name, label_group_type):
    updated_data = {
        "label_group_name": label_group_name,
        "label_group_type": label_group_type,
    }
    update_group = LabelOperateDao.updateTestLabelGroup(label_group_id, updated_data)
    return 0


def get_label_groups(data):
    group_list = LabelQueryDao.showAllTestLabelGroup(data)
    res = []
    num = 0
    for group in group_list:
        temp = {}
        temp["label_group_id"] = group.label_group_id
        temp["label_group_name"] = group.label_group_name
        temp["label_group_type"] = group.label_group_type

        res.append(temp)
        num += 1
    return {"data": res, "total": num}


def get_test_labels(data):
    label_list = LabelQueryDao.showAllTestLabel(data)
    res = []
    num = 0
    for label in label_list:
        temp = {}
        temp["label_id"] = label.label_id
        temp["label_name"] = label.label_name
        temp["label_group_id"] = label.label_group_id
        temp["color"] = label.color
        temp["range"] = label.range

        res.append(temp)
        num += 1
    return {"data": res, "total": num}


def create_label_info(label_name, label_group_id, color, range):
    random_str = generate_random_string()
    label = TestLabel(
        label_id=random_str,
        label_name=label_name,
        label_group_id=label_group_id,
        color=color,
        range=range,
    )
    save_label = LabelOperateDao.saveTestLabel(label)
    return 0


def delete_label_info(label_id):
    LabelOperateDao.deleteTestLabel(label_id)
    return 0


def update_label_info(label_id, label_name, label_group_id, color, range):
    updated_data = {
        "label_name": label_name,
        "label_group_id": label_group_id,
        "color": color,
        "range": range,
    }
    update_label = LabelOperateDao.updateTestLabel(label_id, updated_data)
    return 0
