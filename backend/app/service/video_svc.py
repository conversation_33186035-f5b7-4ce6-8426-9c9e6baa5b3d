import signal
import subprocess
import threading
import sounddevice as sd

from app.middleware.log import get_request_id, logger as log
from app.config.device_config import deviceConfig, is_windows_os

VIDEO_NAME = "video"
CABIN_VIDEO_NAME = "cabin_video"


def get_device_name(video_name, video_index):
    from PyCameraList.camera_device import list_video_devices, list_audio_devices
    same_name_video_map = {}
    lists = list_video_devices()
    count = 0
    video_device_number = 0
    for v in lists:
        if video_name == v[1]:
            same_name_video_map[v[0]] = count
            count += 1

    if len(same_name_video_map) != 1:
        video_device_number = same_name_video_map[video_index]
    audio_list = list_audio_devices()
    audio_name = ""
    count = 0
    for audio in audio_list:
        if video_name in audio[1]:
            if count == video_device_number:
                audio_name = audio[1]
                break
            else:
                count += 1
    return video_device_number, audio_name


class Video:
    def __init__(self, name):
        self.samplerate = 44100
        self.request_id = ""
        self.name = name
        self._process = None
        self.video_index = -1
        self.width = 1920
        self.height = 1080
        self.frame_rate = 30.0
        self._lock = threading.Lock()
        self.init()

    def init(self):
        if self.name == VIDEO_NAME:
            self.frame_rate, self.width, self.height, self.video_index = deviceConfig.get_video_config()
        else:
            self.frame_rate, self.width, self.height, self.video_index = deviceConfig.get_cabin_video_config()

    def start(self, video_path):
        if not is_windows_os():
            log.warn("mac device do not record.")
            return
        self.request_id = get_request_id()
        if self.name == VIDEO_NAME:
            frame_rate, width, height, v_index = deviceConfig.get_video_config()
        else:
            frame_rate, width, height, v_index = deviceConfig.get_cabin_video_config()
        if v_index < 0:
            return

        if "." not in video_path:
            video_path = video_path + ".mp4"

        if self.width != width or self.height != height or self.frame_rate != frame_rate or self.video_index != v_index:
            self.init()
        if self.name == VIDEO_NAME:
            video_name = deviceConfig.get_video_name()
        else:
            video_name = deviceConfig.get_cabin_video_name()

        video_device_number, audio_name = get_device_name(video_name, v_index)
        # sd._terminate()
        # sd._initialize()
        # devices = sd.query_devices()
        # audio_name = ""
        # for i, device in enumerate(devices):
        #     if device['max_input_channels'] > 0 and video_name in device['name']:
        #         audio_name = device['name']
        #         break
        if not audio_name:
            log.warn(f"{self.name} is not opened. {video_name=}")
            return

        with self._lock:
            try:
                command = [
                    'ffmpeg',
                    '-f', 'dshow',
                    '-video_device_number', str(video_device_number),
                    '-video_size', f'{self.width}x{self.height}',
                    '-framerate', str(self.frame_rate),
                    '-rtbufsize', '100M',
                    '-i', f'video={video_name}',
                    '-sample_rate', str(self.samplerate),
                    '-channels', '1',
                    '-f', 'dshow',
                    '-i', f'audio={audio_name}',
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    '-strict', 'experimental',
                    '-async', '1',
                    '-loglevel', 'error',
                    '-y',  # 覆盖已存在文件
                    video_path
                ]
                log.info(f"{self.name} record_av start. {video_name=} {audio_name=} {video_path=}")
                self._process = subprocess.Popen(
                    command,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                )
            except Exception as e:
                log.error(str(e))

    def close(self):
        with self._lock:
            if self._process:
                self._process.send_signal(signal.CTRL_BREAK_EVENT)
                self._process.wait()
                self._process = None
                log.info(f"{self.name} close.")
