import socket
import threading
import queue

from app.dao.plan_dao import Plan<PERSON>ueryDao
from app.dao.project_dao import Project<PERSON>ueryDao
from app.dao.result_dao import ResultQueryDao, ResultOperateDao
from app.dao.corpus_dao import Corpus<PERSON>ueryDao
from app.dao.play_config_dao import PlayConfigQueryDao
from app.dao.models.sqlite_gen import MultiResult
from app.service.speech_recognition_svc import SpeechRecognitionSvc
from app.service.dspy_svc import *
from app.service.minio_svc import *
from app.constant.constant import *
from app.utils.utils import get_response_time, encode_signs
from app.service.data_upload import upload_result
from concurrent.futures import ThreadPoolExecutor
from app.config.device_config import deviceConfig

LLMJudge_Queue = queue.Queue()
LLMJudge_Queue_lock = threading.Lock()


def get_asr_result(result, play_config_type="interaction", asr_correct=False):
    asr_svc = SpeechRecognitionSvc()
    full_path = result.mic_audio_url
    wav_file = full_path[:-9] + ".wav"
    if "rouse" == play_config_type:
        mic_audio_text = asr_svc.local_recognize_rouse(wav_file)
    else:
        mic_audio_text = asr_svc.local_recognize(wav_file, result.text)
    # 如果开启模型纠正则执行
    if asr_correct and mic_audio_text:
        asr_text = mic_audio_text
        try:
            asr_jc_llm = dspy.Predict(ASRCorrectSignature)
            result = asr_jc_llm(asr_text=asr_text, user_question=result.text)
        except Exception as e:
            log.error("asr_correct fail, connect llm fail.")
            return mic_audio_text
        mic_audio_text = result.text
        log.info(f"correct asr_text:{asr_text}, result_text:{mic_audio_text}, audio_path:{wav_file}")
    return mic_audio_text


def get_time(result, play_config_type="interaction"):
    r_time = -1
    if os.path.exists(result.mic_audio_url):
        r_time2 = get_response_time(result.mic_audio_url, play_config_type)
        log.info(f"mic_audio_url: {result.mic_audio_url}, 通过算法算出的时间为 {r_time2}")
        if r_time2 is not None and r_time2 > 0:
            r_time = round(r_time2 * 1000)
    else:
        log.warn(f"--- {result.mic_audio_url=} is not exists ---")
    return r_time


def is_llm_service_open(host, port, timeout=5):
    """检测指定主机和端口是否可连接"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except socket.error:
        return False


def is_rouse_test_result(test_result):
    if isinstance(test_result, list):
        return False
    plan = PlanQueryDao.findProjectPlanById(test_result.plan_id)
    if plan.type == PlayConfigType.rouse:
        return True
    return False


def llm_judge(test_result):
    if not is_rouse_test_result(test_result) and not is_llm_service_open(LLM_HOST, LLM_PORT):
        log.warn("connect llm fail.")
        init_llm()
        raise
    evaluate_program = EvaluateSmartCockpit()
    try:
        if isinstance(test_result, list):
            config = None
            input_data = []
            update_data = {}
            for get_res in test_result[:-1]:
                set_request_id(get_res.request_id)
                if config is None:
                    plan = PlanQueryDao.findProjectPlanById(get_res.plan_id)
                    config_data = {"play_config_id": plan.play_config_id}
                    starconfig = PlayConfigQueryDao.showAllStartConfig(config_data)
                    config = starconfig[0]
                sign_list = get_res.signlist
                asr_text = get_asr_result(get_res, asr_correct=config.asr_correct)
                if asr_text is None or asr_text == "":
                    log.warn("\n--- sign :asr输出为空 ---")
                    sign_list = encode_signs(sign_list, "asr_empty")
                    asr_text = "获取失败"
                update_data = {"asr_result": asr_text, "signlist": sign_list}
                ResultOperateDao.updateTestResult(get_res.result_id, update_data)
                # if config.response_time:
                #     r_time = get_time(get_res)
                #     response_time = get_res.response_time
                #     if r_time != -1:
                #         response_time = r_time
                # else:
                #     response_time = -2.0
                # update_data = {"response_time": response_time}
                # ResultOperateDao.updateTestResult(get_res.result_id, update_data)
                temp = {
                    "result_id": get_res.result_id,
                    "user_question": get_res.text,
                    "expected_behavior": get_res.expect_result,
                    "asr_response": asr_text,
                    "pre_interaction_screenshot": get_res.image[:-10] + "1.jpg",
                    "post_interaction_screenshot": get_res.image,
                    "project_id": get_res.project_id,
                    "plan_id": get_res.plan_id,
                    "turn_id": get_res.turn_id,
                    "circle": get_res.circle,
                    "corpus_id": get_res.corpus_id,
                    "signlist": sign_list,
                }
                input_data.append(temp)
            if isinstance(test_result[-1], MultiResult) and config and config.interaction_success_rate:
                log.info("--- 发送数据给模型判断 ---")
                if evaluate_program is None:
                    log.warn(f" 当前无网络，请链接网络后请求模型结果 ")
                status = multi_llm_judge_result(
                    input_data, test_result[-1], evaluate_program
                )
                if status != "":
                    for multi_result in input_data:
                        update_data["result"] = "分析出错"
                        ResultOperateDao.updateTestResult(multi_result["result_id"], update_data)
                    if status == "Connect Error":
                        log.warn(f" 当前无网络，请链接网络后请求模型结果 ")
                    elif status == "Connect MiniIO Server Error":
                        log.warn(f" 链接 MiniIO Server 失败 ")
            else:
                log.info(f"--- 此次多轮测试语料未进行模型处理，交互成功率设置为False --- ")
                for multi_result in input_data:
                    update_data["result"] = "未测试"
                    ResultOperateDao.updateTestResult(multi_result["result_id"], update_data)
                    upload_result(multi_result["result_id"])
            # LLMJudge_Queue.task_done()
            # continue
        else:
            set_request_id(test_result.request_id)
            corpus = CorpusQueryDao.findTestCorpusById(test_result.corpus_id)
            plan = PlanQueryDao.findProjectPlanById(test_result.plan_id)
            config_data = {"play_config_id": plan.play_config_id}
            starconfig = PlayConfigQueryDao.showAllStartConfig(config_data)
            config = starconfig[0]
            if plan.type == PlayConfigType.rouse:  # 没找到测试语料则判断为唤醒场景
                log.info(
                    f"--- 此次为唤醒语料，未进行模型处理，只通过asr结果判断 --- "
                )
                sign_list = test_result.signlist
                if config.wakeup_time:
                    r_time = get_time(test_result, "rouse")
                    response_time = test_result.response_time if test_result.response_time is not None else -1
                    if r_time != -1:
                        response_time = r_time
                    if response_time > 2000 or response_time < 100:
                        log.warn("\n--- sign :唤醒时间异常 ---")
                        sign_list = encode_signs(sign_list, "rouse_time_abnormal")
                else:
                    response_time = -2.0
                update_data = {"response_time": response_time, "signlist": sign_list}
                ResultOperateDao.updateTestResult(
                    test_result.result_id, update_data
                )
                if config.wakeup_success_rate:
                    asr_text = get_asr_result(test_result, "rouse", asr_correct=config.asr_correct)
                    if asr_text is None or asr_text == "":
                        log.warn("\n--- sign :唤醒应答异常 ---")
                        sign_list = encode_signs(sign_list, "rouse_asr_abnormal")
                        update_data = {
                            "result": "不通过",
                            "score": 0,
                            "asr_result": "获取失败",
                            "signlist": sign_list,
                        }
                    else:
                        update_data = {
                            "result": "通过",
                            "score": 10,
                            "asr_result": asr_text,
                        }
                    ResultOperateDao.updateTestResult(
                        test_result.result_id, update_data
                    )
                else:
                    update_data = {
                        "asr_result": "未测试",
                        "signlist": sign_list,
                    }
                    ResultOperateDao.updateTestResult(
                        test_result.result_id, update_data
                    )
                    log.info(f"--- 此次唤醒语料未进行判断，唤醒成功率设置为False --- ")
                # continue
            else:
                sign_list = test_result.signlist
                if config.response_time:
                    r_time = get_time(test_result)
                    response_time = test_result.response_time if test_result.response_time is not None else -1
                    if r_time != -1:
                        response_time = r_time
                        log.warn("--- 使用算法得出的响应时间 ---")
                    else:
                        log.warn("--- 算法得出的响应时间有误，使用原本的响应时间 ---")
                    testcorpus_config = PlayConfigQueryDao.showAllTestCorpusConfig(config_data)
                    t_corpus = testcorpus_config[0]
                    if response_time <= 0 or response_time >= t_corpus.wait_time * 1000:
                        log.warn("\n--- sign :响应时间异常 ---")
                        sign_list = encode_signs(sign_list, "response_time_abnormal")
                else:
                    response_time = -2.0
                update_data = {"response_time": response_time, "signlist": sign_list}
                ResultOperateDao.updateTestResult(
                    test_result.result_id, update_data
                )
                asr_text = get_asr_result(test_result, asr_correct=config.asr_correct)
                if asr_text is None or asr_text == "":
                    log.warn("\n--- sign :asr输出为空 ---")
                    sign_list = encode_signs(sign_list, "asr_empty")
                    asr_text = "获取失败"
                update_data = {"asr_result": asr_text, "signlist": sign_list}
                ResultOperateDao.updateTestResult(
                    test_result.result_id, update_data
                )

                if config.interaction_success_rate:  # 是否为交互成功率测试
                    log.info("\n--- 开始单轮交互语料模型判断 ---")
                    show_project = ProjectQueryDao.findTestProjectById(
                        test_result.project_id
                    )
                    show_plan = PlanQueryDao.findProjectPlanById(
                        test_result.plan_id
                    )
                    if show_plan and show_project:
                        post_image = test_result.image
                        pre_image = post_image[:-10] + "1.jpg"
                        log.info(
                            f" 项目名={show_project.project_name} # 方案名={show_plan.plan_name} # 轮次={test_result.turn_id} # 语料文本/用户指令={test_result.text} TEXT send to llm : 车机回复={asr_text}, 期望结果={corpus.expect_result}, pre_image={pre_image}, post_image={post_image}"
                        )
                        update_data = {}
                        if evaluate_program is None:
                            log.warn(f"当前无网络，请链接网络后请求模型结果")
                            update_data["result"] = "分析出错"
                            update_data["score"] = 0
                            update_data["reason"] = "模型调用失败，无法访问模型服务，网络异常，请重新链接网络后请求模型结果"
                        elif (not asr_text or asr_text == "获取失败") and (
                                not os.path.exists(post_image) or not os.path.exists(pre_image)):
                            log.warn(f"模型判断没有asr结果和图片结果，直接返回不通过.")
                            update_data["result"] = "不通过"
                            update_data["score"] = 0
                            update_data["reason"] = "没有相关的文本结果和图片结果，本次交互评判为不通过"
                        else:
                            pre_image_data = ""
                            post_image_data = ""
                            if os.path.exists(pre_image):
                                pre_image_data = dspy.Image.from_file(pre_image)
                            if os.path.exists(post_image):
                                post_image_data = dspy.Image.from_file(post_image)
                            judgment = evaluate_program.forward(
                                user_question=test_result.text,
                                expected_behavior=corpus.expect_result,
                                asr_response=asr_text,
                                pre_interaction_screenshot=pre_image_data,
                                post_interaction_screenshot=post_image_data,
                            )
                            log.info(
                                f" 项目名={show_project.project_name} # 方案名={show_plan.plan_name} # 轮次={test_result.turn_id} # 语料文本={test_result.text} TEXT llm result judge : {judgment}"
                            )
                            res = judgment["judgment"]
                            update_data["reason"] = judgment["reasoning"]
                            update_data["llm_voice_confidence"] = judgment["voice_confidence"]
                            update_data["llm_ui_confidence"] = judgment["ui_confidence"]
                            if update_data["llm_voice_confidence"] < 8 and (
                                    update_data["llm_ui_confidence"] and update_data["llm_ui_confidence"] < 8):
                                log.warn("\n--- sign :重点关注 ---")
                                sign_list = encode_signs(sign_list, "result_need_attention")
                            elif update_data["llm_voice_confidence"] < 8 or (
                                    update_data["llm_ui_confidence"] and update_data["llm_ui_confidence"] < 8):
                                log.warn("\n--- sign :需复核结果 置信度小于8---")
                                sign_list = encode_signs(sign_list, "result_need_reviewed")
                            elif update_data["llm_voice_confidence"] >= 8:
                                # corpus.corpus_id
                                pre_turn = test_result.turn_id - 1
                                find_data = {"project_id": test_result.project_id, "corpus_id": corpus.corpus_id,
                                             "turn_id": pre_turn}
                                r_list, _ = ResultQueryDao.showAllTestResult(find_data)
                                if len(r_list) == 0:
                                    pass
                                else:
                                    r = r_list[0]
                                    if (r.result == "通过" and res == "Correct") or (
                                            r.result == "不通过" and res == "Incorrect") \
                                            or (r.result == "不确定" and "Correct" not in res):
                                        pass
                                    else:
                                        log.warn(
                                            f"\n--- sign :需复核结果 当前结论与前一条测试结果不相符 --- pre:{r.result}, recent:{res}")
                                        sign_list = encode_signs(sign_list, "result_need_reviewed")
                            update_data["signlist"] = sign_list
                            if res == "Correct":
                                update_data["result"] = "通过"
                                update_data["score"] = 10
                            elif res == "Incorrect":
                                update_data["result"] = "不通过"
                                update_data["score"] = 0
                            else:
                                update_data["result"] = "不确定"
                                update_data["score"] = 5
                        ResultOperateDao.updateTestResult(test_result.result_id, update_data)
                else:
                    log.info(f"--- 此次测试语料未进行模型处理，交互成功率设置为False --- ")
            upload_result(test_result.result_id)
    except Exception as e:
        log.warn("error: ", e)
        if "No LM" in str(e):
            init_llm()
        raise
    return


def llm_judge_callback(future):
    try:
        future.result()  # 获取任务结果（如果任务失败会抛出异常）
    except Exception as e:
        # log.warn(f"llm_judge failed for result_id:{future.test_result.result_id}")
        # 发生异常时，将任务重新加入队列
        LLMJudge_Queue.put(future.test_result)
    finally:
        LLMJudge_Queue.task_done()  # 标记任务已完成


def llm_judge_result():
    with ThreadPoolExecutor(max_workers=deviceConfig.device_map["result_judge_thread"]) as executor:
        while True:
            try:
                try:
                    test_result = LLMJudge_Queue.get_nowait()  # 从队列中取出数据
                except queue.Empty:
                    time.sleep(1)
                    continue
                future = executor.submit(llm_judge, test_result)
                future.test_result = test_result
                future.add_done_callback(llm_judge_callback)
                # LLMJudge_Queue.task_done()  # 标记任务已完成
            except Exception as e:
                log.warn(f"error: {e}")
            finally:
                time.sleep(1)


def print_evaluation_results(
        result_list, multi_result, single_turn_evals, session_eval_result
):
    """格式化打印评估结果"""
    print("\n=== 智慧座舱大模型指令遵循能力评估 ===")

    print("\n--- 各轮对话评估 ---")
    num = 0
    for eval_result in single_turn_evals:
        res = eval_result["eval_result"]
        update_data = {
            "llm_ui_confidence": eval_result["eval_ui_confidence"],
            "llm_voice_confidence": eval_result["eval_voice_confidence"],
            "reason": eval_result["eval_reason"]
        }
        sign_list = result_list[num]["signlist"]
        if update_data["llm_voice_confidence"] < 8 and (
                update_data["llm_ui_confidence"] and update_data["llm_ui_confidence"] < 8):
            log.warn("\n--- sign :重点关注 ---")
            sign_list = encode_signs(sign_list, "result_need_attention")
        elif update_data["llm_voice_confidence"] < 8 or (
                update_data["llm_ui_confidence"] and update_data["llm_ui_confidence"] < 8):
            log.warn("\n--- sign :需复核结果 置信度小于8---")
            sign_list = encode_signs(sign_list, "result_need_reviewed")

        update_data["signlist"] = sign_list
        if res == "通过":
            update_data["result"] = "通过"
            update_data["score"] = 10
        elif res == "失败":
            update_data["result"] = "不通过"
            update_data["score"] = 0
        else:
            update_data["result"] = "不确定"
            update_data["score"] = 5
        ResultOperateDao.updateTestResult(result_list[num]["result_id"], update_data)
        upload_result(result_list[num]["result_id"])
        num += 1
        log.info(
            f"\n第 {eval_result['turn_num']} 轮: 结果: {res} 理由: {eval_result['eval_reason']} "
            f"系统置信度：{eval_result['eval_ui_confidence']} 语音置信度：{eval_result['eval_voice_confidence']}"
        )

    log.info("\n--- 整体会话评估 ---")
    # print(f"最终结果: {session_eval_result.session_result}")
    # print(f"评估理由: {session_eval_result.session_reason}")
    # print(f"指令执行成功率: {session_eval_result.command_success_rate:.2%}")
    update_multi_res = {
        "result": session_eval_result,
    }
    ResultOperateDao.updateMultiResult(multi_result.id, update_multi_res)
    log.info(f"最终结果: {session_eval_result}")


def multi_llm_judge_result(result_list, multi_result, evaluate_program):
    """
    输入为结果列表
    [{},{}]
    """
    evaluator = CockpitAssistantEvaluator(evaluate_program)
    # 执行评估
    single_turn_evals, session_eval_result = evaluator.forward(result_list)

    # 输出结果
    print_evaluation_results(
        result_list, multi_result, single_turn_evals, session_eval_result
    )
    return ""
