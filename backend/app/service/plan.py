import math
import re

from app.service.video_svc import *
from app.utils.utils import *
from app.utils.thread_manager import thread_manager
from app.dao.plan_dao import PlanQueryDao, PlanOperateDao
from app.dao.corpus_dao import CorpusQueryDao
from app.dao.result_dao import ResultQueryDao, ResultOperateDao
from app.dao.project_dao import ProjectQueryDao, ProjectOperateDao
from app.dao.play_config_dao import PlayConfigQueryDao
from app.dao.models.sqlite_gen import (
    ProjectPlan,
    TCorpusTree,
    RCorpusTree,
    DCorpusTree,
    BNoiseTree,
    TestResult,
    MultiResult,
    FalseRouseResult,
    ExecuteQueue,
)
from app.dao.review_dao import ReviewResultDao, ReviewResult
from app.service.iamge_svc import QUBEImageSvc
from app.service.corpus import get_multi_testcorpus
from app.service.play_config import play_config_list, get_config_by_play_id
from app.middleware.log import set_request_id, get_request_id, logger as log
from app.utils.report import (
    Report,
    TestResultData,
    RouseTestData,
    FalseRouseTestData,
    SingleTestData,
    MultiTestData,
    get_word_report,
)
from app.service.play_config import play_config_delete
from app.dao.base_dao import *
from app.service.data_upload import DataUpload_Queue
from app.service.llm_judge import LLMJudge_Queue
from app.config.device_config import deviceConfig
from app.config.app_config import get_app_run, set_app_run
from app.constant.constant import *

stop_event = threading.Event()


def create_test_plan(data, retest_plan_id="", turn_id=None):
    plan_id = find_table_id("project_plan_id")
    if retest_plan_id == "":
        plan_id_string = "plan_" + str(plan_id)
    else:
        plan_id_string = "retest_" + retest_plan_id + "_" + plan_id
    plan = ProjectPlan(
        plan_id=plan_id_string,
        project_id=data["project_id"],
        plan_name=data["plan_name"],
        turn_id=turn_id
    )
    save_plan = PlanOperateDao.saveProjectPlan(plan)
    return save_plan


def update_test_plan(data):
    if "plan_id" not in data or data["plan_id"] == "plan-1":
        return {}
    updated_data = {"plan_name": data["plan_name"]}

    update_plan = PlanOperateDao.updateProjectPlan(data["plan_id"], updated_data)
    return update_plan


def delete_test_plan(data, delete_config=False):
    if "plan_id" not in data or data["plan_id"] == "plan-1":
        return {}
    plan = PlanQueryDao.findProjectPlanById(data["plan_id"])
    check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
    if check_status.project_status == "progressing":
        return {"status": "error", "error_msg": "This project is in use"}
    PlanOperateDao.deleteTCorpusTree(data["plan_id"])
    PlanOperateDao.deleteRCorpusTree(data["plan_id"])
    PlanOperateDao.deleteDCorpusTree(data["plan_id"])
    PlanOperateDao.deleteBNoiseTree(data["plan_id"])
    PlanOperateDao.deleteProjectPlan(data["plan_id"])
    if delete_config:
        play_config_delete(plan.play_config_id, force=True)
    return {"status": "success", "error_msg": ""}


def fake_delete_test_plan(data):
    if "plan_id" not in data or data["plan_id"] == "plan-1":
        return {}
    plan = PlanQueryDao.findProjectPlanById(data["plan_id"])
    check_status = ProjectQueryDao.findTestProjectById(plan.project_id)
    if check_status.project_status == "progressing":
        return "项目正在使用中"
    PlanOperateDao.deleteTCorpusTree(data["plan_id"])
    PlanOperateDao.deleteRCorpusTree(data["plan_id"])
    PlanOperateDao.deleteDCorpusTree(data["plan_id"])
    PlanOperateDao.deleteBNoiseTree(data["plan_id"])
    # play_config_delete(plan.play_config_id)
    updated_data = {"is_delete": True}
    update_plan = PlanOperateDao.updateProjectPlan(data["plan_id"], updated_data)
    return 0


def test_plan_list(data):
    plan_list, total = PlanQueryDao.showAllProjectPlan(data)
    res = []
    for plan in plan_list:
        temp = {}
        temp["plan_id"] = plan.plan_id
        temp["plan_name"] = plan.plan_name
        temp["play_config_id"] = plan.play_config_id
        temp["type"] = plan.type
        temp["is_delete"] = plan.is_delete
        if "retest_" in plan.plan_id and plan.plan_status == "completed":
            temp["is_completed"] = True
        else:
            temp["is_completed"] = False
        if plan.turn_id is None:
            pass
        elif "turn_id" in data and plan.turn_id == data["turn_id"]:
            pass
        elif "turn_id" in data and plan.turn_id == str(data["turn_id"]):
            pass
        else:
            continue
        res.append(temp)
    return res, total


def get_corpuslist_byplanid(plan_id):
    if plan_id == "plan-1":
        return {}
    plan = PlanQueryDao.findProjectPlanById(plan_id)
    if plan is None:
        return {}
    temp = {}
    temp["plan_id"] = plan.plan_id
    temp["play_config_id"] = plan.play_config_id
    a_list = PlanQueryDao.findAllTCorpusByTreeId(plan.plan_id)
    a_ret = []
    for a in a_list:
        a_ret.append(a)
    temp["testCorpusList"] = a_ret

    b_list = PlanQueryDao.findAllRCorpusByTreeId(plan.plan_id)
    b_ret = []
    for b in b_list:
        b_ret.append(b.corpus_id)
    temp["rouseCorpusList"] = b_ret

    c_list = PlanQueryDao.findAllDCorpusByTreeId(plan.plan_id)
    c_ret = []
    for c in c_list:
        c_ret.append(c.corpus_id)
    temp["disturbCorpusList"] = c_ret

    d_list = PlanQueryDao.findAllNoiseByTreeId(plan.plan_id)
    d_ret = []
    for d in d_list:
        d_ret.append(d.corpus_id)
    temp["backgroundNoiseList"] = d_ret

    return temp


def save_corpuslist_byplanid(data):
    if "plan_id" not in data or data["plan_id"] == "plan-1":
        return {}
    if "testCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteTCorpusTree(data["plan_id"])
        for corpus_id in data["testCorpusList"]:
            if isinstance(corpus_id, str):
                corpus = TCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            else:
                corpus = TCorpusTree(
                    plan_id=data["plan_id"], corpus_id=corpus_id["corpus"], result_id=corpus_id["result"]
                )
            corpus_list.append(corpus)
        PlanOperateDao.saveTCorpusTreeList(corpus_list)
    if "rouseCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteRCorpusTree(data["plan_id"])
        for corpus_id in data["rouseCorpusList"]:
            if isinstance(corpus_id, str):
                corpus = RCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            else:
                corpus = RCorpusTree(
                    plan_id=data["plan_id"], corpus_id=corpus_id["corpus"], result_id=corpus_id["result"]
                )
            corpus_list.append(corpus)
        PlanOperateDao.saveRCorpusTreeList(corpus_list)
    if "disturbCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteDCorpusTree(data["plan_id"])
        for corpus_id in data["disturbCorpusList"]:
            corpus = DCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            corpus_list.append(corpus)
        PlanOperateDao.saveDCorpusTreeList(corpus_list)
    if "backgroundNoiseList" in data:
        corpus_list = []
        PlanOperateDao.deleteBNoiseTree(data["plan_id"])
        for corpus_id in data["backgroundNoiseList"]:
            corpus = BNoiseTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            corpus_list.append(corpus)
        PlanOperateDao.saveBNoiseTreeList(corpus_list)
    play_config = PlayConfigQueryDao.findPlayConfigById(data["play_config_id"])
    updated_data = {"play_config_id": data["play_config_id"], "type": play_config.type}
    update_plan = PlanOperateDao.updateProjectPlan(data["plan_id"], updated_data)
    return update_plan


def play_rousecorpus(corpus_id, wait, channel="channel1", gain=0):
    r_corpus = CorpusQueryDao.findRouseCorpusById(corpus_id)
    aud = CorpusQueryDao.findAudioById(r_corpus.aud_id)
    output_device_id = deviceConfig.get_test_corpus_device_id()
    volume = deviceConfig.get_test_corpus_device_volume()
    play_audio_with_sounddevice(get_request_id(), aud.aud_url, device_id=output_device_id, volume=volume)
    time.sleep(wait / 1000.0)


def play_disturbcorpus(
        project_id, corpus_id, plan_id, turn_id, start_time, channel="channel1", gain=0, video_path=""
):
    show_project = ProjectQueryDao.findTestProjectById(project_id)

    d_corpus = CorpusQueryDao.findDisturbCorpusById(corpus_id)
    aud = CorpusQueryDao.findAudioById(d_corpus.aud_id)
    # play_audio_with_pygame(aud.aud_url)

    # 录音线程
    mic_audio_name = f"{corpus_id}_mic.pcm"
    mic_storage_path = get_mic_dir()
    mic_audio_path = os.path.join(
        mic_storage_path, project_id, str(turn_id), plan_id, corpus_id
    )
    if not os.path.exists(mic_audio_path):
        os.makedirs(mic_audio_path)
    mic_audio_path = os.path.join(str(mic_audio_path), mic_audio_name)
    # sub_thread1 = f"{project_id}_sub_1"
    # recoder_thread = thread_manager.start_sub_thread(
    #     project_id,
    #     sub_thread1,
    #     record_for_time_by_sd,
    #     float(d_corpus.audio_duration),
    #     mic_audio_path,
    # )

    # 播放干扰语料线程
    # start_time = time.time()
    sub_thread2 = f"{project_id}_sub_2"
    log.info(
        f" 项目名={show_project.project_name} # 播放干扰语料 :corpus_id=({corpus_id}) path=({aud.aud_url})"
    )
    output_device_id = deviceConfig.get_disturb_corpus_device_id()
    volume = deviceConfig.get_disturb_corpus_device_volume()
    audio_thread = thread_manager.start_sub_thread(
        project_id, sub_thread2, play_audio_with_sounddevice, get_request_id(), aud.aud_url, False, "", "",
        output_device_id, volume
    )

    # 误唤醒检查线程
    pic_storage_path = get_pic_dir()
    pic_path = os.path.join(
        pic_storage_path, project_id, str(turn_id), plan_id, corpus_id
    )
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)
    svc_client = QUBEImageSvc()
    pic_name = generate_random_string()
    svc_client.photograph(pic_path, pic_name)
    sub_thread3 = f"{project_id}_sub_3"
    check_thread = thread_manager.start_sub_thread(
        project_id,
        sub_thread3,
        svc_client.false_awake_start,
        project_id,
        pic_path,
        pic_name,
    )

    # 等待三个任务完成
    # recoder_thread.join()
    audio_thread.join()

    wav_file = pcm_to_wav(mic_audio_path)
    time_list = svc_client.false_awake_stop()
    plan = PlanQueryDao.findProjectPlanById(plan_id)
    num = 0
    false_wakeup_times = 0
    if plan.false_wakeup_times is not None:
        false_wakeup_times = plan.false_wakeup_times

    if len(time_list) == 0:
        random_str = generate_random_string()
        result = FalseRouseResult(
            result_id="0000" + random_str,
            project_id=project_id,
            plan_id=plan_id,
            disturb_corpus_id=corpus_id,
            turn_id=turn_id,
            relative_interval=0,
            video_path=video_path,
            cabin_video_path=video_path.replace(".mp4", "_cabin.mp4"),
        )
        ResultOperateDao.saveFalseRouseResult(result)
    # 这个位置加for循环
    for t in time_list:
        relative_interval = int(t - start_time)
        local_time = time.localtime(t)
        image_url = os.path.join(str(pic_path), "%s-%d.jpg" % (pic_name, num))
        random_str = find_table_id("false_rouse_result_id")
        result = FalseRouseResult(
            result_id=random_str,
            project_id=project_id,
            plan_id=plan_id,
            disturb_corpus_id=corpus_id,
            turn_id=turn_id,
            asr_result="-",
            image=image_url,
            mic_audio_url=wav_file,
            relative_interval=relative_interval,
            time=time.strftime("%Y-%m-%d %H:%M:%S", local_time),
            video_path=video_path,
            cabin_video_path=video_path.replace(".mp4", "_cabin.mp4"),
        )
        num += 1
        save_result = ResultOperateDao.saveFalseRouseResult(result)
        # 误唤醒

    data = {"play_config_id": plan.play_config_id}
    start_config = PlayConfigQueryDao.showAllStartConfig(data)
    config = start_config[0]
    if config.false_wakeup_times:
        update_data = {"false_wakeup_times": false_wakeup_times + num}
        PlanOperateDao.updateProjectPlan(plan_id, update_data)


def play_backgroundnoise(corpus_id, channel="channel1", gain=0):
    b_corpus = CorpusQueryDao.findBackgroundNoiseById(corpus_id)
    output_device_id = deviceConfig.get_disturb_corpus_device_id()
    volume = deviceConfig.get_disturb_corpus_device_volume()
    aud = CorpusQueryDao.findAudioById(b_corpus.aud_id)
    if not aud:
        log.warn("play_backgroundnoise error: not audio")
        return
    global stop_event
    while not stop_event.is_set():
        play_audio_with_sounddevice(get_request_id(), aud.aud_url, device_id=output_device_id, volume=volume)
    stop_event = threading.Event()


def get_word_recognition_rate(plan_id, max_similarity):
    plan = PlanQueryDao.findProjectPlanById(plan_id)
    data = {"plan_id": plan_id}
    result_list, _ = ResultQueryDao.showAllTestResult(data)
    word_recognition_rate = 0
    if len(result_list) != 0 and plan.word_recognition_rate is not None:
        word_recognition = (len(result_list) - 1) * (
                plan.word_recognition_rate / 100
        ) + max_similarity
        word_recognition_rate = word_recognition / len(result_list)
    else:
        word_recognition_rate = max_similarity
    updata_data = {"word_recognition_rate": round(word_recognition_rate * 100, 2)}
    PlanOperateDao.updateProjectPlan(plan_id, updata_data)


def play_testcorpus(
        play_config_type,
        data_index_config,
        project_id,
        plan_id,
        corpus_id,
        circle,
        repeat,
        wait_time,
        timeout,
        turn,
        relative_interval,
        # thread_manager: "ThreadManager",
        channel="channel1",
        gain=0,
        is_multi=False,
        init=False,
        video_path="",
):
    show_project = ProjectQueryDao.findTestProjectById(project_id)
    show_plan = PlanQueryDao.findProjectPlanById(plan_id)
    expect_result = ""
    request_id = re.sub(r'\([^)]*\)', '', get_request_id())
    request_id = f"{request_id}({corpus_id}-{circle}-{repeat})"
    set_request_id(request_id)
    if "rouse" in play_config_type:
        t_corpus = CorpusQueryDao.findRouseCorpusById(corpus_id)
    else:
        t_corpus = CorpusQueryDao.findTestCorpusById(corpus_id)
        expect_result = t_corpus.expect_result
    aud = CorpusQueryDao.findAudioById(t_corpus.aud_id)
    if not aud:
        log.warn(f"play test corpus no find corpus. {t_corpus.aud_id =}")
        return
    exclude_time = get_audio_duration_ms(aud.aud_url)

    pic_storage_path = get_pic_dir()
    pic_path = os.path.join(pic_storage_path, project_id, str(turn), plan_id, f"{circle}_{repeat}", corpus_id)
    if not os.path.exists(pic_path):
        os.makedirs(pic_path)

    mic_audio_name = f"{corpus_id}_mic_{repeat}.pcm"
    mic_storage_path = get_mic_dir()
    mic_audio_path = os.path.join(
        mic_storage_path, project_id, str(turn), plan_id, f"{circle}_{repeat}", corpus_id
    )
    if not os.path.exists(mic_audio_path):
        os.makedirs(mic_audio_path)
    mic_audio_path = os.path.join(str(mic_audio_path), mic_audio_name)
    # 拷贝语料到mic目录
    shutil.copy(aud.aud_url, os.path.dirname(mic_audio_path))

    can_storage_path = get_can_dir()
    can_storage_path = os.path.join(
        can_storage_path, project_id, str(turn), plan_id, f"{circle}_{repeat}", corpus_id
    )
    can_data_url = os.path.join(str(can_storage_path), f"{corpus_id}_can_data.txt")
    result_id = f"test_result_{find_table_id('test_result_id')}"
    random_str = generate_random_string()
    result_photo_path = os.path.join(str(pic_path), f"{random_str}_result.jpg")
    file_name = f"{circle}_{repeat}" + "_" + t_corpus.corpus_id
    svc_client = QUBEImageSvc()
    photo_map = {
        "result_photo_path": result_photo_path,
        "file_name": file_name,
        "random_str": random_str,
        "project_id": project_id,
        "svc_client": svc_client,
        "pic_path": pic_path,
        "audio_duration": t_corpus.audio_duration,
        "type": show_plan.type
    }
    # ocr线程
    photograph_thread = None
    if play_config_type != "rouse":
        sub_thread1 = f"{project_id}_sub_1"
        image_name = svc_client.photograph(pic_path, f"{random_str}_1.jpg")
        if not image_name:
            log.warn("take result photo(1) fail")
        photograph_thread = thread_manager.start_sub_thread(
            project_id,
            sub_thread1,
            svc_client.photograph_process,
            request_id,
            pic_path,
            file_name,
            t_corpus.audio_duration,
            random_str,
        )
    # relative_interval = int(time.time() - start_time)

    # 录音线程
    sub_thread3 = f"{project_id}_sub_2"
    q = queue.Queue()
    recoder_thread = thread_manager.start_sub_thread(
        project_id,
        sub_thread3,
        play_record,
        request_id,
        q,
        mic_audio_path,
        exclude_time,
        wait_time,
        timeout,
        photo_map,
    )
    # time.sleep(0.5)

    # 播放线程
    sub_thread2 = f"{project_id}_sub_3"
    log.info(
        f" 项目名={show_project.project_name} # 方案名={show_plan.plan_name} # 轮次={turn} # 语料文本={t_corpus.text}  play audio : path=({aud.aud_url})"
    )
    output_device_id = deviceConfig.get_test_corpus_device_id()
    volume = deviceConfig.get_test_corpus_device_volume()
    audio_thread = thread_manager.start_sub_thread(
        project_id, sub_thread2, play_audio_with_sounddevice, request_id, aud.aud_url,
        data_index_config["can_detection"],
        can_storage_path, can_data_url, output_device_id, volume
    )

    # 等待三个任务完成
    audio_thread.join()
    recoder_thread.join()
    if play_config_type != "rouse" and photograph_thread is not None:
        photograph_thread.join()
    if is_multi:
        r_time = None
    else:
        r_time = q.get()

    log.info(
        f" 项目名={show_project.project_name} # 方案名={show_plan.plan_name} # 轮次={turn} # 语料文本={t_corpus.text} car respond: recorded_audio_path={mic_audio_path}, time={r_time}ms"
    )
    sign_list = "0b0000000"
    result_path, result_text, max_similarity, confidence = "", "", 0.0, None
    if data_index_config["word_recognition_rate"] and play_config_type == "interaction":
        path_list = list_all_files(pic_path, file_name)
        result_path, result_text, max_similarity, confidence = (
            svc_client.image_select_by_multi_recognition(
                path_list, t_corpus.text, project_id, project_init=init
            )
        )
        if max_similarity < 0.8:
            sign_list = encode_signs(sign_list, "word_accuracy_abnormal")
        if confidence:
            confidence = round(10 * confidence, 2)
        log.info(
            f" 项目名={show_project.project_name} # 方案名={show_plan.plan_name} # 轮次={turn} # 语料文本={t_corpus.text} ocr result : save_path=({result_path}), ocr_text=({result_text}), max_similarity={max_similarity}"
        )
        # if result_text == "":
        #     result_text = "获取失败"
    image_path = os.path.join(str(pic_path), random_str + "_result.jpg")
    full_record = mic_audio_path[:-4] + "_full.pcm"
    wav_file_full = pcm_to_wav(full_record)
    pcm_to_wav(mic_audio_path)

    r_result, reason, mic_audio_text = "未测试", "未测试", "未测试"
    if (data_index_config["interaction_success_rate"] and (play_config_type == "interaction" or is_multi)) or \
            (data_index_config["wakeup_success_rate"] and play_config_type == "rouse"):
        r_result, reason, mic_audio_text = "分析中", "获取中", "获取中"
    can_result = "未测试"
    if data_index_config["can_detection"]:
        res, save_path = judge_can(can_data_url, t_corpus.target_signal)
        if res:
            can_result = "通过"
            # can_data_result_url = save_path
        else:
            can_result = "不通过"

    result = TestResult(
        result_id=result_id,
        project_id=project_id,
        plan_id=plan_id,
        corpus_id=corpus_id,
        turn_id=turn,
        circle=circle,
        relative_interval=int(relative_interval),
        test_scenario=t_corpus.test_scenario,
        text=t_corpus.text,
        result=r_result,
        can_result=can_result,
        reason=reason,
        asr_result=mic_audio_text,
        image=image_path,
        mic_audio_url=wav_file_full,
        ocr_pic_url=result_path,
        ocr_result=result_text,
        ocr_accuracy_rate=max_similarity,
        response_time=r_time,
        expect_result=expect_result,
        image_confidence=confidence,
        signlist=sign_list,
        can_data_url=can_data_url,
        video_path=video_path,
        cabin_video_path=video_path.replace(".mp4", "_cabin.mp4"),
        request_id=request_id,
    )
    upload_data = [result, aud.aud_url]
    config_str = play_config_type + str(data_index_config["wakeup_time"]) + \
                 str(data_index_config["wakeup_success_rate"]) + str(data_index_config["false_wakeup_times"]) + \
                 str(data_index_config["interaction_success_rate"]) + str(data_index_config["word_recognition_rate"]) + \
                 str(data_index_config["response_time"]) + str(data_index_config["can_detection"])

    upload_data.append(config_str)
    save_result = ResultOperateDao.saveTestResult(result)

    interval_wait_time = data_index_config.get(SinglePlayConfig.start_interval_wait_time, 0)
    if interval_wait_time:
        time.sleep(interval_wait_time)
    if is_multi and not init:
        DataUpload_Queue.put(upload_data)
        return save_result
    if not init:
        DataUpload_Queue.put(upload_data)
        LLMJudge_Queue.put(save_result)
    if data_index_config["word_recognition_rate"]:
        get_word_recognition_rate(plan_id, max_similarity)
    return None


def play_wait(wait):
    time.sleep(wait / 1000.0)


def get_max_turn(project_id):
    turn1 = ResultQueryDao.getMaxTurnForTestResult(project_id)
    turn2 = ResultQueryDao.getMaxTurnForFalseResult(project_id)
    turn3 = ResultQueryDao.getMaxTurnForMultiResult(project_id)

    values = [x for x in [turn1, turn2, turn3] if x is not None]
    if not values:
        return 1

    return max(values) + 1


def play_multicorpus(
        play_config_type,
        data_index_config,
        project_id,
        plan_id,
        corpus_id,
        circle,
        repeat,
        wait_time,
        timeout,
        turn,
        start_time,
        # thread_manager: "ThreadManager",
        channel="channel1",
        gain=0,
        init=False,
        video_path="",
):
    multi_corpus = get_multi_testcorpus(corpus_id)
    mul_result = []
    if data_index_config["interaction_success_rate"]:
        m_result = "分析中"
    else:
        m_result = "未测试"
    multi_result_id = f"multi_result_{find_table_id('multi_result_id')}"
    multi_result = MultiResult(
        multi_result_id=multi_result_id,
        project_id=project_id,
        plan_id=plan_id,
        multicorpus_id=corpus_id,
        turn_id=turn,
        circle=circle,
        result=m_result,
    )
    save_multi_result = (
        ResultOperateDao.saveMultiResult(
            multi_result
        )
    )
    for m_corpus_id in multi_corpus:
        relative_interval = time.time() - start_time
        multi_res = play_testcorpus(
            play_config_type,
            data_index_config,
            project_id,
            plan_id,
            m_corpus_id,
            circle,
            repeat,
            wait_time,
            timeout,
            turn,
            relative_interval,
            channel="channel1",
            gain=0,
            is_multi=True,
            init=init,
            video_path=video_path,
        )

        if multi_res is not None:
            log.info(
                f"--- 获取到 {m_corpus_id} 多轮语料的结果 --- "
            )
            mul_result.append(multi_res)
    # if data_index_config["interaction_success_rate"] == True:
    log.info(
        f"--- 将多轮语料结果放进模型处理队列中 --- "
    )
    mul_result.append(save_multi_result)
    LLMJudge_Queue.put(mul_result)


def create_unique_directory(base_path):
    # 确保基础目录存在
    os.makedirs(base_path, exist_ok=True)

    i = 1
    while True:
        dir_path = os.path.join(base_path, str(i))
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            return dir_path
        i += 1


def execute_queue():
    start_time = time.time()
    video_close_flag = False
    video_path = ""
    running_project_id = ""
    video = Video(VIDEO_NAME)
    cabin_video = Video(CABIN_VIDEO_NAME)

    def get_config_has_rouse(plan_id):
        plan_config = get_config_by_play_id(plan_id)
        res, _ = play_config_list({"play_config_id": plan_config.play_config_id})
        configs = res[0]["configs"]
        for config_type in configs:
            if config_type.get("type", "") == "嵌入唤醒":
                return True
        return False

    while True:
        try:
            if not get_app_run():
                time.sleep(0.5)
                continue
            # time.sleep(0.1)
            running_project_id = find_running_project_id()
            exec_task = ProjectQueryDao.findExecuteQueueFirst(running_project_id)

            if exec_task is None:
                # set_app_run(False)
                log.info("execute_queue exec_task is None.")
                time.sleep(0.5)
                continue
            set_request_id(exec_task.request_id)
            log.info(f"--- execute_queue --- {exec_task.project_id} --- {exec_task.execute_status}")
            if exec_task.execute_status == "project_start":
                update_status = {"project_status": "progressing"}
                ProjectOperateDao.updateTestProject(exec_task.project_id, update_status)
                start_time = time.time()
                thread_manager.start_thread(exec_task.project_id)
            elif exec_task.execute_status == "plan_start":
                update_status = {"plan_status": "progressing"}
                PlanOperateDao.updateProjectPlan(exec_task.plan_id, update_status)
            elif exec_task.execute_status == "play_rousecorpus":
                video_path = os.path.join(create_unique_directory(exec_task.video_path), f"{exec_task.corpus_id}.mp4")
                cabin_video_path = video_path.replace(".mp4", "_cabin.mp4")
                if (exec_task.play_config_type == PlayConfigType.interaction or
                        exec_task.play_config_type == PlayConfigType.multi):
                    video.start(video_path)
                    cabin_video.start(cabin_video_path)
                elif exec_task.play_config_type == PlayConfigType.rouse:
                    video.start(video_path)
                    cabin_video.start(cabin_video_path)
                    video_close_flag = True

                play_rousecorpus(
                    exec_task.corpus_id, exec_task.play_wait, channel="channel1", gain=0
                )
            elif exec_task.execute_status == "play_backgroundnoise":
                play_thread_id = f"play_backgroundnoise_{exec_task.corpus_id}"
                background_thread = thread_manager.start_sub_thread(
                    exec_task.project_id, play_thread_id, play_backgroundnoise, exec_task.corpus_id
                )
            elif exec_task.execute_status == "play_disturbcorpus":
                if exec_task.play_config_type == PlayConfigType.false_rouse:
                    video_path = os.path.join(create_unique_directory(exec_task.video_path), "false_rouse.mp4")
                    cabin_video_path = video_path.replace(".mp4", "_cabin.mp4")
                    video.start(video_path)
                    cabin_video.start(cabin_video_path)
                    video_close_flag = True
                play_disturbcorpus(
                    exec_task.project_id,
                    exec_task.corpus_id,
                    exec_task.plan_id,
                    exec_task.turn,
                    start_time, channel="channel1", gain=0, video_path=video_path,
                )
            elif exec_task.execute_status == "play_testcorpus":
                relative_interval = time.time() - start_time
                if exec_task.repeat != 0 or not get_config_has_rouse(exec_task.plan_id):
                    video_path = os.path.join(create_unique_directory(exec_task.video_path),
                                              f"{exec_task.corpus_id}.mp4")
                    cabin_video_path = video_path.replace(".mp4", "_cabin.mp4")
                    video.start(video_path)
                    cabin_video.start(cabin_video_path)
                play_testcorpus(
                    exec_task.play_config_type,
                    json.loads(exec_task.data_index_config),
                    exec_task.project_id,
                    exec_task.plan_id,
                    exec_task.corpus_id,
                    exec_task.circle,
                    exec_task.repeat,
                    exec_task.wait_time,
                    exec_task.timeout,
                    exec_task.turn,
                    relative_interval,
                    channel="channel1", gain=0, is_multi=False, init=exec_task.init,
                    video_path=video_path,
                )
                video_close_flag = True
            elif exec_task.execute_status == "play_multi":
                # relative_interval = time.time() - start_time
                if exec_task.repeat != 0 or not get_config_has_rouse(exec_task.plan_id):
                    video_path = os.path.join(create_unique_directory(exec_task.video_path),
                                              f"{exec_task.corpus_id}.mp4")
                    cabin_video_path = video_path.replace(".mp4", "_cabin.mp4")
                    video.start(video_path)
                    cabin_video.start(cabin_video_path)
                play_multicorpus(
                    exec_task.play_config_type,
                    json.loads(exec_task.data_index_config),
                    exec_task.project_id,
                    exec_task.plan_id,
                    exec_task.corpus_id,
                    exec_task.circle,
                    exec_task.repeat,
                    exec_task.wait_time,
                    exec_task.timeout,
                    exec_task.turn,
                    start_time,
                    channel="channel1", gain=0, init=exec_task.init,
                    video_path=video_path
                )
                video_close_flag = True
            elif exec_task.execute_status == "play_wait":
                play_wait(exec_task.play_wait)
            elif exec_task.execute_status == "modify_process":
                process = int((exec_task.circle / exec_task.repeat) * 100)
                update_process = {"project_process": str(process)}
                ProjectOperateDao.updateTestProject(
                    exec_task.project_id, update_process
                )
            elif exec_task.execute_status == "plan_completed":
                stop_event.set()
                update_status = {"plan_status": "completed"}
                PlanOperateDao.updateProjectPlan(exec_task.plan_id, update_status)
            elif exec_task.execute_status == "project_finish":
                stop_event.set()
                update_status = {"project_status": "completed"}
                ProjectOperateDao.updateTestProject(exec_task.project_id, update_status)
                if exec_task.project_id in thread_manager.threads:
                    thread_manager.stop_thread(exec_task.project_id)
                # video.close()
                review_data = ReviewResult(
                    project_id=exec_task.project_id,
                    turn_id=exec_task.turn,
                    video_path=exec_task.video_path,
                )
                ReviewResultDao.save(review_data)
                set_app_run(False)
            if video_close_flag:
                video.close()
                cabin_video.close()
                video_close_flag = False
            ProjectOperateDao.deleteExecuteQueue(exec_task)
        except Exception as e:
            log.warn(f"error: {e}")
            video.close()
            cabin_video.close()
            video_close_flag = False
            if running_project_id:
                exec_task = ProjectQueryDao.findExecuteQueueFirst(running_project_id)
                if exec_task:
                    ProjectOperateDao.deleteExecuteQueue(exec_task)
            time.sleep(0.5)


def test_execute(data: dict):
    project_id = data["project_id"]
    request_id = get_request_id()
    video_path = ""
    check_project_status = ProjectQueryDao.findTestProjectById(project_id)
    if get_app_run() or check_project_status.project_status == "progressing":
        return 1, f"The {project_id} is running"
    elif check_project_status.project_status == "stopped":
        update_status = {"project_status": "progressing"}
        ProjectOperateDao.updateTestProject(data["project_id"], update_status)
        set_app_run(True)
        update_running_project_id(project_id)
        exec_task = ProjectQueryDao.findExecuteQueueFirst(project_id)
        if exec_task:
            return 0, f"The {project_id} has been restarted"
    set_app_run(True)
    if "turn_id" in data:
        turn_id = data["turn_id"]
        check_plan = PlanQueryDao.findProjectPlanById(data["plan_id"])
        if check_plan.plan_status == "completed":
            return 1, f"该复测方案{check_plan}已测试过"
    else:
        turn_id = get_max_turn(project_id)
    # video_path = os.path.join(
    #     globalAppSettings.video_dir, f"{project_id}_{turn_id}.mp4"
    # )
    init = False
    if "init" in data:
        init = True
    try:
        execute_info = ExecuteQueue(
            execute_status="project_start",
            project_id=project_id,
            turn=turn_id,
            request_id=request_id,
        )
        ProjectOperateDao.saveExecuteQueue(execute_info)
        update_running_project_id(project_id)
        # plan_list = test_plan_list(data)
        data["is_delete"] = False
        if "plan_id" in data:
            plan_list, _ = PlanQueryDao.showAllProjectPlan(data)
        else:
            plan_list = PlanQueryDao.FilterAllProjectPlan(data)
        # audio = create_audio_client()
        corpus_num = 0
        is_background = []
        for plan_temp in plan_list:
            corpuslist = get_corpuslist_byplanid(plan_temp.plan_id)
            if plan_temp.play_config_id is None:
                continue
            play_data = {"play_config_id": plan_temp.play_config_id}
            res, num = play_config_list(play_data)
            # config_data = {"data": res, "total": num}
            configs = res[0]["configs"]
            play_config_type = res[0]["type"]
            # 循环次数
            circle = 0
            for play_config_obj in configs:
                if play_config_obj["type"] == "开始":
                    config = play_config_obj["config"]
                    if "turn_id" in data:
                        circle = 1
                    else:
                        circle = int(config["circle"])
                elif play_config_obj["type"] == "播放背景噪声":
                    if corpuslist.get("backgroundNoiseList"):
                        background = corpuslist["backgroundNoiseList"][0]
                        execute_info = ExecuteQueue(
                            execute_status="play_backgroundnoise",
                            project_id=project_id,
                            corpus_id=background,
                            turn=turn_id,
                            request_id=request_id,
                        )
                        obj = {"plan": plan_temp.plan_id, "execute_info": execute_info}
                        is_background.append(obj)
            if "rouse" == play_config_type:  # 唤醒测试
                corpus_num += len(corpuslist["rouseCorpusList"]) * circle
            elif "false-rouse" == play_config_type:  # 误唤醒测试
                corpus_num += len(corpuslist["disturbCorpusList"]) * circle
            else:
                corpus_num += len(corpuslist["testCorpusList"]) * circle

        total_num = 0
        rouse = ""
        disturb = ""
        for plan_temp in plan_list:
            if plan_temp.is_delete:
                continue
            video_path = os.path.join(globalAppSettings.video_dir, project_id, str(turn_id), plan_temp.plan_id)
            if not os.path.exists(video_path):
                os.makedirs(video_path)
            execute_info = ExecuteQueue(
                execute_status="plan_start",
                project_id=project_id,
                plan_id=plan_temp.plan_id,
                video_path=video_path,
                turn=turn_id,
                request_id=request_id,
            )
            ProjectOperateDao.saveExecuteQueue(execute_info)
            for obj in is_background:
                if obj["plan"] == plan_temp.plan_id:
                    ProjectOperateDao.saveExecuteQueue(obj["execute_info"])
            corpuslist = get_corpuslist_byplanid(plan_temp.plan_id)
            # 开始根据播放配置进行 语料播放
            # 获取播放配置
            if plan_temp.play_config_id is None:
                continue
            play_data = {"play_config_id": plan_temp.play_config_id}
            res, num = play_config_list(play_data)
            # config_data = {"data": res, "total": num}
            configs = res[0]["configs"]
            play_config_type = res[0]["type"]
            # 循环次数
            circle = 0
            data_index_config = {}
            if configs[0]["type"] == "开始":
                config = configs[0]["config"]
                data_index_config = config
                circle = int(config["circle"])
            else:
                # return "Incorrect playback configuration"
                continue
            # 测试语料
            testlist = []
            if "rouse" == play_config_type:  # 唤醒测试
                testlist = corpuslist["rouseCorpusList"]
            elif "false-rouse" == play_config_type:  # 误唤醒测试
                testlist = corpuslist["disturbCorpusList"]
            else:
                testlist = corpuslist["testCorpusList"]
            # 唤醒语料
            rouse_len = len(corpuslist["rouseCorpusList"])
            if rouse_len != 0:
                rouse = corpuslist["rouseCorpusList"][0]
            elif play_config_type != "false-rouse":
                # return "Not correct Rouse Corpus"
                continue
            # 干扰语料，这个位置我发现前端是多选，这儿默认是单选
            if corpuslist.get("disturbCorpusList"):
                disturb = corpuslist["disturbCorpusList"][0]
            # 背景噪声
            # if corpuslist.get("backgroundNoiseList"):
            #     background = corpuslist["backgroundNoiseList"][0]

            for i in range(circle):
                test_num = 0
                for test in testlist:
                    test_num += 1
                    wake_repeat = 0
                    freq_repeat = ""
                    freq = ""
                    for config in configs:
                        config_info = config["config"]
                        config_type = config["type"]
                        if config_type == "开始":
                            continue
                        elif config_type == "嵌入唤醒":
                            freq = config_info[
                                "frequencyDifferent"
                            ]  # 不同语料间唤醒频率 first 仅一次 every 每次 interval 间隔 frequency_interval 次
                            freq_repeat = config_info[
                                "frequencyRepeated"
                            ]  # 语料重复时唤醒频率 none 不嵌入 every 每次
                            freq_interval = config_info[
                                "frequencyIntervalDifferent"
                            ]  # 不同语料间间隔次数，当上面为 interval间隔 的时候起作用
                            freq_interval += 1
                            wait = config_info["wakeUpWaitDifferent"]
                            wake_repeat = config_info["wakeUpWaitRepeated"]
                            if freq == "first" and test_num == 1:
                                execute_info = ExecuteQueue(
                                    execute_status="play_rousecorpus",
                                    project_id=project_id,
                                    corpus_id=rouse,
                                    play_wait=wait,
                                    video_path=video_path,
                                    turn=turn_id,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                            elif freq == "every":
                                execute_info = ExecuteQueue(
                                    execute_status="play_rousecorpus",
                                    project_id=project_id,
                                    corpus_id=rouse,
                                    play_wait=wait,
                                    video_path=video_path,
                                    turn=turn_id,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                            elif (
                                    freq == "interval"
                                    and freq_interval
                                    and (test_num - 1) % freq_interval == 0
                            ):
                                execute_info = ExecuteQueue(
                                    execute_status="play_rousecorpus",
                                    project_id=project_id,
                                    corpus_id=rouse,
                                    play_wait=wait,
                                    video_path=video_path,
                                    turn=turn_id,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                            elif freq == "every" and freq_repeat == "every":
                                execute_info = ExecuteQueue(
                                    execute_status="play_rousecorpus",
                                    project_id=project_id,
                                    corpus_id=rouse,
                                    play_wait=wake_repeat,
                                    video_path=video_path,
                                    turn=turn_id,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                        # elif config_type == "播放背景噪声":
                        #     execute_info = ExecuteQueue(
                        #         execute_status="play_backgroundnoise",
                        #         project_id=project_id,
                        #         corpus_id=background,
                        #         video_path=video_path,
                        #         turn=turn_id,
                        #     )
                        #     ProjectOperateDao.saveExecuteQueue(execute_info)
                        elif config_type == "播放干扰音":
                            if play_config_type == "false-rouse":
                                init_photo = os.path.join(
                                    globalAppSettings.photo_project_init_dir,
                                    f"{project_id}.jpg",
                                )
                                init_area_txt = os.path.join(
                                    globalAppSettings.photo_project_init_dir,
                                    f"{project_id}.txt",
                                )
                                if not os.path.exists(
                                        init_area_txt
                                ) or not os.path.exists(init_photo):
                                    log.error("no init photo or txt")
                                    return 1, "no init photo or txt"
                                execute_info = ExecuteQueue(
                                    execute_status="play_disturbcorpus",
                                    project_id=project_id,
                                    corpus_id=test,
                                    plan_id=plan_temp.plan_id,
                                    turn=turn_id,
                                    video_path=video_path,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                            else:
                                execute_info = ExecuteQueue(
                                    execute_status="play_disturbcorpus",
                                    project_id=project_id,
                                    corpus_id=disturb,
                                    plan_id=plan_temp.plan_id,
                                    turn=turn_id,
                                    video_path=video_path,
                                    play_config_type=play_config_type,
                                    request_id=request_id,
                                )
                                ProjectOperateDao.saveExecuteQueue(execute_info)
                        elif config_type == "播放语料" or (
                                config_type == "播放唤醒" and play_config_type == "rouse"
                        ):
                            repeat_num = config_info["repeat"]
                            wait_time = 2
                            timout = 10
                            if "rouse" not in play_config_type:
                                wait_time = config_info["wait_time"]
                                timout = config_info["timout"]
                            # 传一个类型来判断是 唤醒场景还是交互场景
                            for j in range(repeat_num):
                                check_status = ProjectQueryDao.findTestProjectById(
                                    data["project_id"]
                                )
                                if check_status.project_status == "stopped":
                                    return "stop test"
                                if "rouse" not in play_config_type:
                                    # 交互场景
                                    t_corpus = CorpusQueryDao.findTestCorpusById(test)
                                    if t_corpus is None:
                                        # 多伦
                                        execute_info = ExecuteQueue(
                                            execute_status="play_multi",
                                            play_config_type=play_config_type,
                                            data_index_config=json.dumps(data_index_config),
                                            project_id=project_id,
                                            plan_id=plan_temp.plan_id,
                                            corpus_id=test,
                                            circle=i,
                                            repeat=j,
                                            wait_time=wait_time,
                                            timeout=timout,
                                            turn=turn_id,
                                            is_multi=True,
                                            video_path=video_path,
                                            request_id=request_id,
                                        )
                                        ProjectOperateDao.saveExecuteQueue(execute_info)
                                    else:
                                        # 一次交互的测试语料
                                        execute_info = ExecuteQueue(
                                            execute_status="play_testcorpus",
                                            play_config_type=play_config_type,
                                            data_index_config=json.dumps(data_index_config),
                                            project_id=project_id,
                                            plan_id=plan_temp.plan_id,
                                            corpus_id=test,
                                            circle=i,
                                            repeat=j,
                                            wait_time=wait_time,
                                            timeout=timout,
                                            turn=turn_id,
                                            init=init,
                                            video_path=video_path,
                                            request_id=request_id,
                                        )
                                        ProjectOperateDao.saveExecuteQueue(execute_info)
                                else:
                                    # 唤醒场景
                                    execute_info = ExecuteQueue(
                                        execute_status="play_testcorpus",
                                        play_config_type=play_config_type,
                                        data_index_config=json.dumps(data_index_config),
                                        project_id=project_id,
                                        plan_id=plan_temp.plan_id,
                                        corpus_id=test,
                                        circle=i,
                                        repeat=j,
                                        wait_time=wait_time,
                                        timeout=timout,
                                        turn=turn_id,
                                        init=init,
                                        video_path=video_path,
                                        request_id=request_id,
                                    )
                                    ProjectOperateDao.saveExecuteQueue(execute_info)
                                if (
                                        repeat_num - j > 1
                                        and freq_repeat == "every"
                                        and freq == "every"
                                ):
                                    execute_info = ExecuteQueue(
                                        execute_status="play_rousecorpus",
                                        project_id=project_id,
                                        corpus_id=rouse,
                                        play_wait=wake_repeat,
                                        video_path=video_path,
                                        turn=turn_id,
                                        request_id=request_id,
                                    )
                                    ProjectOperateDao.saveExecuteQueue(execute_info)
                        elif config_type == "等待":
                            wait = config_info["wakeUpWaitDifferent"]
                            execute_info = ExecuteQueue(
                                execute_status="play_wait",
                                project_id=project_id,
                                play_wait=wait,
                                video_path=video_path,
                                turn=turn_id,
                                request_id=request_id,
                            )
                            ProjectOperateDao.saveExecuteQueue(execute_info)
                        check_status = ProjectQueryDao.findTestProjectById(
                            data["project_id"]
                        )
                        if check_status.project_status == "stopped":
                            return "stop test"
                    total_num += 1
                    execute_info = ExecuteQueue(
                        execute_status="modify_process",
                        project_id=project_id,
                        circle=total_num,
                        repeat=corpus_num,
                        video_path=video_path,
                        request_id=request_id,
                    )
                    ProjectOperateDao.saveExecuteQueue(execute_info)
            # 修改方案状态
            execute_info = ExecuteQueue(
                execute_status="plan_completed",
                project_id=project_id,
                plan_id=plan_temp.plan_id,
                video_path=video_path,
                turn=turn_id,
                request_id=request_id,
            )
            ProjectOperateDao.saveExecuteQueue(execute_info)
        # close_audio_client(audio)
        # execute_info = ExecuteQueue(
        #     execute_status="project_completed",
        #     project_id=project_id,
        # )
        # ProjectOperateDao.saveExecuteQueue(execute_info)
    except Exception as e:
        log.error("start test fail. ", str(e))
    finally:
        # 执行完毕后清理线程字典中的该线程
        # project_id = data["project_id"]
        # if project_id in thread_manager.threads:
        #     thread_manager.stop_thread(project_id)
        # 关闭视频录制
        execute_info = ExecuteQueue(
            execute_status="project_finish",
            project_id=project_id,
            turn=turn_id,
            video_path=video_path,
            request_id=request_id,
        )
        ProjectOperateDao.saveExecuteQueue(execute_info)
    return 0, ""


def get_multi_result(multi_result):
    multi_corpus = CorpusQueryDao.findMultiCorpusById(multi_result.multicorpus_id)
    multi = {"multi_result_id": multi_result.multi_result_id, "project_id": multi_result.project_id,
             "plan_id": multi_result.plan_id}
    plan = PlanQueryDao.findProjectPlanById(multi_result.plan_id)
    config_data = {"play_config_id": plan.play_config_id}
    starconfig = PlayConfigQueryDao.showAllStartConfig(config_data)
    config = starconfig[0]
    multi["plan_config"] = {
        "wakeup_time": config.wakeup_time,
        "wakeup_success_rate": config.wakeup_success_rate,
        "false_wakeup_times": config.false_wakeup_times,
        "interaction_success_rate": config.interaction_success_rate,
        "word_recognition_rate": config.word_recognition_rate,
        "response_time": config.response_time,
        "can_detection": config.can_detection,
    }
    multi["multicorpus_id"] = multi_result.multicorpus_id
    multi["turn_id"] = multi_result.turn_id
    multi["circle"] = multi_result.circle
    multi["result"] = multi_result.result
    if multi["result"] == "分析中":
        multi["reason"] = "分析中"
    else:
        multi["reason"] = multi_result.reason
    multi["success_rate"] = multi_result.success_rate
    multi["time"] = multi_result.time
    res_temp = []
    temp_log = ""
    for corpus in multi_corpus:
        data = {
            "project_id": multi_result.project_id,
            "turn_id": multi_result.turn_id,
            "circle": multi_result.circle,
            "plan_id": multi_result.plan_id,
            "corpus_id": corpus.testcorpus_id,
        }
        result_list, _ = ResultQueryDao.showAllTestResult(data)
        for result in result_list:
            temp = {"result_id": result.result_id, "time": result.time, "corpus_id": result.corpus_id,
                    "test_scenario": result.test_scenario, "text": result.text, "result": result.result,
                    "image": [result.image[:-10] + "1.jpg", result.image], "score": result.score,
                    "reason": result.reason, "asr_result": result.asr_result, "mic_audio_url": result.mic_audio_url,
                    "video_path": result.video_path, "update_result_reason": result.update_result_reason,
                    "cabin_video_path": result.cabin_video_path}
            if result.ocr_pic_url == "" and result.ocr_result == "" and math.isclose(result.ocr_accuracy_rate,
                                                                                     0.0) and result.image_confidence is None:
                temp["ocr_pic_url"] = "未测试"
                temp["ocr_result"] = "未测试"
                temp["ocr_accuracy_rate"] = "未测试"
                temp["image_confidence"] = "未测试"
            elif result.ocr_result == "获取失败":
                temp["ocr_pic_url"] = "获取失败"
                temp["ocr_result"] = "获取失败"
                temp["ocr_accuracy_rate"] = "未检测出"
                temp["image_confidence"] = "获取失败"
            else:
                temp["ocr_pic_url"] = result.ocr_pic_url
                temp["ocr_result"] = result.ocr_result
                temp["ocr_accuracy_rate"] = round(result.ocr_accuracy_rate, 2)
                temp["image_confidence"] = result.image_confidence
            temp["relative_interval"] = result.relative_interval
            # if math.isclose(result.response_time, -2.0):
            #     temp["response_time"] = "未测试"
            # elif math.isclose(result.response_time, -1):
            #     temp["response_time"] = "分析出错"
            # else:
            #     temp["response_time"] = result.response_time
            if result.expect_result is None or result.expect_result == "":
                temp["expect_result"] = "无"
            else:
                temp["expect_result"] = result.expect_result
            if result.result == "未测试":
                temp["llm_ui_confidence"] = "未测试"
                temp["llm_voice_confidence"] = "未测试"
            elif result.result == "分析出错":
                temp["llm_ui_confidence"] = "获取失败"
                temp["llm_voice_confidence"] = "获取失败"
            elif result.result == "分析中":
                temp["llm_ui_confidence"] = "获取中"
                temp["llm_voice_confidence"] = "获取中"
            else:
                if result.llm_ui_confidence is None:
                    temp["llm_ui_confidence"] = "无"
                else:
                    temp["llm_ui_confidence"] = result.llm_ui_confidence
                if result.llm_voice_confidence is None:
                    temp["llm_voice_confidence"] = "无"
                else:
                    temp["llm_voice_confidence"] = result.llm_voice_confidence
            temp["signlist"] = decode_signs(result.signlist)
            if os.path.exists(result.can_data_url):
                can_data = []
                # with open(result.can_data_url, 'r', encoding='gbk') as file:
                #     for line in file:
                #         can_data.append(line)
                corpus = CorpusQueryDao.findTestCorpusById(result.corpus_id)
                if corpus is not None and corpus.target_signal is not None:
                    target_signal = extract_title_and_values(corpus.target_signal)
                    for signal in target_signal:
                        can_data.append(signal["title"])
                temp["can_data"] = can_data
            temp_log = (
                    temp_log
                    + result.time.strftime("%Y-%m-%d %H:%M:%S")
                    + "  "
                    + result.text
                    + "\n"
            )
            res_temp.append(temp)
    # 这里查出来的result_list 没有按倒叙排，先List反转，后续再看
    res_temp.reverse()
    multi["sub_result"] = res_temp

    return multi, temp_log


def get_recent_testinfo(project_id, turn_id, plan_id, get_type="interaction",
                        page=0, page_size=0, test_result="", sign=""):
    total = 0
    project = ProjectQueryDao.findTestProjectById(project_id)
    if project is None:
        return {"status": "error", "error_msg": "Can't find available project_id"}, total
    plan = PlanQueryDao.findProjectPlanById(plan_id)
    if plan is None:
        return {}, total
    temp_log = ""
    res = {"process": project.project_process, "status": project.project_status, "video_url": "", "audio_url": "",
           "wakeup_time": plan.wakeup_time, "wakeup_success_rate": plan.wakeup_success_rate,
           "false_wakeup_times": plan.false_wakeup_times, "interaction_success_rate": plan.interaction_success_rate,
           "word_recognition_rate": plan.word_recognition_rate, "response_time": plan.response_time,
           "can_detection": plan.can_detection}
    data = {"project_id": project_id, "turn_id": turn_id, "plan_id": plan_id, "page": page, "page_size": page_size}
    if test_result:
        data["result"] = test_result
    if sign:
        sign_label = decode_signs_word(sign)
        if sign_label:
            signlist = encode_signs("0b00000000", sign_label)
            data["signlist"] = signlist
    result_list, total = ResultQueryDao.showAllTestResult(data)
    # if len(result_list) == 0:
    #     return {}

    if get_type == "interaction" or get_type == "rouse":
        res_temp = []
        for result in result_list:
            plan = PlanQueryDao.findProjectPlanById(result.plan_id)
            config_data = {"play_config_id": plan.play_config_id}
            starconfig = PlayConfigQueryDao.showAllStartConfig(config_data)
            config = starconfig[0]
            temp = {"result_id": result.result_id, "time": result.time, "project_id": result.project_id,
                    "plan_id": result.plan_id, "plan_config": {
                    "wakeup_time": config.wakeup_time,
                    "wakeup_success_rate": config.wakeup_success_rate,
                    "false_wakeup_times": config.false_wakeup_times,
                    "interaction_success_rate": config.interaction_success_rate,
                    "word_recognition_rate": config.word_recognition_rate,
                    "response_time": config.response_time,
                    "can_detection": config.can_detection,
                }, "corpus_id": result.corpus_id, "turn_id": result.turn_id, "test_scenario": result.test_scenario,
                    "text": result.text, "result": result.result, "can_result": result.can_result,
                    "reason": result.reason, "image": [result.image[:-10] + "1.jpg", result.image],
                    "score": result.score, "asr_result": result.asr_result, "mic_audio_url": result.mic_audio_url,
                    "video_path": result.video_path, "update_result_reason": result.update_result_reason,
                    "cabin_video_path": result.cabin_video_path}
            # log.info(
            #     f" {result.ocr_pic_url=} {result.ocr_result=} {result.ocr_accuracy_rate=} {result.image_confidence=}"
            # )
            if result.ocr_pic_url == "" and result.ocr_result == "" and math.isclose(result.ocr_accuracy_rate,
                                                                                     0.0) and result.image_confidence is None:
                temp["ocr_pic_url"] = "未测试"
                temp["ocr_result"] = "未测试"
                temp["ocr_accuracy_rate"] = "未测试"
                temp["image_confidence"] = "未测试"
            else:
                temp["ocr_pic_url"] = result.ocr_pic_url
                temp["ocr_result"] = result.ocr_result
                temp["ocr_accuracy_rate"] = round(result.ocr_accuracy_rate, 2)
                temp["image_confidence"] = result.image_confidence
            temp["relative_interval"] = result.relative_interval
            if result.expect_result is None or result.expect_result == "":
                temp["expect_result"] = "无"
            else:
                temp["expect_result"] = result.expect_result
            if result.response_time is None or math.isclose(result.response_time, -2.0):
                temp["response_time"] = "未测试"
            elif math.isclose(result.response_time, -1):
                temp["response_time"] = "未检测出"
            else:
                temp["response_time"] = result.response_time
            if result.result == "未测试":
                temp["llm_ui_confidence"] = "未测试"
                temp["llm_voice_confidence"] = "未测试"
            elif result.result == "分析出错":
                temp["llm_ui_confidence"] = "获取失败"
                temp["llm_voice_confidence"] = "获取失败"
            elif result.result == "分析中":
                temp["llm_ui_confidence"] = "获取中"
                temp["llm_voice_confidence"] = "获取中"
                temp["response_time"] = "分析中"
            else:
                if result.llm_ui_confidence is None:
                    temp["llm_ui_confidence"] = "无"
                else:
                    temp["llm_ui_confidence"] = result.llm_ui_confidence
                if result.llm_voice_confidence is None:
                    temp["llm_voice_confidence"] = "无"
                else:
                    temp["llm_voice_confidence"] = result.llm_voice_confidence
            temp["signlist"] = decode_signs(result.signlist)

            temp["relative_interval"] = result.relative_interval
            if get_type == "interaction":
                can_data = []
                # with open(result.can_data_url, 'r', encoding='gbk') as file:
                #     for line in file:
                #         can_data.append(line)
                corpus = CorpusQueryDao.findTestCorpusById(result.corpus_id)
                if corpus is not None and corpus.target_signal is not None:
                    target_signal = extract_title_and_values(corpus.target_signal)
                    for signal in target_signal:
                        can_data.append(signal["title"])
                temp["can_data"] = can_data
            temp_log = (
                    temp_log
                    + result.time.strftime("%Y-%m-%d %H:%M:%S")
                    + "  "
                    + result.text
                    + "\n"
            )
            res_temp.append(temp)
        res["result_list"] = res_temp
        res["log"] = temp_log
        return res, total
    elif get_type == "interaction-multi":
        multi_result, total = ResultQueryDao.showAllMultiResult(data)
        multi = []
        for result in multi_result:
            multi_temp, temp_log = get_multi_result(result)
            multi.append(multi_temp)
        res["result_list"] = multi
        res["log"] = temp_log
        return res, total
    elif get_type == "false-rouse":
        false_result, total = ResultQueryDao.showAllFalseRouseResult(data)
        false_list = []
        num = 0
        if len(false_result) > 0:
            res["false_wakeup_times"] = num
            res["video_path"] = false_result[0].video_path
            res["cabin_video_path"] = false_result[0].cabin_video_path
            if len(false_result) == 1 and "0000" in false_result[0].result_id:
                return res, total
            for result in false_result:
                false_body = {"asr_result": result.asr_result, "time": result.time, "image": result.image,
                              "mic_audio_url": result.mic_audio_url, "relative_interval": result.relative_interval,
                              "video_path": result.video_path, "cabin_video_path": result.cabin_video_path}
                false_list.append(false_body)
                if result.time:
                    temp_log = temp_log + result.time + "\n"
                num += 1
            res["result_list"] = false_list
            res["log"] = temp_log
            res["false_wakeup_times"] = num
            return res, total
    return {}, total


def test_suspend(data):
    set_app_run(False)
    ProjectOperateDao.truncateExecuteQueue()
    update_status = {"project_status": "completed", "project_process": str(100)}
    ProjectOperateDao.updateTestProject(data["project_id"], update_status)


def test_pause(data):
    set_app_run(False)
    update_status = {"project_status": "stopped"}
    ProjectOperateDao.updateTestProject(data["project_id"], update_status)


def update_test_result(data):
    for i in data["result_ids"]:
        update_result = {"result": data["result"]}
        ResultOperateDao.updateTestResult(i, update_result)
    return


def get_turn_list(data):
    if "project_id" not in data:
        return []
    project_id = data["project_id"]
    turns1 = ResultQueryDao.getTurnListBySet(project_id)
    turn_list1 = [name[0] for name in turns1]
    turns2 = ResultQueryDao.getFalseTurnListBySet(project_id)
    turn_list2 = [name[0] for name in turns2]
    turns3 = ResultQueryDao.getMultiTurnListBySet(project_id)
    turn_list3 = [name[0] for name in turns3]
    turn_set = set(turn_list1) | set(turn_list2) | set(turn_list3)
    return list(turn_set)


def get_review_video_name(project_id, turn_id):
    result = ReviewResultDao.getByPorjectAndTurn(project_id, turn_id)
    name = os.path.basename(result.video_path)
    return name


# rouse 唤醒  interaction 单轮对话
def do_excel(project_name, configtype, project_id, turn_id, plan_list, equired_field, order):
    res_temp = []
    for plan in plan_list:
        data = {"project_id": project_id, "turn_id": turn_id, "plan_id": plan.plan_id}
        result_list, _ = ResultQueryDao.showAllTestResult(data)
        data = {"play_config_id": plan.play_config_id}
        start_config = PlayConfigQueryDao.showAllStartConfig(data)
        config = start_config[0]

        for result in result_list:
            temp = {}
            if '项目名称' in equired_field:
                temp["项目名称"] = project_name
            if '方案名称' in equired_field:
                temp["方案名称"] = plan.plan_name
            if '结果id' in equired_field:
                temp["结果id"] = result.result_id
            if '测试时间' in equired_field:
                temp["测试时间"] = result.time
            if '语料id' in equired_field:
                temp["语料id"] = result.corpus_id
            if '第几轮测试' in equired_field:
                temp["第几轮测试"] = result.turn_id
            if '测试场景' in equired_field:
                temp["测试场景"] = result.test_scenario
            if '语料文本' in equired_field:
                temp["语料文本"] = result.text
            if '预期结果' in equired_field:
                temp["预期结果"] = result.expect_result
            if 'OCR图片判断置信度' in equired_field:
                temp["OCR图片判断置信度"] = result.image_confidence
            if '模型图片判断置信度' in equired_field:
                temp["模型图片判断置信度"] = result.llm_ui_confidence
            if '模型语音判断置信度' in equired_field:
                temp["模型语音判断置信度"] = result.llm_voice_confidence
            if '判断结果' in equired_field:
                temp["判断结果"] = result.result
            if configtype == "rouse":
                pass
            else:
                image_result = result.image
                if '判断分数' in equired_field:
                    temp["判断分数"] = result.score
                if '评估理由' in equired_field:
                    temp["评估理由"] = result.reason
                if '语料播出前图片' in equired_field:
                    temp["语料播出前图片"] = image_result[:-10] + "1.jpg"
                if '语料播出后图片' in equired_field:
                    temp["语料播出后图片"] = image_result
                if '车机识别结果' in equired_field:
                    temp["车机识别结果"] = result.ocr_result
            if '车机响应识别' in equired_field:
                temp["车机响应识别"] = result.asr_result
            if '录音路径' in equired_field:
                temp["录音路径"] = result.mic_audio_url
            if '结果标记' in equired_field:
                signlist = decode_signs(result.signlist)
                sign_str = " ".join(signlist)
                temp["结果标记"] = sign_str
            if '修改测试结果理由' in equired_field:
                temp["修改测试结果理由"] = result.update_result_reason
            if config.response_time is not None and configtype != "rouse":
                temp["车机响应时间(ms)"] = result.response_time
            if config.wakeup_time is not None and configtype == "rouse":
                temp["车机唤醒时间(ms)"] = result.response_time
            # if configtype != "rouse":
            #     temp["车机识别结果"] = result.ocr_result
            if config.word_recognition_rate is not None:
                temp["车机识别准确率"] = result.ocr_accuracy_rate

            res_temp.append(temp)
    file_path = get_excel_dir()
    ret, full_file_path = save_data_to_excel(file_path, project_name, turn_id, configtype, res_temp, order)
    return ret, full_file_path


def do_false_rouse_excel(project_name, configtype, project_id, turn_id, plan_list, equired_field, order):
    res_temp = []
    for plan in plan_list:
        data = {"project_id": project_id, "turn_id": turn_id, "plan_id": plan.plan_id}
        result_list, _ = ResultQueryDao.showAllFalseRouseResult(data)

        for result in result_list:
            temp = {}
            if '项目名称' in equired_field:
                temp["项目名称"] = project_name
            if '方案名称' in equired_field:
                temp["方案名称"] = plan.plan_name
            if '结果id' in equired_field:
                temp["结果id"] = result.result_id
            if '干扰语料id' in equired_field:
                temp["干扰语料id"] = result.disturb_corpus_id
            if '唤醒响应' in equired_field:
                temp["唤醒响应"] = result.asr_result
            if '误唤醒时刻' in equired_field:
                temp["误唤醒时刻"] = result.time
            if '第几轮测试' in equired_field:
                temp["第几轮测试"] = result.turn_id
            if '唤醒录音' in equired_field:
                temp["唤醒录音"] = result.mic_audio_url
            if '唤醒图像' in equired_field:
                temp["唤醒图像"] = result.image

            res_temp.append(temp)
    file_path = get_excel_dir()
    ret, full_file_path = save_data_to_excel(file_path, project_name, turn_id, configtype, res_temp, order)
    return ret, full_file_path


def do_multi_corpus_excel(project_name, configtype, project_id, turn_id, plan_list, equired_field, order):
    res_temp = []
    for plan in plan_list:
        data = {"project_id": project_id, "turn_id": turn_id, "plan_id": plan.plan_id}
        multi_results, _ = ResultQueryDao.showAllMultiResult(data)
        for multi_result in multi_results:
            multi_corpus = CorpusQueryDao.findMultiCorpusById(
                multi_result.multicorpus_id
            )
            data = {"play_config_id": plan.play_config_id}
            start_config = PlayConfigQueryDao.showAllStartConfig(data)
            config = start_config[0]
            for i, corpus in enumerate(multi_corpus):
                data = {
                    "project_id": multi_result.project_id,
                    "turn_id": multi_result.turn_id,
                    "plan_id": multi_result.plan_id,
                    "corpus_id": corpus.testcorpus_id,
                }
                result_list, _ = ResultQueryDao.showAllTestResult(data)
                for result in result_list:
                    temp = {}
                    image_result = result.image
                    if '项目名称' in equired_field:
                        temp["项目名称"] = project_name
                    if '方案名称' in equired_field:
                        temp["方案名称"] = plan.plan_name
                    if '结果id' in equired_field:
                        temp["结果id"] = result.result_id
                    if '测试时间' in equired_field:
                        temp["测试时间"] = result.time
                    if '语料id' in equired_field:
                        temp["语料id"] = result.corpus_id
                    if '第几轮测试' in equired_field:
                        temp["第几轮测试"] = result.turn_id
                    if '测试场景' in equired_field:
                        temp["测试场景"] = result.test_scenario
                    if '语料文本' in equired_field:
                        temp["语料文本"] = result.text
                    if '预期结果' in equired_field:
                        temp["预期结果"] = result.expect_result
                    if '判断结果' in equired_field:
                        temp["判断结果"] = result.result
                    if '判断分数' in equired_field:
                        temp["判断分数"] = result.score
                    if '评估理由' in equired_field:
                        temp["评估理由"] = result.reason
                    if '车机响应识别' in equired_field:
                        temp["车机响应识别"] = result.asr_result
                    if '录音路径' in equired_field:
                        temp["录音路径"] = result.mic_audio_url
                    if '语料播出前图片' in equired_field:
                        temp["语料播出前图片"] = image_result[:-10] + "1.jpg"
                    if '语料播出后图片' in equired_field:
                        temp["语料播出后图片"] = image_result
                    if '车机识别结果' in equired_field:
                        temp["车机识别结果"] = result.ocr_result
                    if '结果标记' in equired_field:
                        signlist = decode_signs(result.signlist)
                        sign_str = " ".join(signlist)
                        temp["结果标记"] = sign_str
                    if '修改测试结果理由' in equired_field:
                        temp["修改测试结果理由"] = result.update_result_reason
                    if config.word_recognition_rate is not None:
                        temp["车机识别准确率"] = result.ocr_accuracy_rate
                    if '连续对话判断' in equired_field and i == len(multi_corpus) - 1:
                        temp["连续对话判断"] = multi_result.result
                    if '响应时间' in equired_field:
                        temp["响应时间"] = result.response_time

                    res_temp.append(temp)
    file_path = get_excel_dir()
    ret, full_file_path = save_data_to_excel(file_path, project_name, turn_id, configtype, res_temp, order)
    return ret, full_file_path


def get_excel_report(project_id, turn_id, configtype, equired_field, order):
    if turn_id == 0:
        return False, ""
    project = ProjectQueryDao.findTestProjectById(project_id)
    if project is None:
        return False, ""
    plan_list, _ = PlanQueryDao.showAllProjectPlan({"project_id": project_id})
    plan_filter_list = []
    for plan in plan_list:
        play_config = PlayConfigQueryDao.findPlayConfigById(plan.play_config_id)
        if play_config is not None and play_config.type == configtype:
            plan_filter_list.append(plan)
    ret, full_file_path = True, ""
    if configtype == "rouse" or configtype == "interaction":
        ret, full_file_path = do_excel(
            project.project_name, configtype, project_id, turn_id, plan_filter_list, equired_field, order
        )
    elif configtype == "interaction-multi":
        ret, full_file_path = do_multi_corpus_excel(
            project.project_name, configtype, project_id, turn_id, plan_filter_list, equired_field, order
        )
    elif configtype == "false-rouse":
        ret, full_file_path = do_false_rouse_excel(
            project.project_name, configtype, project_id, turn_id, plan_filter_list, equired_field, order
        )
    else:
        return False, ""

    # file_path = get_excel_dir()
    # full_file_path = ""
    # full_file_path = os.path.join(file_path, f"{project.project_name}_{turn_id}.xlsx")
    return ret, full_file_path


def statistic_data(plan_id, play_config_id, plan_type):
    data = {"play_config_id": play_config_id}
    start_config = PlayConfigQueryDao.showAllStartConfig(data)
    config = start_config[0]
    # 获取数据
    data = {"plan_id": plan_id}
    if plan_type == "interaction-multi":
        result_list, _ = ResultQueryDao.showAllMultiResult(data)
        update_data = {}
        num = 0
        interaction_success_rate_sum = 0
        can_detection_num = 0
        for result in result_list:
            num += 1
            if config.interaction_success_rate and result.result == "通过":
                interaction_success_rate_sum += 1
            if config.can_detection and result.can_result == "通过":
                can_detection_num += 1
        if config.interaction_success_rate:
            update_data["interaction_success_rate"] = interaction_success_rate_sum / num * 100
        if config.can_detection:
            update_data["can_detection"] = can_detection_num / num * 100
        PlanOperateDao.updateProjectPlan(plan_id, update_data)
        return

    result_list, _ = ResultQueryDao.showAllTestResult(data)

    num = 0
    wakeup_time_sum = 0
    wakeup_success_rate_sum = 0
    # false_wakeup_times_sum = 0
    interaction_success_rate_sum = 0
    word_recognition_rate_sum = 0
    response_time_sum = 0
    can_detection_sum = 0
    for result in result_list:
        num += 1
        if config.wakeup_time:
            wakeup_time_sum += result.response_time
        if config.wakeup_success_rate and result.result == "通过":
            wakeup_success_rate_sum += 1
        # 误唤醒
        # if config.false_wakeup_times and result.result == "通过":
        #     false_wakeup_times_sum += 1
        if config.interaction_success_rate and result.result == "通过":
            interaction_success_rate_sum += 1
        if config.word_recognition_rate:
            word_recognition_rate_sum += result.ocr_accuracy_rate
        if config.response_time:
            response_time_sum += result.response_time
        if config.can_detection and result.result == "通过":
            can_detection_sum += 1
    update_data = {}
    if config.wakeup_time and plan_type == "rouse":
        update_data["wakeup_time"] = round(wakeup_time_sum / num, 2)
    if config.wakeup_success_rate and plan_type == "rouse":
        update_data["wakeup_success_rate"] = round((wakeup_success_rate_sum / num) * 100, 2)
    # if config.false_wakeup_times:
    #     update_data = {"false_wakeup_times": false_wakeup_times_sum}
    #     PlanOperateDao.updateProjectPlan(plan_id, update_data)
    if config.interaction_success_rate and "rouse" not in plan_type:
        update_data["interaction_success_rate"] = round((interaction_success_rate_sum / num) * 100, 2)
    if config.word_recognition_rate and "rouse" not in plan_type:
        update_data["word_recognition_rate"] = round((word_recognition_rate_sum / num) * 100, 2)
    if config.response_time and "rouse" not in plan_type:
        update_data["response_time"] = round(response_time_sum / num, 2)
    if config.can_detection and "rouse" not in plan_type:
        update_data["can_detection"] = round((can_detection_sum / num) * 100, 2)
    # 更新数据库
    if len(update_data) != 0:
        PlanOperateDao.updateProjectPlan(plan_id, update_data)
    return


def get_word(project_id, report_name, report_number, testing_unit, turn_id):
    if turn_id == 0:
        return False, ""
    project = ProjectQueryDao.findTestProjectById(project_id)
    if project is None:
        return False, ""
    plan_list, _ = PlanQueryDao.showAllProjectPlan({"project_id": project_id})
    test_result_data_list = []
    rouse_test_data_list = []
    false_rouse_test_data_list = []
    single_test_data_list = []
    multi_test_data_list = []
    plan_name_info = ""
    for plan in plan_list:
        plan_name_info = plan_name_info + plan.plan_name + " "
        play_config = PlayConfigQueryDao.findPlayConfigById(plan.play_config_id)
        if play_config is None:
            continue
        statistic_data(plan.plan_id, plan.play_config_id, plan.type)
        if plan.wakeup_time is not None and play_config.type == "rouse":
            test_result_data_list.append(
                TestResultData(
                    测试方案=plan.plan_name,
                    测试类型="唤醒",
                    指标="唤醒时间",
                    测试结果=str(plan.wakeup_time),
                )
            )
        if plan.wakeup_success_rate is not None and play_config.type == "rouse":
            test_result_data_list.append(
                TestResultData(
                    测试方案=plan.plan_name,
                    测试类型="唤醒",
                    指标="唤醒成功率",
                    测试结果=str(plan.wakeup_success_rate),
                )
            )
        if plan.false_wakeup_times is not None and play_config.type == "false-rouse":
            test_result_data_list.append(
                TestResultData(
                    测试方案=plan.plan_name,
                    测试类型="误唤醒",
                    指标="误唤醒次数",
                    测试结果=str(plan.false_wakeup_times),
                )
            )
        if plan.word_recognition_rate is not None and "interaction" in play_config.type:
            test_result_data_list.append(
                TestResultData(
                    测试方案=plan.plan_name,
                    测试类型="单次对话",
                    指标="字识别率",
                    测试结果=str(plan.word_recognition_rate),
                )
            )
        if plan.response_time is not None and "interaction" in play_config.type:
            test_result_data_list.append(
                TestResultData(
                    测试方案=plan.plan_name,
                    测试类型="单次对话",
                    指标="响应时间",
                    测试结果=str(plan.response_time),
                )
            )
        if plan.interaction_success_rate is not None:
            if play_config is not None and play_config.type == "interaction-multi":
                test_result_data_list.append(
                    TestResultData(
                        测试方案=plan.plan_name,
                        测试类型="连续对话",
                        指标="交互成功率",
                        测试结果=str(plan.interaction_success_rate),
                    )
                )
            elif play_config is not None and play_config.type == "interaction":
                test_result_data_list.append(
                    TestResultData(
                        测试方案=plan.plan_name,
                        测试类型="单次对话",
                        指标="交互成功率",
                        测试结果=str(plan.interaction_success_rate),
                    )
                )
        data = {"project_id": project_id, "plan_id": plan.plan_id}
        result_list, _ = ResultQueryDao.showAllTestResult(data)
        if play_config.type == "rouse":
            for result in result_list:
                rouse_test_data_list.append(
                    RouseTestData(
                        唤醒时间=result.time.strftime("%Y-%m-%d %H:%M:%S"),
                        语料文本=result.text,
                        判断结果=result.result,
                        响应时间=str(result.response_time),
                        车机响应=result.asr_result,
                    )
                )
        elif play_config.type == "false-rouse":
            false_result_list, _ = ResultQueryDao.showAllFalseRouseResult(data)
            for result in false_result_list:
                false_rouse_test_data_list.append(
                    FalseRouseTestData(
                        误唤醒时刻=result.time,
                        唤醒响应=result.asr_result,
                        # 图片路径=result.result,
                        # 录音路径=result.response_time,
                    )
                )
        elif play_config.type == "interaction":
            for result in result_list:
                single_test_data_list.append(
                    SingleTestData(
                        时间=result.time.strftime("%Y-%m-%d %H:%M:%S"),
                        语料文本=result.text,
                        判断结果=result.result,
                        响应时间=str(result.response_time),
                        车机响应=result.asr_result,
                        预期结果=result.expect_result,
                        字识别率=str(result.ocr_accuracy_rate),
                        # ocr图片路径=result.ocr_pic_url,
                        # 结果图片路径=result.image,
                        # 录音路径=result.mic_audio_url,
                    )
                )
        elif play_config.type == "interaction-multi":
            for result in result_list:
                multi_test_data_list.append(
                    MultiTestData(
                        时间=result.time.strftime("%Y-%m-%d %H:%M:%S"),
                        语料文本=result.text,
                        判断结果=result.result,
                        响应时间=str(result.response_time),
                        车机响应=result.asr_result,
                        预期结果=result.expect_result,
                        字识别率=str(result.ocr_accuracy_rate),
                        # ocr图片路径=result.ocr_pic_url,
                        # 结果图片路径=result.image,
                        # 录音路径=result.mic_audio_url,
                    )
                )

    report_data = Report(
        report_id=report_number,
        report_name=report_name,
        product_name=project.test_object_name,
        product_model=project.test_object_version,
        entrust_company=testing_unit,
        project_name=project.project_name,
        sample_name=project.test_object_name,
        type_specification=project.test_object_version,
        test_item=plan_name_info,
        test_result_data=test_result_data_list,
        rouse_test_data=rouse_test_data_list,
        false_rouse_test_data=false_rouse_test_data_list,
        single_test_data=single_test_data_list,
        multi_test_data=multi_test_data_list,
    )

    file_path = get_excel_dir()
    demo_path = os.path.join(file_path, "demo")
    demo_path = os.path.join(demo_path, "report_demo.docx")
    file_path = get_word_report(demo_path, report_data)

    return True, file_path


def de_weight_corpus(corpus_columns):
    result_corpus = []
    seen = set()
    for i in corpus_columns:
        if i["corpus"] not in seen:
            result_corpus.append(i)
            seen.add(i["corpus"])
    return result_corpus


def result_to_corpus(result_ids, plan_type):
    corpus_list = []
    result_list = []
    for result_id in result_ids:
        if plan_type == "interaction-multi":
            r = ResultQueryDao.findMultiResultById(result_id)
        else:
            r = ResultQueryDao.findTestResultById(result_id)
        result_list.append(r)
        # corpus_list.append(r.corpus_id)
    # 从早到晚  如果需要降序排序（从晚到早），可以添加 reverse=True
    # sorted_results = sorted(result_list, key=lambda x: x.time, reverse=True)
    sorted_results = sorted(result_list, key=lambda x: x.time)
    res = []
    if plan_type == "interaction-multi":
        for r in sorted_results:
            temp = {"corpus": r.multicorpus_id, "result": r.multi_result_id}
            corpus_list.append(temp)
        res = de_weight_corpus(corpus_list)
        return res
    for r in sorted_results:
        temp = {"corpus": r.corpus_id, "result": r.result_id}
        corpus_list.append(temp)
    res = de_weight_corpus(corpus_list)
    return res


def retest_plan_create(project_id, plan_id, turn_id, retest_plan_name, result_columns):
    create_plan_data = {"project_id": project_id, "plan_name": retest_plan_name}
    rerun_plan = create_test_plan(create_plan_data, plan_id, turn_id)

    plan = PlanQueryDao.findProjectPlanById(plan_id)
    construct_data = {"plan_id": rerun_plan.plan_id, "play_config_id": plan.play_config_id}
    result_corpus_list = result_to_corpus(result_columns, plan.type)
    if plan.type == "rouse":
        construct_data["rouseCorpusList"] = result_corpus_list
    elif plan.type == "interaction" or plan.type == "interaction-multi":
        get_rouse_tree = {"plan_id": plan_id}
        rouse_list = PlanQueryDao.showAllRCorpusTree(get_rouse_tree)
        corpus_list = []
        for rouse_tree in rouse_list:
            corpus = RCorpusTree(plan_id=rerun_plan.plan_id, corpus_id=rouse_tree.corpus_id)
            corpus_list.append(corpus)
        PlanOperateDao.saveRCorpusTreeList(corpus_list)
        construct_data["testCorpusList"] = result_corpus_list

    save_corpuslist_byplanid(construct_data)
    return True


def add_corpuslist_byplanid(data):
    if "plan_id" not in data or data["plan_id"] == "plan-1":
        return {}
    if "testCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteTCorpusTree(data["plan_id"])
        for corpus_id in data["testCorpusList"]:
            if isinstance(corpus_id, str):
                corpus = TCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            else:
                corpus = TCorpusTree(
                    plan_id=data["plan_id"], corpus_id=corpus_id["corpus"], result_id=corpus_id["result"]
                )
            corpus_list.append(corpus)
        PlanOperateDao.saveTCorpusTreeList(corpus_list)
    if "rouseCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteRCorpusTree(data["plan_id"])
        for corpus_id in data["rouseCorpusList"]:
            if isinstance(corpus_id, str):
                corpus = RCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            else:
                corpus = RCorpusTree(
                    plan_id=data["plan_id"], corpus_id=corpus_id["corpus"], result_id=corpus_id["result"]
                )
            corpus_list.append(corpus)
        PlanOperateDao.saveRCorpusTreeList(corpus_list)
    if "disturbCorpusList" in data:
        corpus_list = []
        PlanOperateDao.deleteDCorpusTree(data["plan_id"])
        for corpus_id in data["disturbCorpusList"]:
            corpus = DCorpusTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            corpus_list.append(corpus)
        PlanOperateDao.saveDCorpusTreeList(corpus_list)
    if "backgroundNoiseList" in data:
        corpus_list = []
        PlanOperateDao.deleteBNoiseTree(data["plan_id"])
        for corpus_id in data["backgroundNoiseList"]:
            corpus = BNoiseTree(plan_id=data["plan_id"], corpus_id=corpus_id)
            corpus_list.append(corpus)
        PlanOperateDao.saveBNoiseTreeList(corpus_list)


def add_to_retestplan(project_id, plan_id, result_ids):
    plan = PlanQueryDao.findProjectPlanById(plan_id)
    if plan is None:
        return False
    construct_data = {"plan_id": plan.plan_id}
    result_temp = []
    if plan.type == "rouse":
        result_temp = PlanQueryDao.findAllRCorpusByTreeId(plan_id, True)
    elif plan.type == "interaction" or plan.type == "interaction-multi":
        result_temp = PlanQueryDao.findAllTCorpusByTreeId(plan_id, True)
    for i in result_temp:
        result_ids.append(i.result_id)
    corpus_ids = result_to_corpus(result_ids, plan.type)
    if plan.type == "rouse":
        construct_data["rouseCorpusList"] = corpus_ids
    elif plan.type == "interaction" or plan.type == "interaction-multi":
        construct_data["testCorpusList"] = corpus_ids

    add_corpuslist_byplanid(construct_data)
    return True


def result_remove_sign(project_id, plan_id, turn_id, result_id, sign):
    result = ResultQueryDao.findTestResultById(result_id)
    if sign == "人工复核修正":
        return "This tag cannot be removed"
    sign_label = decode_signs_word(sign)
    if sign_label is None:
        return "No valid sign value found"
    signlist = encode_signs(result.signlist, sign_label)
    update_data = {"signlist": signlist}
    ResultOperateDao.updateTestResult(result_id, update_data)
    return None


def update_multi_result(multi_result, new_result, reason):
    update_data = {"result": new_result}
    ResultOperateDao.updateMultiResult(multi_result.id, update_data)
    update_data["update_result_reason"] = reason
    multi_corpus = CorpusQueryDao.findMultiCorpusById(multi_result.multicorpus_id)
    for corpus in multi_corpus:
        data = {
            "project_id": multi_result.project_id,
            "turn_id": multi_result.turn_id,
            "circle": multi_result.circle,
            "plan_id": multi_result.plan_id,
            "corpus_id": corpus.testcorpus_id,
        }
        result_list, _ = ResultQueryDao.showAllTestResult(data)
        for result_obj in result_list:
            result = ResultQueryDao.findTestResultById(result_obj.result_id)
            sign_label = decode_signs(result.signlist)
            if "人工复核修正" in sign_label:
                return
            else:
                sign_list = encode_signs(result.signlist, "manual_review_correction")
                update_data["signlist"] = sign_list
            ResultOperateDao.updateTestResult(result_obj.result_id, update_data)


def multi_test_result_update(update_id_list, result_id_list, multi_id, new_result, reason):
    if len(update_id_list) == 0 or not multi_id:
        return "param error."
    if not reason:
        reason = ""
    update_data = {"result": new_result, "update_result_reason": reason}

    result_list = []
    for result_id in result_id_list:
        test_result = ResultQueryDao.findTestResultById(result_id)
        result = test_result.result
        if result_id in update_id_list:
            result = new_result
            sign_label = decode_signs(test_result.signlist)
            if "人工复核修正" not in sign_label:
                sign_list = encode_signs(test_result.signlist, "manual_review_correction")
                update_data["signlist"] = sign_list
            ResultOperateDao.updateTestResult(result_id, update_data)
        result_list.append(result)
    if all(x == "通过" for x in result_list):
        ResultOperateDao.updateMultiResultByMid(multi_id, {"result": "通过"})
    else:
        ResultOperateDao.updateMultiResultByMid(multi_id, {"result": "不通过"})


def test_result_update_result(result_id, new_result, reason):
    if not reason:
        reason = ""
    update_data = {"result": new_result, "update_result_reason": reason}
    result = ResultQueryDao.findTestResultById(result_id)
    sign_label = decode_signs(result.signlist)
    if "人工复核修正" not in sign_label:
        sign_list = encode_signs(result.signlist, "manual_review_correction")
        update_data["signlist"] = sign_list
    ResultOperateDao.updateTestResult(result_id, update_data)
    return


def test_result_update_ocr_accuracy_rate(result_id, ocr_accuracy_rate):
    if ocr_accuracy_rate < 0 or ocr_accuracy_rate > 1:
        return
    update_data = {"ocr_accuracy_rate": ocr_accuracy_rate}
    result = ResultQueryDao.findTestResultById(result_id)
    sign_label = decode_signs(result.signlist)
    if "字识别准确率异常" in sign_label:
        if result.ocr_accuracy_rate < 0.8 <= ocr_accuracy_rate:
            sign_list = encode_signs(result.signlist, "word_accuracy_abnormal")
            update_data["signlist"] = sign_list
    else:
        if result.ocr_accuracy_rate >= 0.8 > ocr_accuracy_rate:
            sign_list = encode_signs(result.signlist, "word_accuracy_abnormal")
            update_data["signlist"] = sign_list
    ResultOperateDao.updateTestResult(result_id, update_data)
    return


def get_signal_data(result_id, signal_name):
    result = ResultQueryDao.findTestResultById(result_id)
    if os.path.exists(result.can_data_url):
        res = find_signal_data(result.can_data_url, signal_name)
        return res
    return []


def get_update_result_reason(result_id):
    result = ResultQueryDao.findTestResultById(result_id)
    if result:
        return result.update_result_reason
