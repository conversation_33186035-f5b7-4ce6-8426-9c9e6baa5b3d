
class ChannelObj:
    ifdescs = None
    configed_channel = []
    enable_channel = []

    def set_value(self, value):
        self.ifdescs = value

    def get_value(self):
        return self.ifdescs

    def set_configed_channel(self, obj):
        self.configed_channel.append(obj)

    def del_configed_channel(self, id):
        for channel_obj, item in enumerate(self.configed_channel):
            if item["channel_id"] == id:
                self.configed_channel.pop(channel_obj)
    
    def get_configed_channel(self):
        return self.configed_channel
    
    def set_enable_channel(self, obj):
        self.enable_channel.append(obj)

    def del_enable_channel(self, id):
        for channel_obj, item in enumerate(self.enable_channel):
            if item["channel_id"] == id:
                self.enable_channel.pop(channel_obj)
    
    def get_enable_channel(self):
        return self.enable_channel

global_channel = ChannelObj()

# 常用 波特率配置
bit_table = {
    50:   [200, 7, 6, 2, 2, 16, 0.875],
    100:  [100, 7, 6, 2, 2, 16, 0.875],
    125:  [80,  7, 6, 2, 2, 16, 0.875],
    200:  [50,  7, 6, 2, 2, 16, 0.875],
    250:  [40,  7, 6, 2, 2, 16, 0.875],
    500:  [16,  9, 6, 4, 4, 20, 0.8],
    800:  [10,  9, 6, 4, 4, 20, 0.8],
    1000: [10,  7, 4, 4, 4, 16, 0.75],
    2000: [5,   7, 4, 4, 4, 16, 0.75],
}

import queue
can_data_queue = queue.Queue()