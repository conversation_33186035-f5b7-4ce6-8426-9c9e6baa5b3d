from app.utils.utils import delete_file_by_project_id
from app.dao.project_dao import ProjectOperateDao, ProjectQueryDao
from app.dao.plan_dao import PlanQueryDao
from app.service.plan import delete_test_plan
from app.dao.models.sqlite_gen import TestProject
from app.dao.result_dao import ResultQueryDao, ResultOperateDao
from app.dao.base_dao import find_table_id
import os


def test_project_create(data):
    id = find_table_id("test_project_id")
    project = TestProject(
        project_id="project_" + str(id),
        project_name=data["project_name"],
        project_code=data["project_code"],
        description=data["description"],
        test_object_name=data["test_object_name"],
        test_object_version=data["test_object_version"],
    )
    save_project = ProjectOperateDao.saveTestProject(project)
    return save_project


def test_project_update(data):
    updated_data = {
        "project_name": data["project_name"],
        "project_code": data["project_code"],
        "description": data["description"],
        "test_object_name": data["test_object_name"],
        "test_object_version": data["test_object_version"],
    }

    update_project = ProjectOperateDao.updateTestProject(
        data["project_id"], updated_data
    )
    return update_project


def test_project_delete(data):
    plan_list, _ = PlanQueryDao.showAllProjectPlan(data)
    for plan in plan_list:
        d_data = {"plan_id": plan.plan_id}
        res = delete_test_plan(d_data)
        if res["status"] == "error":
            return res["error_msg"]
    ResultOperateDao.deleteManyTestResult(data)
    delete_file_by_project_id(data["project_id"])
    # 删除项目
    ProjectOperateDao.deleteTestProject(data["project_id"])


def test_project_list(data):
    project_list, total = ProjectQueryDao.showAllTestProject(data)
    res = []
    for project in project_list:
        temp = {"project_id": project.project_id, "project_name": project.project_name,
                "project_code": project.project_code, "description": project.description,
                "test_object_name": project.test_object_name, "test_object_version": project.test_object_version}

        res.append(temp)
    return res, total


def test_result_check(result_id, result):
    res = ResultQueryDao.findTestResultById(result_id)
    if res is None:
        return " No result_id was found available "
    updata_data = {"result": result}
    ResultOperateDao.updateTestResult(result_id, updata_data)
    return 0
