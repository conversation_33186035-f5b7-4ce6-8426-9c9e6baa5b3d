from fastapi import FastAP<PERSON>
from starlette.staticfiles import StaticFiles
from app.controller import RegisterRouterList
from app.dao.base_dao import init_db
from app.middleware.log import RequestIdMiddleware, init_log
from app.service.llm_judge import llm_judge_result
from app.service.data_upload import upload_data_thread
from app.service.plan import execute_queue
import threading


def init(server: FastAPI):
    # 挂载静态资源目录
    server.mount("/static", StaticFiles(directory="video"), name="static")
    server.mount("/mic_static", StaticFiles(directory="mic_audio"), name="mic_static")
    server.mount("/audio", StaticFiles(directory="audio"), name="audio")
    server.mount("/photo", StaticFiles(directory="photo"), name="photo")
    server.mount("/video", StaticFiles(directory="video"), name="video")

    # 注册自定义错误处理器
    # errors.registerCustomErrorHandle(server)
    # 初始化日志
    init_log()
    # 注册中间件
    # middleware.registerMiddlewareHandle(server)
    server.add_middleware(RequestIdMiddleware)
    # 初始化数据库
    init_db()
    # 创建并启动 数据测试获取线程
    func0_thread = threading.Thread(target=execute_queue, daemon=True)
    func0_thread.start()

    # 创建并启动 大模型判断线程
    func1_thread = threading.Thread(target=llm_judge_result, daemon=True)
    func1_thread.start()

    # 创建并启动 数据上传线程
    func2_thread = threading.Thread(target=upload_data_thread, daemon=True)
    func2_thread.start()

    # 加载路由
    for item in RegisterRouterList:
        server.include_router(item.router)