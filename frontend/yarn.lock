# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"7zip-bin@npm:~5.2.0":
  version: 5.2.0
  resolution: "7zip-bin@npm:5.2.0"
  checksum: 10c0/7f6c69b4cb10c4060fb8fda258ae2ab88d30516b5a52941efa0e2cbd9ce362ab7d8d568549cd85e9f125c1c68f95c7bb076cc314c2f3c0cb306d3b638080c2ce
  languageName: node
  linkType: hard

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: 10c0/53c2b231a61a46792b39a0d43bc4f4f776bb4542aa57ee04930676802e5501282c2fc8aac14e4cd1f1120ff8b52616b6ff5ab539ad30aa2277d726444b71619f
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0, @ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@antfu/install-pkg@npm:^0.1.1":
  version: 0.1.1
  resolution: "@antfu/install-pkg@npm:0.1.1"
  dependencies:
    execa: "npm:^5.1.1"
    find-up: "npm:^5.0.0"
  checksum: 10c0/ae3116cc0918765ad356901b9c8825340be27deac03eb4c8969377eab9731a3b41d96e920fa0b08adf91fba27a808d08c68852b110775ff79ba40481422cc8ba
  languageName: node
  linkType: hard

"@antfu/utils@npm:^0.7.5, @antfu/utils@npm:^0.7.7":
  version: 0.7.7
  resolution: "@antfu/utils@npm:0.7.7"
  checksum: 10c0/7ca5db419f3cb6ceabf3ea254ce00ff0eab307585838c074eef40b0f948a8eade0cbad21622a62f3dea83e1cf88accf561be82bc2895e47c62cef01537b92be9
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.23.5, @babel/code-frame@npm:^7.24.1, @babel/code-frame@npm:^7.24.2":
  version: 7.24.2
  resolution: "@babel/code-frame@npm:7.24.2"
  dependencies:
    "@babel/highlight": "npm:^7.24.2"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/d1d4cba89475ab6aab7a88242e1fd73b15ecb9f30c109b69752956434d10a26a52cbd37727c4eca104b6d45227bd1dfce39a6a6f4a14c9b2f07f871e968cf406
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.23.5":
  version: 7.24.4
  resolution: "@babel/compat-data@npm:7.24.4"
  checksum: 10c0/9cd8a9cd28a5ca6db5d0e27417d609f95a8762b655e8c9c97fd2de08997043ae99f0139007083c5e607601c6122e8432c85fe391731b19bf26ad458fa0c60dd3
  languageName: node
  linkType: hard

"@babel/core@npm:^7.23.5, @babel/core@npm:^7.24.3":
  version: 7.24.4
  resolution: "@babel/core@npm:7.24.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.24.2"
    "@babel/generator": "npm:^7.24.4"
    "@babel/helper-compilation-targets": "npm:^7.23.6"
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helpers": "npm:^7.24.4"
    "@babel/parser": "npm:^7.24.4"
    "@babel/template": "npm:^7.24.0"
    "@babel/traverse": "npm:^7.24.1"
    "@babel/types": "npm:^7.24.0"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/fc136966583e64d6f84f4a676368de6ab4583aa87f867186068655b30ef67f21f8e65a88c6d446a7efd219ad7ffb9185c82e8a90183ee033f6f47b5026641e16
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.24.1, @babel/generator@npm:^7.24.4":
  version: 7.24.4
  resolution: "@babel/generator@npm:7.24.4"
  dependencies:
    "@babel/types": "npm:^7.24.0"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^2.5.1"
  checksum: 10c0/67a1b2f7cc985aaaa11b01e8ddd4fffa4f285837bc7a209738eb8203aa34bdafeb8507ed75fd883ddbabd641a036ca0a8d984e760f28ad4a9d60bff29d0a60bb
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/5a80dc364ddda26b334bbbc0f6426cab647381555ef7d0cd32eb284e35b867c012ce6ce7d52a64672ed71383099c99d32765b3d260626527bb0e3470b0f58e45
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.23.6":
  version: 7.23.6
  resolution: "@babel/helper-compilation-targets@npm:7.23.6"
  dependencies:
    "@babel/compat-data": "npm:^7.23.5"
    "@babel/helper-validator-option": "npm:^7.23.5"
    browserslist: "npm:^4.22.2"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/ba38506d11185f48b79abf439462ece271d3eead1673dd8814519c8c903c708523428806f05f2ec5efd0c56e4e278698fac967e5a4b5ee842c32415da54bc6fa
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.24.4":
  version: 7.24.4
  resolution: "@babel/helper-create-class-features-plugin@npm:7.24.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-member-expression-to-functions": "npm:^7.23.0"
    "@babel/helper-optimise-call-expression": "npm:^7.22.5"
    "@babel/helper-replace-supers": "npm:^7.24.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/6ebb38375dcd44c79f40008c2de4d023376cf436c135439f15c9c54603c2d6a8ada39b2e07be545da684d9e40b602a0cb0d1670f3877d056deb5f0d786c4bf86
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-environment-visitor@npm:7.22.20"
  checksum: 10c0/e762c2d8f5d423af89bd7ae9abe35bd4836d2eb401af868a63bbb63220c513c783e25ef001019418560b3fdc6d9a6fb67e6c0b650bcdeb3a2ac44b5c3d2bdd94
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-function-name@npm:7.23.0"
  dependencies:
    "@babel/template": "npm:^7.22.15"
    "@babel/types": "npm:^7.23.0"
  checksum: 10c0/d771dd1f3222b120518176733c52b7cadac1c256ff49b1889dbbe5e3fed81db855b8cc4e40d949c9d3eae0e795e8229c1c8c24c0e83f27cfa6ee3766696c6428
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/60a3077f756a1cd9f14eb89f0037f487d81ede2b7cfe652ea6869cd4ec4c782b0fb1de01b8494b9a2d2050e3d154d7d5ad3be24806790acfb8cbe2073bf1e208
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-member-expression-to-functions@npm:7.23.0"
  dependencies:
    "@babel/types": "npm:^7.23.0"
  checksum: 10c0/b810daddf093ffd0802f1429052349ed9ea08ef7d0c56da34ffbcdecbdafac86f95bdea2fe30e0e0e629febc7dd41b56cb5eacc10d1a44336d37b755dac31fa4
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.15":
  version: 7.24.3
  resolution: "@babel/helper-module-imports@npm:7.24.3"
  dependencies:
    "@babel/types": "npm:^7.24.0"
  checksum: 10c0/052c188adcd100f5e8b6ff0c9643ddaabc58b6700d3bbbc26804141ad68375a9f97d9d173658d373d31853019e65f62610239e3295cdd58e573bdcb2fded188d
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/helper-module-transforms@npm:7.23.3"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/helper-simple-access": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/211e1399d0c4993671e8e5c2b25383f08bee40004ace5404ed4065f0e9258cc85d99c1b82fd456c030ce5cfd4d8f310355b54ef35de9924eabfc3dff1331d946
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/31b41a764fc3c585196cf5b776b70cf4705c132e4ce9723f39871f215f2ddbfb2e28a62f9917610f67c8216c1080482b9b05f65dd195dae2a52cef461f2ac7b8
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.24.0":
  version: 7.24.0
  resolution: "@babel/helper-plugin-utils@npm:7.24.0"
  checksum: 10c0/90f41bd1b4dfe7226b1d33a4bb745844c5c63e400f9e4e8bf9103a7ceddd7d425d65333b564d9daba3cebd105985764d51b4bd4c95822b97c2e3ac1201a8a5da
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/helper-replace-supers@npm:7.24.1"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-member-expression-to-functions": "npm:^7.23.0"
    "@babel/helper-optimise-call-expression": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/d39a3df7892b7c3c0e307fb229646168a9bd35e26a72080c2530729322600e8cff5f738f44a14860a2358faffa741b6a6a0d6749f113387b03ddbfa0ec10e1a0
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/f0cf81a30ba3d09a625fd50e5a9069e575c5b6719234e04ee74247057f8104beca89ed03e9217b6e9b0493434cedc18c5ecca4cea6244990836f1f893e140369
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/ab7fa2aa709ab49bb8cd86515a1e715a3108c4bb9a616965ba76b43dc346dee66d1004ccf4d222b596b6224e43e04cbc5c3a34459501b388451f8c589fbc3691
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 10c0/d83e4b623eaa9622c267d3c83583b72f3aac567dc393dda18e559d79187961cb29ae9c57b2664137fc3d19508370b12ec6a81d28af73a50e0846819cb21c6e44
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.23.4":
  version: 7.24.1
  resolution: "@babel/helper-string-parser@npm:7.24.1"
  checksum: 10c0/2f9bfcf8d2f9f083785df0501dbab92770111ece2f90d120352fda6dd2a7d47db11b807d111e6f32aa1ba6d763fe2dc6603d153068d672a5d0ad33ca802632b2
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-validator-identifier@npm:7.22.20"
  checksum: 10c0/dcad63db345fb110e032de46c3688384b0008a42a4845180ce7cd62b1a9c0507a1bed727c4d1060ed1a03ae57b4d918570259f81724aaac1a5b776056f37504e
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/helper-validator-option@npm:7.23.5"
  checksum: 10c0/af45d5c0defb292ba6fd38979e8f13d7da63f9623d8ab9ededc394f67eb45857d2601278d151ae9affb6e03d5d608485806cd45af08b4468a0515cf506510e94
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.24.4":
  version: 7.24.4
  resolution: "@babel/helpers@npm:7.24.4"
  dependencies:
    "@babel/template": "npm:^7.24.0"
    "@babel/traverse": "npm:^7.24.1"
    "@babel/types": "npm:^7.24.0"
  checksum: 10c0/747ef62b7fe87de31a2f3c19ff337a86cbb79be2f6c18af63133b614ab5a8f6da5b06ae4b06fb0e71271cb6a27efec6f8b6c9f44c60b8a18777832dc7929e6c5
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.24.2":
  version: 7.24.2
  resolution: "@babel/highlight@npm:7.24.2"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/98ce00321daedeed33a4ed9362dc089a70375ff1b3b91228b9f05e6591d387a81a8cba68886e207861b8871efa0bc997ceabdd9c90f6cce3ee1b2f7f941b42db
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.9, @babel/parser@npm:^7.24.0, @babel/parser@npm:^7.24.1, @babel/parser@npm:^7.24.4":
  version: 7.24.4
  resolution: "@babel/parser@npm:7.24.4"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/8381e1efead5069cb7ed2abc3a583f4a86289b2f376c75cecc69f59a8eb36df18274b1886cecf2f97a6a0dff5334b27330f58535be9b3e4e26102cc50e12eac8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.24.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.24.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6cec76fbfe6ca81c9345c2904d8d9a8a0df222f9269f0962ed6eb2eb8f3f10c2f15e993d1ef09dbaf97726bf1792b5851cf5bd9a769f966a19448df6be95d19a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.24.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.24.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7a81e277dcfe3138847e8e5944e02a42ff3c2e864aea6f33fd9b70d1556d12b0e70f0d56cc1985d353c91bcbf8fe163e6cc17418da21129b7f7f1d8b9ac00c93
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.23.3":
  version: 7.24.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.24.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.24.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f44bfacf087dc21b422bab99f4e9344ee7b695b05c947dacae66de05c723ab9d91800be7edc1fa016185e8c819f3aca2b4a5f66d8a4d1e47d9bad80b8fa55b8e
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.24.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helper-plugin-utils": "npm:^7.24.0"
    "@babel/helper-simple-access": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/efb3ea2047604a7eb44a9289311ebb29842fe6510ff8b66a77a60440448c65e1312a60dc48191ed98246bdbd163b5b6f3348a0669bcc0e3809e69c7c776b20fa
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.24.1":
  version: 7.24.4
  resolution: "@babel/plugin-transform-typescript@npm:7.24.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-create-class-features-plugin": "npm:^7.24.4"
    "@babel/helper-plugin-utils": "npm:^7.24.0"
    "@babel/plugin-syntax-typescript": "npm:^7.24.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/fa6625046f219cdc75061025c8031ada75ef631b137f1442e3d0054ba4e63548eb12cf55e2e1f442c889aa5fdd76d0d0b7904fdf812ce4c38748446227acc798
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/preset-typescript@npm:7.24.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.24.0"
    "@babel/helper-validator-option": "npm:^7.23.5"
    "@babel/plugin-syntax-jsx": "npm:^7.24.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.24.1"
    "@babel/plugin-transform-typescript": "npm:^7.24.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0033dc6fbc898ed0d8017c83a2dd5e095c82909e2f83e48cf9f305e3e9287148758c179ad90f27912cf98ca68bfec3643c57c70c0ca34d3a6c50dc8243aef406
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.10.1, @babel/runtime@npm:^7.11.1, @babel/runtime@npm:^7.11.2, @babel/runtime@npm:^7.18.0, @babel/runtime@npm:^7.18.3, @babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.20.7, @babel/runtime@npm:^7.23.2, @babel/runtime@npm:^7.23.6, @babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.8.7":
  version: 7.24.4
  resolution: "@babel/runtime@npm:7.24.4"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/785aff96a3aa8ff97f90958e1e8a7b1d47f793b204b47c6455eaadc3f694f48c97cd5c0a921fe3596d818e71f18106610a164fb0f1c71fd68c622a58269d537c
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.15, @babel/template@npm:^7.24.0":
  version: 7.24.0
  resolution: "@babel/template@npm:7.24.0"
  dependencies:
    "@babel/code-frame": "npm:^7.23.5"
    "@babel/parser": "npm:^7.24.0"
    "@babel/types": "npm:^7.24.0"
  checksum: 10c0/9d3dd8d22fe1c36bc3bdef6118af1f4b030aaf6d7d2619f5da203efa818a2185d717523486c111de8d99a8649ddf4bbf6b2a7a64962d8411cf6a8fa89f010e54
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.24.1":
  version: 7.24.1
  resolution: "@babel/traverse@npm:7.24.1"
  dependencies:
    "@babel/code-frame": "npm:^7.24.1"
    "@babel/generator": "npm:^7.24.1"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-hoist-variables": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/parser": "npm:^7.24.1"
    "@babel/types": "npm:^7.24.0"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/c087b918f6823776537ba246136c70e7ce0719fc05361ebcbfd16f4e6f2f6f1f8f4f9167f1d9b675f27d12074839605189cc9d689de20b89a85e7c140f23daab
  languageName: node
  linkType: hard

"@babel/types@npm:^7.22.5, @babel/types@npm:^7.23.0, @babel/types@npm:^7.24.0, @babel/types@npm:^7.8.3":
  version: 7.24.0
  resolution: "@babel/types@npm:7.24.0"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.23.4"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    to-fast-properties: "npm:^2.0.0"
  checksum: 10c0/777a0bb5dbe038ca4c905fdafb1cdb6bdd10fe9d63ce13eca0bd91909363cbad554a53dc1f902004b78c1dcbc742056f877f2c99eeedff647333b1fadf51235d
  languageName: node
  linkType: hard

"@ctrl/tinycolor@npm:^3.4.1, @ctrl/tinycolor@npm:^3.6.1":
  version: 3.6.1
  resolution: "@ctrl/tinycolor@npm:3.6.1"
  checksum: 10c0/444d81612cd8c5c802a3d1253df83d5f77d3db87f351861655683a4743990e6b38976bf2e4129591c5a258607b63574b3c7bed702cf6a0eb7912222edf4570e9
  languageName: node
  linkType: hard

"@develar/schema-utils@npm:~2.6.5":
  version: 2.6.5
  resolution: "@develar/schema-utils@npm:2.6.5"
  dependencies:
    ajv: "npm:^6.12.0"
    ajv-keywords: "npm:^3.4.1"
  checksum: 10c0/7c6075ce6742dd5c89b3cebf81351ec1d73dafc7c3409748860e4f8262fb26ffe6d998c5baab4eca579cd436e7c6c12c615fe89819c19484a22d25b3e6825cb5
  languageName: node
  linkType: hard

"@electron-toolkit/eslint-config-ts@npm:^1.0.1":
  version: 1.0.1
  resolution: "@electron-toolkit/eslint-config-ts@npm:1.0.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:^6.14.0"
    "@typescript-eslint/parser": "npm:^6.14.0"
  peerDependencies:
    eslint: ">=8.0.0"
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/ba5f56db8168acfce18b6f5414bad339d3d7ca32e73b527167bcab58943aeb03a5a3ec1c0a787ca70af281501b0acfed259120a87cd552d31affc223aea32487
  languageName: node
  linkType: hard

"@electron-toolkit/eslint-config@npm:^1.0.2":
  version: 1.0.2
  resolution: "@electron-toolkit/eslint-config@npm:1.0.2"
  peerDependencies:
    eslint: ">= 8.0.0"
  checksum: 10c0/078d67a8898c26100fd85568b5416521066434279c14525802ee7456c7fb3433177e5d531a62ea2d8ec9a63e61f86f17dd492b4d1162651225db5c4dafe67e45
  languageName: node
  linkType: hard

"@electron-toolkit/preload@npm:^3.0.0":
  version: 3.0.1
  resolution: "@electron-toolkit/preload@npm:3.0.1"
  peerDependencies:
    electron: ">=13.0.0"
  checksum: 10c0/7ebc3738dbdbc7295f37fa5cfc4dbc0b60576405c04a017aa121f6b3377674fc38871c030f0506cf9c7dc61c96f944576731dd8ae1aac471b8b63f4651b9451f
  languageName: node
  linkType: hard

"@electron-toolkit/tsconfig@npm:^1.0.1":
  version: 1.0.1
  resolution: "@electron-toolkit/tsconfig@npm:1.0.1"
  peerDependencies:
    "@types/node": "*"
  checksum: 10c0/6e8670998abe3a725d0d99f2466d10b9d36026035efa8872f6b91000f04b748caad9922722e2189560ef0b0489aed6d6beb4099a2ae417606cff37f6ffc3eb46
  languageName: node
  linkType: hard

"@electron-toolkit/utils@npm:^3.0.0":
  version: 3.0.0
  resolution: "@electron-toolkit/utils@npm:3.0.0"
  peerDependencies:
    electron: ">=13.0.0"
  checksum: 10c0/b9cb3da36fec97b1a8e2344283da8a78c77d7fb27d09bcf60ca0947db246460a8d05f12cd25bc1c764211443caa58684656e197f9fa3f587a9a5589ec9cf74de
  languageName: node
  linkType: hard

"@electron/asar@npm:^3.2.1":
  version: 3.2.9
  resolution: "@electron/asar@npm:3.2.9"
  dependencies:
    commander: "npm:^5.0.0"
    glob: "npm:^7.1.6"
    minimatch: "npm:^3.0.4"
  bin:
    asar: bin/asar.js
  checksum: 10c0/80ab9d01160c6114571f327a59a4b594fbd65463ea771acee2e989ce5cce5fb771abb7003314e59ddd22127fdf1ab774d65bd13e2e2c7b93fcab1146378f7b59
  languageName: node
  linkType: hard

"@electron/get@npm:^2.0.0":
  version: 2.0.3
  resolution: "@electron/get@npm:2.0.3"
  dependencies:
    debug: "npm:^4.1.1"
    env-paths: "npm:^2.2.0"
    fs-extra: "npm:^8.1.0"
    global-agent: "npm:^3.0.0"
    got: "npm:^11.8.5"
    progress: "npm:^2.0.3"
    semver: "npm:^6.2.0"
    sumchecker: "npm:^3.0.1"
  dependenciesMeta:
    global-agent:
      optional: true
  checksum: 10c0/148957d531bac50c29541515f2483c3e5c9c6ba9f0269a5d536540d2b8d849188a89588f18901f3a84c2b4fd376d1e0c5ea2159eb2d17bda68558f57df19015e
  languageName: node
  linkType: hard

"@electron/notarize@npm:2.2.1":
  version: 2.2.1
  resolution: "@electron/notarize@npm:2.2.1"
  dependencies:
    debug: "npm:^4.1.1"
    fs-extra: "npm:^9.0.1"
    promise-retry: "npm:^2.0.1"
  checksum: 10c0/d3fbbaaf26e809d4484f87826f02ba9108eba222a495ff533d9728a58a0cca6e267764baefc5616952318a6674eb6d3b7d07b1136ca0254da1c51012a0e6e6ae
  languageName: node
  linkType: hard

"@electron/osx-sign@npm:1.0.5":
  version: 1.0.5
  resolution: "@electron/osx-sign@npm:1.0.5"
  dependencies:
    compare-version: "npm:^0.1.2"
    debug: "npm:^4.3.4"
    fs-extra: "npm:^10.0.0"
    isbinaryfile: "npm:^4.0.8"
    minimist: "npm:^1.2.6"
    plist: "npm:^3.0.5"
  bin:
    electron-osx-flat: bin/electron-osx-flat.js
    electron-osx-sign: bin/electron-osx-sign.js
  checksum: 10c0/9b1099858cfe32c2d9329c16e832c003e246cf14356e541342e91876dcbb7a9bab27c89d1f521c3192d1a1d0fd06a186fa101b45341608c434fe850a3053bfb0
  languageName: node
  linkType: hard

"@electron/universal@npm:1.5.1":
  version: 1.5.1
  resolution: "@electron/universal@npm:1.5.1"
  dependencies:
    "@electron/asar": "npm:^3.2.1"
    "@malept/cross-spawn-promise": "npm:^1.1.0"
    debug: "npm:^4.3.1"
    dir-compare: "npm:^3.0.0"
    fs-extra: "npm:^9.0.1"
    minimatch: "npm:^3.0.4"
    plist: "npm:^3.0.4"
  checksum: 10c0/2ba4cfd6c7ba4a475c73ae9b168481b1c106e2f8d618a35185d72cf6bd0b9f6b8051e153fab2b63c2514f4fc9da879cca606e63e253d886b29e0e364a87bf840
  languageName: node
  linkType: hard

"@element-plus/icons-vue@npm:^2.3.1":
  version: 2.3.1
  resolution: "@element-plus/icons-vue@npm:2.3.1"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10c0/eaa00290d094fd8554027e2170cef002b6fb5f24e51f0e97764a32f0efc0fb190a9341f28292d06c3caecf1493f3d539c53ca1958d62392ee3e2a8085d2e726b
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/aix-ppc64@npm:0.19.12"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/aix-ppc64@npm:0.20.2"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/android-arm64@npm:0.19.12"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-arm64@npm:0.20.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/android-arm@npm:0.19.12"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-arm@npm:0.20.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/android-x64@npm:0.19.12"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-x64@npm:0.20.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/darwin-arm64@npm:0.19.12"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/darwin-arm64@npm:0.20.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/darwin-x64@npm:0.19.12"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/darwin-x64@npm:0.20.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/freebsd-arm64@npm:0.19.12"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/freebsd-arm64@npm:0.20.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/freebsd-x64@npm:0.19.12"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/freebsd-x64@npm:0.20.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-arm64@npm:0.19.12"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-arm64@npm:0.20.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-arm@npm:0.19.12"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-arm@npm:0.20.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-ia32@npm:0.19.12"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-ia32@npm:0.20.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-loong64@npm:0.19.12"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-loong64@npm:0.20.2"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-mips64el@npm:0.19.12"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-mips64el@npm:0.20.2"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-ppc64@npm:0.19.12"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-ppc64@npm:0.20.2"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-riscv64@npm:0.19.12"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-riscv64@npm:0.20.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-s390x@npm:0.19.12"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-s390x@npm:0.20.2"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/linux-x64@npm:0.19.12"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-x64@npm:0.20.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/netbsd-x64@npm:0.19.12"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/netbsd-x64@npm:0.20.2"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/openbsd-x64@npm:0.19.12"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/openbsd-x64@npm:0.20.2"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/sunos-x64@npm:0.19.12"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/sunos-x64@npm:0.20.2"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/win32-arm64@npm:0.19.12"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-arm64@npm:0.20.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/win32-ia32@npm:0.19.12"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-ia32@npm:0.20.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.19.12":
  version: 0.19.12
  resolution: "@esbuild/win32-x64@npm:0.19.12"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-x64@npm:0.20.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.3.0"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/7e559c4ce59cd3a06b1b5a517b593912e680a7f981ae7affab0d01d709e99cd5647019be8fafa38c350305bc32f1f7d42c7073edde2ab536c745e365f37b607e
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.10.0
  resolution: "@eslint-community/regexpp@npm:4.10.0"
  checksum: 10c0/c5f60ef1f1ea7649fa7af0e80a5a79f64b55a8a8fa5086de4727eb4c86c652aedee407a9c143b8995d2c0b2d75c1222bec9ba5d73dbfc1f314550554f0979ef4
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.6.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/32f67052b81768ae876c84569ffd562491ec5a5091b0c1e1ca1e0f3c24fb42f804952fdd0a137873bc64303ba368a71ba079a6f691cee25beee9722d94cc8573
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.0":
  version: 8.57.0
  resolution: "@eslint/js@npm:8.57.0"
  checksum: 10c0/9a518bb8625ba3350613903a6d8c622352ab0c6557a59fe6ff6178bf882bf57123f9d92aa826ee8ac3ee74b9c6203fe630e9ee00efb03d753962dcf65ee4bd94
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.0.0":
  version: 1.6.0
  resolution: "@floating-ui/core@npm:1.6.0"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.1"
  checksum: 10c0/667a68036f7dd5ed19442c7792a6002ca02d1799221c4396691bbe0b6008b48f6ccad581225e81fa266bb91232f6c66838a5f825f554217e1ec886178b93381b
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.1":
  version: 1.6.3
  resolution: "@floating-ui/dom@npm:1.6.3"
  dependencies:
    "@floating-ui/core": "npm:^1.0.0"
    "@floating-ui/utils": "npm:^0.2.0"
  checksum: 10c0/d6cac10877918ce5a8d1a24b21738d2eb130a0191043d7c0dd43bccac507844d3b4dc5d4107d3891d82f6007945ca8fb4207a1252506e91c37e211f0f73cf77e
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.0, @floating-ui/utils@npm:^0.2.1":
  version: 0.2.1
  resolution: "@floating-ui/utils@npm:0.2.1"
  checksum: 10c0/ee77756712cf5b000c6bacf11992ffb364f3ea2d0d51cc45197a7e646a17aeb86ea4b192c0b42f3fbb29487aee918a565e84f710b8c3645827767f406a6b4cc9
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.14":
  version: 0.11.14
  resolution: "@humanwhocodes/config-array@npm:0.11.14"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^2.0.2"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.0.5"
  checksum: 10c0/66f725b4ee5fdd8322c737cb5013e19fac72d4d69c8bf4b7feb192fcb83442b035b92186f8e9497c220e58b2d51a080f28a73f7899bc1ab288c3be172c467541
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.2":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: 10c0/80520eabbfc2d32fe195a93557cef50dfe8c8905de447f022675aaf66abc33ae54098f5ea78548d925aa671cd4ab7c7daa5ad704fe42358c9b5e7db60f80696c
  languageName: node
  linkType: hard

"@iconify/types@npm:^2.0.0":
  version: 2.0.0
  resolution: "@iconify/types@npm:2.0.0"
  checksum: 10c0/65a3be43500c7ccacf360e136d00e1717f050b7b91da644e94370256ac66f582d59212bdb30d00788aab4fc078262e91c95b805d1808d654b72f6d2072a7e4b2
  languageName: node
  linkType: hard

"@iconify/utils@npm:^2.1.22":
  version: 2.1.22
  resolution: "@iconify/utils@npm:2.1.22"
  dependencies:
    "@antfu/install-pkg": "npm:^0.1.1"
    "@antfu/utils": "npm:^0.7.5"
    "@iconify/types": "npm:^2.0.0"
    debug: "npm:^4.3.4"
    kolorist: "npm:^1.8.0"
    local-pkg: "npm:^0.5.0"
    mlly: "npm:^1.5.0"
  checksum: 10c0/59637de1ca1d40b3338c360d1e31bbb72115799cab1413c8f2e2c95aea97d23eeb26dbbf150b011a0b1b922ef8a3a207fee81536419de3dfaf21d9b8d0921d2a
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/1be4fd4a6b0f41337c4f5fdf4afc3bd19e39c3691924817108b82ffcb9c9e609c273f936932b9fba4b3a298ce2eb06d9bff4eb1cc3bd81c4f4ee1b4917e25feb
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.4.15":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: 10c0/0c6b5ae663087558039052a626d2d7ed5208da36cfd707dcc5cea4a07cfc918248403dcb5989a8f7afaf245ce0573b7cc6fd94c4a30453bd10e44d9363940ba5
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@malept/cross-spawn-promise@npm:^1.1.0":
  version: 1.1.1
  resolution: "@malept/cross-spawn-promise@npm:1.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.1"
  checksum: 10c0/74c427a152ffff0f19b74af6479d05bef1e996d5e081cfc3b8c47477b9240bd1c42a930884cbcd0c89ee3835201a3bd88d0b0bfd754c0cbb56fc84a28996a8e7
  languageName: node
  linkType: hard

"@malept/flatpak-bundler@npm:^0.4.0":
  version: 0.4.0
  resolution: "@malept/flatpak-bundler@npm:0.4.0"
  dependencies:
    debug: "npm:^4.1.1"
    fs-extra: "npm:^9.0.0"
    lodash: "npm:^4.17.15"
    tmp-promise: "npm:^3.0.2"
  checksum: 10c0/b3c87f6482b1956411af1118c771afb39cd9a0568fbb5e86015547ff6d68d2e73a7f0d74b75a57f0a156391c347c8d0adc1037e75172b92da72b96e0a05a2f4f
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.2
  resolution: "@npmcli/agent@npm:2.2.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/325e0db7b287d4154ecd164c0815c08007abfb07653cc57bceded17bb7fd240998a3cbdbe87d700e30bef494885eccc725ab73b668020811d56623d145b524ae
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/162b4a0b8705cd6f5c2470b851d1dc6cd228c86d2170e1769d738c1fbb69a87160901411c3c035331e9e99db72f1f1099a8b734bf1637cc32b9a5be1660e4e1e
  languageName: node
  linkType: hard

"@oozcitak/dom@npm:1.15.5":
  version: 1.15.5
  resolution: "@oozcitak/dom@npm:1.15.5"
  dependencies:
    "@oozcitak/infra": "npm:1.0.5"
    "@oozcitak/url": "npm:1.0.0"
    "@oozcitak/util": "npm:8.0.0"
  checksum: 10c0/480c704883e66d0905eb77fd8e6515f24e9dc68168af4b9c41d64f724081839c649934c43b1e1a583d2d66913078c3b35e8e78698afd72d0695bbb15296481e3
  languageName: node
  linkType: hard

"@oozcitak/dom@npm:1.15.6":
  version: 1.15.6
  resolution: "@oozcitak/dom@npm:1.15.6"
  dependencies:
    "@oozcitak/infra": "npm:1.0.5"
    "@oozcitak/url": "npm:1.0.0"
    "@oozcitak/util": "npm:8.3.4"
  checksum: 10c0/a12500428bde0c137262135e116ab4b3d055b789becf23fd46edaa116ba25d8b9660c13ff29e4814fbf3ee05bc86bbc7562cbcc82bed2ac84e09ded5a9c33c86
  languageName: node
  linkType: hard

"@oozcitak/infra@npm:1.0.3":
  version: 1.0.3
  resolution: "@oozcitak/infra@npm:1.0.3"
  dependencies:
    "@oozcitak/util": "npm:1.0.1"
  checksum: 10c0/d785a93806807c552b2d9246cd794e66ca13ea3f210244b8b888112bd8b5f71a4fa9532a36308bffccc1c496bcd7bb66184d235eb6b7bf6484e4f3d2bf5b0278
  languageName: node
  linkType: hard

"@oozcitak/infra@npm:1.0.5":
  version: 1.0.5
  resolution: "@oozcitak/infra@npm:1.0.5"
  dependencies:
    "@oozcitak/util": "npm:8.0.0"
  checksum: 10c0/e56eed062e1139e6393e1d914b133c5ceedfec3183ebe2294fd6d60bcdd36ffc0f3f9fb2fcae12ba3fe27dbba594752d946057bb65055f6d91d60ef432254fdc
  languageName: node
  linkType: hard

"@oozcitak/url@npm:1.0.0":
  version: 1.0.0
  resolution: "@oozcitak/url@npm:1.0.0"
  dependencies:
    "@oozcitak/infra": "npm:1.0.3"
    "@oozcitak/util": "npm:1.0.2"
  checksum: 10c0/4936b902844fe554ac4d7c8c929096324a3768d3d1422a23adc7f769d48800743019ec1f74c971c006e1436c9ce12673aea16485a2b95a03ac6c3b728b82dd8d
  languageName: node
  linkType: hard

"@oozcitak/util@npm:1.0.1":
  version: 1.0.1
  resolution: "@oozcitak/util@npm:1.0.1"
  checksum: 10c0/a8be595300591d04b19ddddc5e01ceea1ccb9d3adcf4bdba459c56d9ee7562e2cea9adc274e49631ac5325aa628ddf3f8f0fd59aeefc9b080bbe8e1f1f2ef8ce
  languageName: node
  linkType: hard

"@oozcitak/util@npm:1.0.2":
  version: 1.0.2
  resolution: "@oozcitak/util@npm:1.0.2"
  checksum: 10c0/05940629e5c04f93470e2508ce7b6b13bf277cab0cdb7ce7eb58d9e06898d827ce2d98cea7c935a0d195438bc300c37f676a8d605ff7625297cf0c359e6ee825
  languageName: node
  linkType: hard

"@oozcitak/util@npm:8.0.0":
  version: 8.0.0
  resolution: "@oozcitak/util@npm:8.0.0"
  checksum: 10c0/41d7ae81f14f57c9c4c0b7f89dfa4cf7138a94560215a876f2a585e0e446e8b40a04050eac3232f622d88d3e5531b81b398be05bbef6179e5132c13717c6715f
  languageName: node
  linkType: hard

"@oozcitak/util@npm:8.3.3":
  version: 8.3.3
  resolution: "@oozcitak/util@npm:8.3.3"
  checksum: 10c0/ae17e800f2468d4e35d4afbda00a86801827c1a2d3094ff2ac886f60d8896f7a32f7859859284b4648088c91cec3359019513e5aa00b934269229d7b42306d90
  languageName: node
  linkType: hard

"@oozcitak/util@npm:8.3.4":
  version: 8.3.4
  resolution: "@oozcitak/util@npm:8.3.4"
  checksum: 10c0/361187a5e3b8a4d4a11009543d51cf8a8befc73c8354074591d1692fc994561de940dc1ab5bee7fdac0ef3d455724af9c76f3cc7c37880887955b8a6ce293957
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.1
  resolution: "@pkgr/core@npm:0.1.1"
  checksum: 10c0/3f7536bc7f57320ab2cf96f8973664bef624710c403357429fbf680a5c3b4843c1dbd389bb43daa6b1f6f1f007bb082f5abcb76bb2b5dc9f421647743b71d3d8
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.24":
  version: 1.0.0-next.25
  resolution: "@polka/url@npm:1.0.0-next.25"
  checksum: 10c0/ef61f0a0fe94bb6e1143fc5b9d5a12e6ca9dbd2c57843ebf81db432c21b9f1005c09e8a1ef8b6d5ddfa42146ca65b640feb2d353bd0d3546da46ba59e48a5349
  languageName: node
  linkType: hard

"@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  version: 2.11.7
  resolution: "@sxzz/popperjs-es@npm:2.11.7"
  checksum: 10c0/5027bd98ff98caad74a20379287cb7e8bf4263eeb58e749c97dffe64e900bdd49f9e78c188c8f3cf10c0a3d4e8054ea7a11b2cc15fb67139a4c40b2a5a61b259
  languageName: node
  linkType: hard

"@rc-component/color-picker@npm:^1.5.3":
  version: 1.5.3
  resolution: "@rc-component/color-picker@npm:1.5.3"
  dependencies:
    "@babel/runtime": "npm:^7.23.6"
    "@ctrl/tinycolor": "npm:^3.6.1"
    classnames: "npm:^2.2.6"
    rc-util: "npm:^5.38.1"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/40c0c1d319624a859828c8de8ae891ac1b187ef1770ab6aab237929abf11569af3e2a5ece30dfb2c207fe80e86c4659029b0bbbdc3801e8a29e52b2885fb1f03
  languageName: node
  linkType: hard

"@rc-component/mini-decimal@npm:^1.0.1":
  version: 1.1.0
  resolution: "@rc-component/mini-decimal@npm:1.1.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.0"
  checksum: 10c0/53a7ca71591bc03eba71ab844016df788e83c96c3c7c542710c3eeeae7f55340c88c4930d7a0b11ebe7f1cd9fc65cb5bc284f466fbe95589992dd9833edf6ddf
  languageName: node
  linkType: hard

"@rc-component/portal@npm:^1.0.0-8, @rc-component/portal@npm:^1.1.0":
  version: 1.1.2
  resolution: "@rc-component/portal@npm:1.1.2"
  dependencies:
    "@babel/runtime": "npm:^7.18.0"
    classnames: "npm:^2.3.2"
    rc-util: "npm:^5.24.4"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/3c0297356635d47f364be79de02bb16009f06b1ce82124c3e63da9a71b8e7d3ea2c147e4703ead9cae0f662435a21e9feb30d2edf7197108bc3dbf969f3ca51f
  languageName: node
  linkType: hard

"@rc-component/trigger@npm:^1.18.3":
  version: 1.18.3
  resolution: "@rc-component/trigger@npm:1.18.3"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
    "@rc-component/portal": "npm:^1.1.0"
    classnames: "npm:^2.3.2"
    rc-motion: "npm:^2.0.0"
    rc-resize-observer: "npm:^1.3.1"
    rc-util: "npm:^5.38.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/13cb9b77cc977c10c2e4d80042d12ae91d164e117761db14360250d8b200b5b779874314bdacbe169062ad02626f446f831ebb97330ea2e7e3938ee703c570a9
  languageName: node
  linkType: hard

"@rc-component/trigger@npm:^2.0.0, @rc-component/trigger@npm:^2.1.1":
  version: 2.1.1
  resolution: "@rc-component/trigger@npm:2.1.1"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
    "@rc-component/portal": "npm:^1.1.0"
    classnames: "npm:^2.3.2"
    rc-motion: "npm:^2.0.0"
    rc-resize-observer: "npm:^1.3.1"
    rc-util: "npm:^5.38.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/c5de9d651b764c9d7b94d614df346246eb3a77a3ba5339b0107a3fa434a75c3a51198db0bca0cd596b1be296128052275af70bd3831f9f1bd6ea40d64a638342
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.1.0":
  version: 5.1.0
  resolution: "@rollup/pluginutils@npm:5.1.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^2.0.2"
    picomatch: "npm:^2.3.1"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/c7bed15711f942d6fdd3470fef4105b73991f99a478605e13d41888963330a6f9e32be37e6ddb13f012bc7673ff5e54f06f59fd47109436c1c513986a8a7612d
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.14.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-android-arm64@npm:4.14.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-darwin-arm64@npm:4.14.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-darwin-x64@npm:4.14.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.14.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.14.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.14.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.14.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.14.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.14.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.14.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.14.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.14.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.14.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.14.2":
  version: 4.14.2
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.14.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.7.1":
  version: 1.10.2
  resolution: "@rushstack/eslint-patch@npm:1.10.2"
  checksum: 10c0/3712b8994bfed0968d4c4f21ff10bf5f2abb47f47d65d4e616de4311d2f372f2528b03b1d4e0dcaa7392f8626ccdf114e753db4f790e92436fc5a4e52195d181
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 10c0/33b6fb1d0834ec8dd7689ddc0e2781c2bfd8b9c4e4bacbcb14111e0ae00621f2c264b8a7d36541799d74888b5dccdf422a891a5cb5a709ace26325eedc81e22e
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: "npm:^2.0.0"
  checksum: 10c0/73946918c025339db68b09abd91fa3001e87fc749c619d2e9c2003a663039d4c3cb89836c98a96598b3d47dec2481284ba85355392644911f5ecd2336536697f
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: 10c0/073bfa548026b1ebaf1659eb8961e526be22fa77139b10d60e712f46d2f0f05f4e6c8bec62a087d41088ee9e29faa7f54838568e475ab2f776171003c3920858
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "npm:*"
    "@types/keyv": "npm:^3.1.4"
    "@types/node": "npm:*"
    "@types/responselike": "npm:^1.0.0"
  checksum: 10c0/10816a88e4e5b144d43c1d15a81003f86d649776c7f410c9b5e6579d0ad9d4ca71c541962fb403077388b446e41af7ae38d313e46692144985f006ac5e11fa03
  languageName: node
  linkType: hard

"@types/debug@npm:^4.1.6":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10c0/5dcd465edbb5a7f226e9a5efd1f399c6172407ef5840686b73e3608ce135eeca54ae8037dcd9f16bdb2768ac74925b820a8b9ecc588a58ca09eca6acabe33e2f
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.5, @types/estree@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree@npm:1.0.5"
  checksum: 10c0/b3b0e334288ddb407c7b3357ca67dbee75ee22db242ca7c56fe27db4e1a31989cb8af48a84dd401deb787fe10cc6b2ab1ee82dc4783be87ededbe3d53c79c70d
  languageName: node
  linkType: hard

"@types/fs-extra@npm:9.0.13, @types/fs-extra@npm:^9.0.11":
  version: 9.0.13
  resolution: "@types/fs-extra@npm:9.0.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/576d4e9d382393316ed815c593f7f5c157408ec5e184521d077fcb15d514b5a985245f153ef52142b9b976cb9bd8f801850d51238153ebd0dc9e96b7a7548588
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: 10c0/51b72568b4b2863e0fe8d6ce8aad72a784b7510d72dc866215642da51d84945a9459fa89f49ec48f1e9a1752e6a78e85a4cda0ded06b1c73e727610c925f9ce6
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/ff8f54fc49621210291f815fe5b15d809fd7d032941b3180743440bd507ecdf08b9e844625fa346af568c84bf34114eb378dcdc3e921a08ba1e2a08d7e3c809c
  languageName: node
  linkType: hard

"@types/lodash-es@npm:^4.17.6":
  version: 4.17.12
  resolution: "@types/lodash-es@npm:4.17.12"
  dependencies:
    "@types/lodash": "npm:*"
  checksum: 10c0/5d12d2cede07f07ab067541371ed1b838a33edb3c35cb81b73284e93c6fd0c4bbeaefee984e69294bffb53f62d7272c5d679fdba8e595ff71e11d00f2601dde0
  languageName: node
  linkType: hard

"@types/lodash@npm:*, @types/lodash@npm:^4.14.182":
  version: 4.17.0
  resolution: "@types/lodash@npm:4.17.0"
  checksum: 10c0/4c5b41c9a6c41e2c05d08499e96f7940bcf194dcfa84356235b630da920c2a5e05f193618cea76006719bec61c76617dff02defa9d29934f9f6a76a49291bd8f
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 0.7.34
  resolution: "@types/ms@npm:0.7.34"
  checksum: 10c0/ac80bd90012116ceb2d188fde62d96830ca847823e8ca71255616bc73991aa7d9f057b8bfab79e8ee44ffefb031ddd1bcce63ea82f9e66f7c31ec02d2d823ccc
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.12.7
  resolution: "@types/node@npm:20.12.7"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/dce80d63a3b91892b321af823d624995c61e39c6a223cc0ac481a44d337640cc46931d33efb3beeed75f5c85c3bda1d97cef4c5cd4ec333caf5dee59cff6eca0
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.18, @types/node@npm:^18.19.9":
  version: 18.19.31
  resolution: "@types/node@npm:18.19.31"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/bfebae8389220c0188492c82eaf328f4ba15e6e9b4abee33d6bf36d3b13f188c2f53eb695d427feb882fff09834f467405e2ed9be6aeb6ad4705509822d2ea08
  languageName: node
  linkType: hard

"@types/plist@npm:^3.0.1":
  version: 3.0.5
  resolution: "@types/plist@npm:3.0.5"
  dependencies:
    "@types/node": "npm:*"
    xmlbuilder: "npm:>=11.0.1"
  checksum: 10c0/2a929f4482e3bea8c3288a46ae589a2ae2d01df5b7841ead7032d7baa79d79af6c875a5798c90705eea9306c2fb1544d7ed12ab3c905c5626d5dd5dc9f464b94
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/a58ba341cb9e7d74f71810a88862da7b2a6fa42e2a1fc0ce40498f6ea1d44382f0640117057da779f74c47039f7166bf48fad02dc876f94e005c7afa50f5e129
  languageName: node
  linkType: hard

"@types/semver@npm:^7.5.0":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: 10c0/8663ff927234d1c5fcc04b33062cb2b9fcfbe0f5f351ed26c4d1e1581657deebd506b41ff7fdf89e787e3d33ce05854bc01686379b89e9c49b564c4cfa988efa
  languageName: node
  linkType: hard

"@types/verror@npm:^1.10.3":
  version: 1.10.10
  resolution: "@types/verror@npm:1.10.10"
  checksum: 10c0/413c0c0370ed6a796d630fbcdae20049ab3e26558c62bc5f53327830ddb0965aaadedb92f4933b28ee8fc8089e1293b742a0efbf6b264d15ce3930c6b83b0984
  languageName: node
  linkType: hard

"@types/web-bluetooth@npm:^0.0.16":
  version: 0.0.16
  resolution: "@types/web-bluetooth@npm:0.0.16"
  checksum: 10c0/9a265fdd048319e174f9a0ae2dfb748d0b3e07f888d9797f89dd78b96d680fd304fbfa9fd0e11ccf283bd6a441641333ec8c3184e61a50c7ee61507add63f0a2
  languageName: node
  linkType: hard

"@types/web-bluetooth@npm:^0.0.20":
  version: 0.0.20
  resolution: "@types/web-bluetooth@npm:0.0.20"
  checksum: 10c0/3a49bd9396506af8f1b047db087aeeea9fe4301b7fad4fe06ae0f6e00d331138caae878fd09e6410658b70b4aaf10e4b191c41c1a5ff72211fe58da290c7d003
  languageName: node
  linkType: hard

"@types/yauzl@npm:^2.9.1":
  version: 2.10.3
  resolution: "@types/yauzl@npm:2.10.3"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/f1b7c1b99fef9f2fe7f1985ef7426d0cebe48cd031f1780fcdc7451eec7e31ac97028f16f50121a59bcf53086a1fc8c856fd5b7d3e00970e43d92ae27d6b43dc
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.14.0, @typescript-eslint/eslint-plugin@npm:^6.7.0":
  version: 6.21.0
  resolution: "@typescript-eslint/eslint-plugin@npm:6.21.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.5.1"
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/type-utils": "npm:6.21.0"
    "@typescript-eslint/utils": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.4"
    natural-compare: "npm:^1.4.0"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/f911a79ee64d642f814a3b6cdb0d324b5f45d9ef955c5033e78903f626b7239b4aa773e464a38c3e667519066169d983538f2bf8e5d00228af587c9d438fb344
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.14.0, @typescript-eslint/parser@npm:^6.7.0":
  version: 6.21.0
  resolution: "@typescript-eslint/parser@npm:6.21.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/a8f99820679decd0d115c0af61903fb1de3b1b5bec412dc72b67670bf636de77ab07f2a68ee65d6da7976039bbf636907f9d5ca546db3f0b98a31ffbc225bc7d
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/scope-manager@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
  checksum: 10c0/eaf868938d811cbbea33e97e44ba7050d2b6892202cea6a9622c486b85ab1cf801979edf78036179a8ba4ac26f1dfdf7fcc83a68c1ff66be0b3a8e9a9989b526
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/type-utils@npm:6.21.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    "@typescript-eslint/utils": "npm:6.21.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/7409c97d1c4a4386b488962739c4f1b5b04dc60cf51f8cd88e6b12541f84d84c6b8b67e491a147a2c95f9ec486539bf4519fb9d418411aef6537b9c156468117
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/types@npm:6.21.0"
  checksum: 10c0/020631d3223bbcff8a0da3efbdf058220a8f48a3de221563996ad1dcc30d6c08dadc3f7608cc08830d21c0d565efd2db19b557b9528921c78aabb605eef2d74d
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/typescript-estree@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:9.0.3"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/af1438c60f080045ebb330155a8c9bb90db345d5069cdd5d01b67de502abb7449d6c75500519df829f913a6b3f490ade3e8215279b6bdc63d0fb0ae61034df5f
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/utils@npm:6.21.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@types/json-schema": "npm:^7.0.12"
    "@types/semver": "npm:^7.5.0"
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: 10c0/ab2df3833b2582d4e5467a484d08942b4f2f7208f8e09d67de510008eb8001a9b7460f2f9ba11c12086fd3cdcac0c626761c7995c2c6b5657d5fa6b82030a32d
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/visitor-keys@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10c0/7395f69739cfa1cb83c1fb2fad30afa2a814756367302fb4facd5893eff66abc807e8d8f63eba94ed3b0fe0c1c996ac9a1680bcbf0f83717acedc3f2bb724fbf
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.2.0
  resolution: "@ungap/structured-clone@npm:1.2.0"
  checksum: 10c0/8209c937cb39119f44eb63cf90c0b73e7c754209a6411c707be08e50e29ee81356dca1a848a405c8bdeebfe2f5e4f831ad310ae1689eeef65e7445c090c6657d
  languageName: node
  linkType: hard

"@univerjs/core@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/core@npm:0.1.7"
  dependencies:
    "@univerjs/protocol": "npm:^0.1.17"
    dayjs: "npm:^1.11.10"
    nanoid: "npm:5.0.7"
    numeral: "npm:^2.0.6"
  peerDependencies:
    "@wendellhu/redi": 0.13.0
    rxjs: ">=7.0.0"
  checksum: 10c0/65e186fbd641682a6b4caafd176e13256bda28226d831e4d64186077a3b2e575553c0f70a32045909d2cdb3ec1682ff3b29b34de7c1dcd9dc51b50ef0b38f996
  languageName: node
  linkType: hard

"@univerjs/design@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/design@npm:0.1.7"
  dependencies:
    "@rc-component/color-picker": "npm:^1.5.3"
    "@rc-component/trigger": "npm:^1.18.3"
    "@univerjs/icons": "npm:^0.1.44"
    dayjs: "npm:^1.11.10"
    rc-dialog: "npm:^9.4.0"
    rc-dropdown: "npm:^4.2.0"
    rc-input: "npm:^1.4.5"
    rc-input-number: "npm:^9.0.0"
    rc-menu: "npm:^9.13.0"
    rc-picker: "npm:^4.3.0"
    rc-segmented: "npm:^2.3.0"
    rc-select: "npm:^14.13.0"
    rc-textarea: "npm:^1.6.3"
    rc-tooltip: "npm:^6.2.0"
    rc-util: "npm:^5.39.1"
    react-draggable: "npm:^4.4.6"
    react-grid-layout: "npm:^1.4.4"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    clsx: ">=2.0.0"
    react: ^16.9.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0
  checksum: 10c0/aa09486d0d267dad9225326522df42bbb8be9fd4795702cf4a6a570a7360184d7fd6226ec2f14773c3ea5c5ae606df0e0bbb92750aae6f9ae044c81037b64337
  languageName: node
  linkType: hard

"@univerjs/docs-ui@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/docs-ui@npm:0.1.7"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/design": 0.1.7
    "@univerjs/docs": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@univerjs/ui": 0.1.7
    "@wendellhu/redi": 0.13.0
    react: ^16.9.0 || ^17.0.0 || ^18.0.0
    rxjs: ">=7.0.0"
  checksum: 10c0/8365c5c30251c035948bde0ce5c70ebb93d19a17406e6606de4647f51e4c577f19c8b43000c88726d0b033b4721155487b9d4f6d201920c22620c67fead511bc
  languageName: node
  linkType: hard

"@univerjs/docs@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/docs@npm:0.1.7"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@wendellhu/redi": 0.13.0
    rxjs: ">=7.0.0"
  checksum: 10c0/b002bbf3d4431c2d5014d64d3278129641fa5d3fb2697e6c679b39032cc1fe0575e4ab4080c8205e890daa624059c99859f5ce1ca4524de29f4afe6555bf5acb
  languageName: node
  linkType: hard

"@univerjs/engine-formula@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/engine-formula@npm:0.1.7"
  dependencies:
    big.js: "npm:^6.2.1"
    numfmt: "npm:^2.5.2"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@wendellhu/redi": 0.13.0
    rxjs: ">=7.0.0"
  checksum: 10c0/8ec0e64967d0fca36329a7c0bfa52d2b10e921b66dbe3b9f80fb86bfa0737e19815969f916aae8425dedabf690d4e09d85f67d0250e4a892cdec8403057412ff
  languageName: node
  linkType: hard

"@univerjs/engine-render@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/engine-render@npm:0.1.7"
  dependencies:
    cjk-regex: "npm:^3.1.0"
    linebreak: "npm:^1.1.0"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@wendellhu/redi": 0.13.0
    rxjs: ">=7.0.0"
  checksum: 10c0/a870ec93155583efe4b9cd7048c43a69d3801b845190bac6e650d39a3700338e682bcb9de0144732a1efc2c569e3089691075a2f9866671ce5c3b0d98516a2c0
  languageName: node
  linkType: hard

"@univerjs/icons@npm:^0.1.44":
  version: 0.1.44
  resolution: "@univerjs/icons@npm:0.1.44"
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: 10c0/866096efe670c7282e3b8f55bd66ba9119b01e3207649fd6aa76672974ffac8e5e62551551b5f86fdf74ace0359de6beee7c39213102862ccc85f0d45d506f12
  languageName: node
  linkType: hard

"@univerjs/protocol@npm:^0.1.17":
  version: 0.1.17
  resolution: "@univerjs/protocol@npm:0.1.17"
  peerDependencies:
    "@grpc/grpc-js": ^1.9.14
    rxjs: ">=7.0.0"
  checksum: 10c0/b186b16b623fcd23f0c43b2b379f7a5d03a87d4369094f6cba7c455c7c325ff2027547f4f8efd4428ac920ff30714966ab9da144343c45be3234242100450544
  languageName: node
  linkType: hard

"@univerjs/sheets-formula@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/sheets-formula@npm:0.1.7"
  dependencies:
    "@univerjs/icons": "npm:^0.1.44"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/design": 0.1.7
    "@univerjs/docs": 0.1.7
    "@univerjs/engine-formula": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@univerjs/sheets": 0.1.7
    "@univerjs/sheets-numfmt": 0.1.7
    "@univerjs/sheets-ui": 0.1.7
    "@univerjs/ui": 0.1.7
    "@wendellhu/redi": 0.13.0
    react: ^16.9.0 || ^17.0.0 || ^18.0.0
    rxjs: ">=7.0.0"
  checksum: 10c0/c9b0ee04c1d67ff8906bf0c0c3bca7bdb5975d4e524d5e933bf49c19bd077221f853c9681578ddf57fab134f96874711700ec0fa5e19ea7f039b2a75dae7f8c2
  languageName: node
  linkType: hard

"@univerjs/sheets-ui@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/sheets-ui@npm:0.1.7"
  dependencies:
    "@univerjs/icons": "npm:^0.1.44"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/design": 0.1.7
    "@univerjs/docs": 0.1.7
    "@univerjs/docs-ui": 0.1.7
    "@univerjs/engine-formula": 0.1.7
    "@univerjs/engine-numfmt": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@univerjs/sheets": 0.1.7
    "@univerjs/ui": 0.1.7
    "@wendellhu/redi": 0.13.0
    clsx: ">=2.0.0"
    react: ^16.9.0 || ^17.0.0 || ^18.0.0
    rxjs: ">=7.0.0"
  checksum: 10c0/c1e1a6b190182b6f0e2178a852cfcc4b91a7cf139b22fd8a13309a4436eb17737b3d2d3f168b7e85e0de3544b06c61e5fad978accdae3029705dd9ff7b34935b
  languageName: node
  linkType: hard

"@univerjs/sheets@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/sheets@npm:0.1.7"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/engine-formula": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@wendellhu/redi": 0.13.0
    rxjs: ">=7.0.0"
  checksum: 10c0/1e3393d775eebdebad562a9c6daf44236c0e8e2960f684c3ba1f4a257d42eaae010e5444e04207dee0a3536bd1f732c5d06b55feafc8691a63a793a81164f1d7
  languageName: node
  linkType: hard

"@univerjs/ui@npm:^0.1.4":
  version: 0.1.7
  resolution: "@univerjs/ui@npm:0.1.7"
  dependencies:
    "@univerjs/icons": "npm:^0.1.44"
    localforage: "npm:^1.10.0"
    rc-notification: "npm:^5.4.0"
    rc-util: "npm:^5.39.1"
    vue: "npm:>=3.0.0"
  peerDependencies:
    "@univerjs/core": 0.1.7
    "@univerjs/design": 0.1.7
    "@univerjs/engine-formula": 0.1.7
    "@univerjs/engine-render": 0.1.7
    "@wendellhu/redi": 0.13.0
    clsx: ">=2.0.0"
    react: ^16.9.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0
    rxjs: ">=7.0.0"
  dependenciesMeta:
    vue:
      optional: true
  checksum: 10c0/765f5ebc11f651422574fc57b8d57c82e809db5439e6c860018578a65326dc4d243f67163caaa1b73e2cb1b20aadf0aaafbd629c29e3986c09c6b2dc8254ea76
  languageName: node
  linkType: hard

"@unocss/astro@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/astro@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/reset": "npm:0.59.0-beta.1"
    "@unocss/vite": "npm:0.59.0-beta.1"
  peerDependencies:
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
  peerDependenciesMeta:
    vite:
      optional: true
  checksum: 10c0/67666308d04a96a65094c6fb057c53538ffa629d8262ec5c4b4e689a1ae7b20cea77f39439c7c4dddfe93a73f1cabbf345480fb3fcf31b87ee5471b2551694f9
  languageName: node
  linkType: hard

"@unocss/cli@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/cli@npm:0.59.0-beta.1"
  dependencies:
    "@ampproject/remapping": "npm:^2.3.0"
    "@rollup/pluginutils": "npm:^5.1.0"
    "@unocss/config": "npm:0.59.0-beta.1"
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/preset-uno": "npm:0.59.0-beta.1"
    cac: "npm:^6.7.14"
    chokidar: "npm:^3.6.0"
    colorette: "npm:^2.0.20"
    consola: "npm:^3.2.3"
    fast-glob: "npm:^3.3.2"
    magic-string: "npm:^0.30.8"
    pathe: "npm:^1.1.2"
    perfect-debounce: "npm:^1.0.0"
  bin:
    unocss: bin/unocss.mjs
  checksum: 10c0/6855e662b5ba244c2b5b4b3a69c8485d6175177217bce4cf0bfb416f5323c0e0d3fb8d15fea292b387f7d3544de5d9f15237e0a796b7b8d8c884632c7672fff8
  languageName: node
  linkType: hard

"@unocss/config@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/config@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    unconfig: "npm:^0.3.11"
  checksum: 10c0/1fb2dc548aeb2d70975e7f3f6783ccee832ff007d9dbfc3318bd88bf6d1724de04d874ea159b75fc42c63cb880dbc824f9edd9483b853fbe66839b6b312ec94d
  languageName: node
  linkType: hard

"@unocss/core@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/core@npm:0.59.0-beta.1"
  checksum: 10c0/94d0ae47957228924d683574436ed63de288095fab6b6df9c7125df5b8215589eb20b9acd914bbdbe65ddfb097a16694a639f4b337db6c3f778a1b5fcfb696d2
  languageName: node
  linkType: hard

"@unocss/core@npm:^0.59.0-beta.1":
  version: 0.59.2
  resolution: "@unocss/core@npm:0.59.2"
  checksum: 10c0/d34c9f04ccac37f9e8302889e3fffe98815321fe676411d4a4532fe802a18abc807ece83b9a481000852a65f474f2a3f81f6bdc544333c72b3a6a9dac80e408b
  languageName: node
  linkType: hard

"@unocss/extractor-arbitrary-variants@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/extractor-arbitrary-variants@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/c5324ab3cd140e48ba42af578289a0118c6e77e0e1cbd96c6522613e18665baf0214198c4c3dd32350a4f45dbfe707260bac7eb7895ba4e78924352dacde53ab
  languageName: node
  linkType: hard

"@unocss/inspector@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/inspector@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
    gzip-size: "npm:^6.0.0"
    sirv: "npm:^2.0.3"
  checksum: 10c0/48ecfb451524c779dd7d876c2d1528fece63550a28662508283f7589829a97e6755e486f4fed1cf37af74506aaf414afeedbb02a6a1cbae7c7768d983bd3133a
  languageName: node
  linkType: hard

"@unocss/postcss@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/postcss@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/config": "npm:0.59.0-beta.1"
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
    css-tree: "npm:^2.3.1"
    fast-glob: "npm:^3.3.2"
    magic-string: "npm:^0.30.8"
    postcss: "npm:^8.4.38"
  peerDependencies:
    postcss: ^8.4.21
  checksum: 10c0/19b811b0deb907d7a06b3f7cf04d49f659fd5b4acb4f13404ef07f7c2f53bacb64f1512f5fe41529625a0780d099139272f1bd37dddc40dc41eae405ebaa1127
  languageName: node
  linkType: hard

"@unocss/preset-attributify@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-attributify@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/d13a5abb761504ba9897c41edfa53dc52ec981e63271ba4d548af28ecdff157bed517fc8b9a81ffa696c0d04eb802da50000dee312b8a52e384c27fca5b6c391
  languageName: node
  linkType: hard

"@unocss/preset-icons@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-icons@npm:0.59.0-beta.1"
  dependencies:
    "@iconify/utils": "npm:^2.1.22"
    "@unocss/core": "npm:0.59.0-beta.1"
    ofetch: "npm:^1.3.4"
  checksum: 10c0/b4af96e0b864331276f6f458043a39c9195d0c09f620764a3027431c8d95f3d8c405e1f0ac0b8a61ab40b7f30e94a71f327ae4a7c6205d0b499869e5f8f1cc4f
  languageName: node
  linkType: hard

"@unocss/preset-mini@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-mini@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/extractor-arbitrary-variants": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
  checksum: 10c0/0988378db173aca1ffacda8c5b508808efca43b9869af440b471affe98fddcde4f4d8483aecd615381f938e8bb0fa9e7696d66b1edb9b0bd9d1a563769171aa7
  languageName: node
  linkType: hard

"@unocss/preset-tagify@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-tagify@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/c7a0c72f068474956d33bf334e252c94854d2c8131a96ecba1e9a7f6711cb6ac09e94bfc7d90e06fe8a50cb3f33248ff6de5775af32c1ad4f4e54648607d3d78
  languageName: node
  linkType: hard

"@unocss/preset-typography@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-typography@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/preset-mini": "npm:0.59.0-beta.1"
  checksum: 10c0/8aedf39b12a2ffe0bf89f60df0837a9c97ce9de6da54aa662970c0d98cbf2522b755161773b77ad9380fabf7e9a1871c018fd461ed6e6b47bbbf730be2b1d515
  languageName: node
  linkType: hard

"@unocss/preset-uno@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-uno@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/preset-mini": "npm:0.59.0-beta.1"
    "@unocss/preset-wind": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
  checksum: 10c0/a6f3d276a80a5a6dba50d2e41040f0e82f321de6f69f173464046fd66a1d1862ff4fb67b7d10cacbac1c55be55125af537ac5b94c793ea7012eb7fca9ac8aacd
  languageName: node
  linkType: hard

"@unocss/preset-web-fonts@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-web-fonts@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    ofetch: "npm:^1.3.4"
  checksum: 10c0/094b9959ecd2d1acc5dd9b7680c2d0649c276eb0986d6f944cb319d30779ecb7d68996ec5ee630d2297ae9a5241c02beeec1350e750f16507e89bb7c8eb2d451
  languageName: node
  linkType: hard

"@unocss/preset-wind@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/preset-wind@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/preset-mini": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
  checksum: 10c0/d6612752de82c3713de0cea1d663a232ef4aea14deb691b753125e05a3e9b05fbf54169fdf236f294df0ea4d816e81ba105286ff7ab6d11ee8625295b843ed36
  languageName: node
  linkType: hard

"@unocss/reset@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/reset@npm:0.59.0-beta.1"
  checksum: 10c0/71760df8a2f37c93865460d7ef1806a1c57f02bc1394098e4cb20008f95f15b70682b6c3c9ff6ef6e39162f33bad91865d48d834203c805014ce1d5fe13c9b34
  languageName: node
  linkType: hard

"@unocss/rule-utils@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/rule-utils@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:^0.59.0-beta.1"
    magic-string: "npm:^0.30.8"
  checksum: 10c0/fcf085504e9906db913f1d8657ff2b13adbcce1de786e2ba5386f5ac434dee34e2229f89732305093df32f2eb1a45baf8028abab5fab4740faba68d14039024f
  languageName: node
  linkType: hard

"@unocss/scope@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/scope@npm:0.59.0-beta.1"
  checksum: 10c0/eeb111a72f9c6fd90eca4b2d212ab7a83a27c30d719b610aaa88d6e95c3af5129961dc030c8bdaa67614ade4bf272ecde3f295a7d4aace4d249fac268fefb3b6
  languageName: node
  linkType: hard

"@unocss/transformer-attributify-jsx-babel@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/transformer-attributify-jsx-babel@npm:0.59.0-beta.1"
  dependencies:
    "@babel/core": "npm:^7.24.3"
    "@babel/plugin-syntax-jsx": "npm:^7.24.1"
    "@babel/preset-typescript": "npm:^7.24.1"
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/6e2fb0e33d5bc8c7b2a4cbb623513ad2423bdf791d977011b78fd86f4a81bca3f853ae2085f514e6b0adc7b13a8e0b1002f85584d0408fd53adfadcbd797569c
  languageName: node
  linkType: hard

"@unocss/transformer-attributify-jsx@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/transformer-attributify-jsx@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/3bb98f1c9c33a0f18fd1f5e88f4bdc8699059dca23432b5c0258c436d688a20438b59bdf62856d357b3177af7033e00f1ca9b175071bacf8562178e0a8fa2d5c
  languageName: node
  linkType: hard

"@unocss/transformer-compile-class@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/transformer-compile-class@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/70de2c12c9fcf4c4687c99b69bce121c2a18b424158996a1a82a106c6297c62c100ea4bf40160ff98b394ae782678b9b2ca6133eee5352861cf41bffd3323444
  languageName: node
  linkType: hard

"@unocss/transformer-directives@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/transformer-directives@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/rule-utils": "npm:0.59.0-beta.1"
    css-tree: "npm:^2.3.1"
  checksum: 10c0/2a34b5f9dbec065eed0cf051f1023321672ebf8d4b90f8a138ff58ead5d5d1d16534f36f9b29a3c5e62993840344157757186f09063b542781cc0c2b4a665768
  languageName: node
  linkType: hard

"@unocss/transformer-variant-group@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/transformer-variant-group@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/core": "npm:0.59.0-beta.1"
  checksum: 10c0/232d8f4864b8c8327242bade3ed9062708d0a90ea1244008bef4239429609a0459034a1284884596df8a6e5885aae514f55cb1cb00b925baa331155df569a87a
  languageName: node
  linkType: hard

"@unocss/vite@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "@unocss/vite@npm:0.59.0-beta.1"
  dependencies:
    "@ampproject/remapping": "npm:^2.3.0"
    "@rollup/pluginutils": "npm:^5.1.0"
    "@unocss/config": "npm:0.59.0-beta.1"
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/inspector": "npm:0.59.0-beta.1"
    "@unocss/scope": "npm:0.59.0-beta.1"
    "@unocss/transformer-directives": "npm:0.59.0-beta.1"
    chokidar: "npm:^3.6.0"
    fast-glob: "npm:^3.3.2"
    magic-string: "npm:^0.30.8"
  peerDependencies:
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
  checksum: 10c0/7638e5b8e864706f9e613fef5b2aa64937760723083b1d48effbab38886ecdf99df8730cd190903a0a16c956280757fa783855d551b34a7e8df75bc2488b0e3f
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:^5.0.3":
  version: 5.0.4
  resolution: "@vitejs/plugin-vue@npm:5.0.4"
  peerDependencies:
    vite: ^5.0.0
    vue: ^3.2.25
  checksum: 10c0/2e65900ff41037a013ef40351aa2fb68ee1963da461795b4e3d01fc4a0226c65e9ca4bc39941dc163f883773bcd80744ee0f1f96ecc2f46fae1a4cd71c741308
  languageName: node
  linkType: hard

"@volar/language-core@npm:1.11.1, @volar/language-core@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/language-core@npm:1.11.1"
  dependencies:
    "@volar/source-map": "npm:1.11.1"
  checksum: 10c0/92c4439e3a9ccc534c970031388c318740f6fa032283d03e136c6c8c0228f549c68a7c363af1a28252617a0dca6069e14028329ac906d5acf1912931d0cdcb69
  languageName: node
  linkType: hard

"@volar/source-map@npm:1.11.1, @volar/source-map@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/source-map@npm:1.11.1"
  dependencies:
    muggle-string: "npm:^0.3.1"
  checksum: 10c0/0bfc639889802705f8036ea8b2052a95a4d691a68bc2b6744ba8b9d312d887393dd3278101180a5ee5304972899d493972a483afafd41e097968746c77d724cb
  languageName: node
  linkType: hard

"@volar/typescript@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/typescript@npm:1.11.1"
  dependencies:
    "@volar/language-core": "npm:1.11.1"
    path-browserify: "npm:^1.0.1"
  checksum: 10c0/86fe153db3a14d8eb3632784a1d7fcbfbfb51fa5517c3878bfdd49ee8d15a83b1a09f9c589454b7396454c104d3a8e2db3a987dc99b37c33816772fc3e292bf2
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-core@npm:3.4.21"
  dependencies:
    "@babel/parser": "npm:^7.23.9"
    "@vue/shared": "npm:3.4.21"
    entities: "npm:^4.5.0"
    estree-walker: "npm:^2.0.2"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/3ee871b95e17948d10375093c8dd3265923f844528a24ac67512c201ddb9b628021c010565f3e50f2e551b217c502e80a7901384f616a977a04f81e68c64a37c
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.4.21, @vue/compiler-dom@npm:^3.3.0":
  version: 3.4.21
  resolution: "@vue/compiler-dom@npm:3.4.21"
  dependencies:
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/b4a1099eddacded2663d12388b48088ca0be0d8969a070476f49e4e65da9b22851fc897cc693662b178e7e7fdee98fcf9ea3617a1f626c3a1b2089815cb1264e
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-sfc@npm:3.4.21"
  dependencies:
    "@babel/parser": "npm:^7.23.9"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-ssr": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:^0.30.7"
    postcss: "npm:^8.4.35"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/8d9a6ee07a9c542528f09b7a99e5d40e9752dca39251994e4309cb6121997c47db6818be75555aa69fb4f0bd54820bc7675c0c6e2ea5afe339f09d40890d26a9
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-ssr@npm:3.4.21"
  dependencies:
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/bae2b76f8619f258a90e2964cdcebef44aa240ae64be6bb08227f3404239c66f3d77fb25b88a809d9b29063a2f0f423595c8be8e5f7c80dc8337da2aad4f6fdc
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.5.1":
  version: 6.6.1
  resolution: "@vue/devtools-api@npm:6.6.1"
  checksum: 10c0/ab9a1e09baae514b0d3a8bf1d670ecb7724f7e55b82eea30aa2e7255e6200b45c1086c1376560f243cf86e98c0726e94d69cefe0ad23dfd50c7c49dfcb1fbf21
  languageName: node
  linkType: hard

"@vue/eslint-config-prettier@npm:^9.0.0":
  version: 9.0.0
  resolution: "@vue/eslint-config-prettier@npm:9.0.0"
  dependencies:
    eslint-config-prettier: "npm:^9.0.0"
    eslint-plugin-prettier: "npm:^5.0.0"
  peerDependencies:
    eslint: ">= 8.0.0"
    prettier: ">= 3.0.0"
  checksum: 10c0/465fe43e7a4f3181e73298df55cc5ba1de4e4349b6d859ef38e52f82406a0eac938809721b3a0f026b56ae533d51b45c4a2ecb6112d78e8d6e6ee3ca0892bba6
  languageName: node
  linkType: hard

"@vue/eslint-config-typescript@npm:^12.0.0":
  version: 12.0.0
  resolution: "@vue/eslint-config-typescript@npm:12.0.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:^6.7.0"
    "@typescript-eslint/parser": "npm:^6.7.0"
    vue-eslint-parser: "npm:^9.3.1"
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0
    eslint-plugin-vue: ^9.0.0
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/3edb549c5d0ee7049e8c49c69fca5272df53376fba66afd7cf3e088c89dfc154ccb9406ac9729c275e643f93ff5ae6d4b35fbd7b808517dd02c3488ab9392d64
  languageName: node
  linkType: hard

"@vue/language-core@npm:1.8.27":
  version: 1.8.27
  resolution: "@vue/language-core@npm:1.8.27"
  dependencies:
    "@volar/language-core": "npm:~1.11.1"
    "@volar/source-map": "npm:~1.11.1"
    "@vue/compiler-dom": "npm:^3.3.0"
    "@vue/shared": "npm:^3.3.0"
    computeds: "npm:^0.0.1"
    minimatch: "npm:^9.0.3"
    muggle-string: "npm:^0.3.1"
    path-browserify: "npm:^1.0.1"
    vue-template-compiler: "npm:^2.7.14"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/2018214d8ce2643d19e8e84eddaeacddca28b2980984d7916d97f97556c3716be184cf9f8c4f506d072a11f265401e3bc0391117cf7cfcc1e4a25048f4432dc7
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/reactivity@npm:3.4.21"
  dependencies:
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/9296ba12dc87bed7c056801a8cbff215ff4245404078186188f3c549a075f159acc7eeaf876a949450d22fc1c99a5b9f1818b4c0f828feaaf25273476ff4244b
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/runtime-core@npm:3.4.21"
  dependencies:
    "@vue/reactivity": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/9d57aaf24b33c21f4b632c1efca3baac6af420676e2f3ef0d79f0efd606617d1be56f2bd345afb1960a5603f2f5f48bbb80535403cd526f3bbba1322f823ce5b
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/runtime-dom@npm:3.4.21"
  dependencies:
    "@vue/runtime-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    csstype: "npm:^3.1.3"
  checksum: 10c0/29ad38e1c9c6286bb7dfbc3d1830b03f73f870183f9e9d81e6dbc11a71f6ff7ec2a0428832d678d8ed9e8722a98580202597adde57dcc3b47f848abb7b8d16c2
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/server-renderer@npm:3.4.21"
  dependencies:
    "@vue/compiler-ssr": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  peerDependencies:
    vue: 3.4.21
  checksum: 10c0/3ff91392473cea8d85a11e8315bf378fd0cb4b5e4f650acad3b1bc672ceb3a0e29d22d4860186b06697b72a8ab544d67ba7969e77fed07a402c3528f90c764ed
  languageName: node
  linkType: hard

"@vue/shared@npm:3.4.21, @vue/shared@npm:^3.3.0":
  version: 3.4.21
  resolution: "@vue/shared@npm:3.4.21"
  checksum: 10c0/79cba4228c3c1769ba8024302d7dbebf6ed1b77fb2e7a69e635cdebaa1c18b409e9c27ce27ccbe3a98e702a7e2dae1b87754d87f0b29adfe2a8f9e1e7c7899d5
  languageName: node
  linkType: hard

"@vueuse/core@npm:^10.9.0":
  version: 10.9.0
  resolution: "@vueuse/core@npm:10.9.0"
  dependencies:
    "@types/web-bluetooth": "npm:^0.0.20"
    "@vueuse/metadata": "npm:10.9.0"
    "@vueuse/shared": "npm:10.9.0"
    vue-demi: "npm:>=0.14.7"
  checksum: 10c0/d5c90a5bdd2dc5123a594e6ff9bc96f70f8f062fe9749d1c194d15516acfee7dd2f99ef9d1c6b06a9d18e7a13a0287ff0e5ee8a2565a04810196c90bb13daf91
  languageName: node
  linkType: hard

"@vueuse/core@npm:^9.1.0":
  version: 9.13.0
  resolution: "@vueuse/core@npm:9.13.0"
  dependencies:
    "@types/web-bluetooth": "npm:^0.0.16"
    "@vueuse/metadata": "npm:9.13.0"
    "@vueuse/shared": "npm:9.13.0"
    vue-demi: "npm:*"
  checksum: 10c0/59791dbfad5725810139c22adb4d8266ca9de419a4b252cb99f1b2a0bdb2f500988a7aabd42583c255fa45499ebb43dafc9d6ddc45fdf09ef15fdadd02958f42
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:10.9.0":
  version: 10.9.0
  resolution: "@vueuse/metadata@npm:10.9.0"
  checksum: 10c0/7f647b2ae73ff86c4de658c84a51988f7cdfec1643e1e706d5b29034bd949e240cffacf52505ab8e9223bb245f7bafe7f7bfa5c32702172ba29afc4aef96b475
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:9.13.0":
  version: 9.13.0
  resolution: "@vueuse/metadata@npm:9.13.0"
  checksum: 10c0/c2a8a85946f382b9b51b4e96f17f0913091e7c271fbde565b59d3c4fd8f67f2f34778e002d65dd78c420700781e725c05d72cb65acec9c773a423116e8d49cd4
  languageName: node
  linkType: hard

"@vueuse/shared@npm:10.9.0":
  version: 10.9.0
  resolution: "@vueuse/shared@npm:10.9.0"
  dependencies:
    vue-demi: "npm:>=0.14.7"
  checksum: 10c0/55593c325b9dc15cfe9eaf84ca2a757a0c418491be56760f5560a641dfe2579c72cb55ccd04ead71217a97fc6b17ad7b7b7b9177b376bc6967174121707d1c49
  languageName: node
  linkType: hard

"@vueuse/shared@npm:9.13.0":
  version: 9.13.0
  resolution: "@vueuse/shared@npm:9.13.0"
  dependencies:
    vue-demi: "npm:*"
  checksum: 10c0/22c453dc3c9ccd389e32d4dcfb6e6facfbb29860376c0b1c4d40d2745edd733857d1a1f82835c1d698dedf0c9f697bd9d1265e4e70a6702c85b61cc295bd7352
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.6, @xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 10c0/f742a5a107473946f426c691c08daba61a1d15942616f300b5d32fd735be88fef5cba24201757b6c407fd564555fb48c751cfa33519b2605c8a7aadd22baf372
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.11.3, acorn@npm:^8.9.0":
  version: 8.11.3
  resolution: "acorn@npm:8.11.3"
  bin:
    acorn: bin/acorn
  checksum: 10c0/3ff155f8812e4a746fee8ecff1f227d527c4c45655bb1fad6347c3cb58e46190598217551b1500f18542d2bbe5c87120cb6927f5a074a59166fbdd9468f0a299
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0, agent-base@npm:^7.1.1":
  version: 7.1.1
  resolution: "agent-base@npm:7.1.1"
  dependencies:
    debug: "npm:^4.3.4"
  checksum: 10c0/e59ce7bed9c63bf071a30cc471f2933862044c97fd9958967bfe22521d7a0f601ce4ed5a8c011799d0c726ca70312142ae193bbebb60f576b52be19d4a363b50
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10c0/a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.4.1":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 10c0/0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.0, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 10c0/cbe16dbd2c6b2735d1df7976a7070dd277326434f0212f43abf6d87674095d247968209babdaad31bb00882fa68807256ba9be340eec2f1004de14ca75f52a08
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"app-builder-bin@npm:4.0.0":
  version: 4.0.0
  resolution: "app-builder-bin@npm:4.0.0"
  checksum: 10c0/9df57b2460aa058971c8619132c4ab5b7b4572449c8f5b562e44c9d6c1c73ec7284f4d1e170549c42eef53cd9e0b7579409fb49fba862ab4d3050433579ef14c
  languageName: node
  linkType: hard

"app-builder-lib@npm:24.13.3":
  version: 24.13.3
  resolution: "app-builder-lib@npm:24.13.3"
  dependencies:
    "@develar/schema-utils": "npm:~2.6.5"
    "@electron/notarize": "npm:2.2.1"
    "@electron/osx-sign": "npm:1.0.5"
    "@electron/universal": "npm:1.5.1"
    "@malept/flatpak-bundler": "npm:^0.4.0"
    "@types/fs-extra": "npm:9.0.13"
    async-exit-hook: "npm:^2.0.1"
    bluebird-lst: "npm:^1.0.9"
    builder-util: "npm:24.13.1"
    builder-util-runtime: "npm:9.2.4"
    chromium-pickle-js: "npm:^0.2.0"
    debug: "npm:^4.3.4"
    ejs: "npm:^3.1.8"
    electron-publish: "npm:24.13.1"
    form-data: "npm:^4.0.0"
    fs-extra: "npm:^10.1.0"
    hosted-git-info: "npm:^4.1.0"
    is-ci: "npm:^3.0.0"
    isbinaryfile: "npm:^5.0.0"
    js-yaml: "npm:^4.1.0"
    lazy-val: "npm:^1.0.5"
    minimatch: "npm:^5.1.1"
    read-config-file: "npm:6.3.2"
    sanitize-filename: "npm:^1.6.3"
    semver: "npm:^7.3.8"
    tar: "npm:^6.1.12"
    temp-file: "npm:^3.4.0"
  peerDependencies:
    dmg-builder: 24.13.3
    electron-builder-squirrel-windows: 24.13.3
  checksum: 10c0/a3ff90e63f738e8a0d8a2f52fc336cd130adf1c00c7fe8e575a3b2bbb23b733135d530589882b45735fb8e43ff9ad8ed19d5992b4ac81029371efbb4bc6ffdb2
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"argparse@npm:~1.0.3":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 10c0/b194b9d50c3a8f872ee85ab110784911e696a4d49f7ee6fc5fb63216dedbefd2c55999c70cb2eaeb4cf4a0e0338b44e9ace3627117b5bf0d42460e9132f21b91
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10c0/f63d439cc383db1b9c5c6080d1e240bd14dae745f15d11ec5da863e182bbeca70df6c8191cffef5deba0b566ef98834610a68be79ac6379c95eeb26e1b310e25
  languageName: node
  linkType: hard

"async-exit-hook@npm:^2.0.1":
  version: 2.0.1
  resolution: "async-exit-hook@npm:2.0.1"
  checksum: 10c0/81407a440ef0aab328df2369f1a9d957ee53e9a5a43e3b3dcb2be05151a68de0e4ff5e927f4718c88abf85800731f5b3f69a47a6642ce135f5e7d43ca0fce41d
  languageName: node
  linkType: hard

"async-validator@npm:^4.2.5":
  version: 4.2.5
  resolution: "async-validator@npm:4.2.5"
  checksum: 10c0/0ec09ee388aae5f6b037a320049a369b681ca9b341b28e2693e50e89b5c4c64c057a2c57f9fc1c18dd020823809d8af4b72b278e0a7a872c9e3accd5c4c3ce3a
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.5
  resolution: "async@npm:3.2.5"
  checksum: 10c0/1408287b26c6db67d45cb346e34892cee555b8b59e6c68e6f8c3e495cad5ca13b4f218180e871f3c2ca30df4ab52693b66f2f6ff43644760cab0b2198bda79c1
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"axios@npm:^1.6.8":
  version: 1.6.8
  resolution: "axios@npm:1.6.8"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/0f22da6f490335479a89878bc7d5a1419484fbb437b564a80c34888fc36759ae4f56ea28d55a191695e5ed327f0bad56e7ff60fb6770c14d1be6501505d47ab9
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-js@npm:0.0.8":
  version: 0.0.8
  resolution: "base64-js@npm:0.0.8"
  checksum: 10c0/60f02a9fdbbbb251c0a1064834d4062f5a3c4237edd9f0313282d75980a80ce303316795f7a80c8e240e524169644d88445ec0697b03f81ab9f4458090979375
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"big.js@npm:^6.2.1":
  version: 6.2.1
  resolution: "big.js@npm:6.2.1"
  checksum: 10c0/87734c70e6c668f3bc29d84e99b8a97392debbea258d260419c847039d63251fddb8675019c751ceb54ae6fa5f796c5221c4c08526b316149b14ad78dc0d7db1
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bluebird-lst@npm:^1.0.9":
  version: 1.0.9
  resolution: "bluebird-lst@npm:1.0.9"
  dependencies:
    bluebird: "npm:^3.5.5"
  checksum: 10c0/701eef18f37a53277adeacb21281a70fc4536e521fe0deb665a284f4d8480056c6932988c3dfa6a0c46b4d55f4599f716a15873f30ed5fc2470928093438f87e
  languageName: node
  linkType: hard

"bluebird@npm:^3.5.5":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 10c0/680de03adc54ff925eaa6c7bb9a47a0690e8b5de60f4792604aae8ed618c65e6b63a7893b57ca924beaf53eee69c5af4f8314148c08124c550fe1df1add897d2
  languageName: node
  linkType: hard

"bluebird@npm:~3.4.0":
  version: 3.4.7
  resolution: "bluebird@npm:3.4.7"
  checksum: 10c0/ac7e3df09a433b985a0ba61a0be4fc23e3874bf62440ffbca2f275a8498b00c11336f1f633631f38419b2c842515473985f9c4aaa9e4c9b36105535026d94144
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"boolean@npm:^3.0.1":
  version: 3.2.0
  resolution: "boolean@npm:3.2.0"
  checksum: 10c0/6a0dc9668f6f3dda42a53c181fcbdad223169c8d87b6c4011b87a8b14a21770efb2934a778f063d7ece17280f8c06d313c87f7b834bb1dd526a867ffcd00febf
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: "npm:^7.0.1"
  checksum: 10c0/321b4d675791479293264019156ca322163f02dc06e3c4cab33bb15cd43d80b51efef69b0930cfde3acd63d126ebca24cd0544fa6f261e093a0fb41ab9dda381
  languageName: node
  linkType: hard

"browser-split@npm:0.0.1":
  version: 0.0.1
  resolution: "browser-split@npm:0.0.1"
  checksum: 10c0/c0ed77e8bf3494e6803681c578f5c0c0d050fbd041f608f0fe7ec23bdb98483ba6905dfa785786963b9b555aab04f95d6e15f8b4ecdd53a40215134a66ec3f39
  languageName: node
  linkType: hard

"browserslist@npm:^4.22.2":
  version: 4.23.0
  resolution: "browserslist@npm:4.23.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001587"
    electron-to-chromium: "npm:^1.4.668"
    node-releases: "npm:^2.0.14"
    update-browserslist-db: "npm:^1.0.13"
  bin:
    browserslist: cli.js
  checksum: 10c0/8e9cc154529062128d02a7af4d8adeead83ca1df8cd9ee65a88e2161039f3d68a4d40fea7353cab6bae4c16182dec2fdd9a1cf7dc2a2935498cee1af0e998943
  languageName: node
  linkType: hard

"buffer-crc32@npm:~0.2.3":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: 10c0/cb0a8ddf5cf4f766466db63279e47761eb825693eeba6a5a95ee4ec8cb8f81ede70aa7f9d8aeec083e781d47154290eb5d4d26b3f7a465ec57fb9e7d59c47150
  languageName: node
  linkType: hard

"buffer-equal@npm:^1.0.0":
  version: 1.0.1
  resolution: "buffer-equal@npm:1.0.1"
  checksum: 10c0/578f03cc9458f9151f68478ab80ebee99a4203de0647a47b491aa3d5fb821938cb4139119a2dae1a1ef9ed5506e0eee4d6a37178efbf2e2e0ee3a9886898fffd
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.1.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"builder-util-runtime@npm:9.2.4":
  version: 9.2.4
  resolution: "builder-util-runtime@npm:9.2.4"
  dependencies:
    debug: "npm:^4.3.4"
    sax: "npm:^1.2.4"
  checksum: 10c0/858978ffced52935db9c13139235679933616095459796ef2969e86641be53edec8c07bf14cfb42516e017124c653839aa4f66451dd5b41ba84728f54a167c64
  languageName: node
  linkType: hard

"builder-util@npm:24.13.1":
  version: 24.13.1
  resolution: "builder-util@npm:24.13.1"
  dependencies:
    7zip-bin: "npm:~5.2.0"
    "@types/debug": "npm:^4.1.6"
    app-builder-bin: "npm:4.0.0"
    bluebird-lst: "npm:^1.0.9"
    builder-util-runtime: "npm:9.2.4"
    chalk: "npm:^4.1.2"
    cross-spawn: "npm:^7.0.3"
    debug: "npm:^4.3.4"
    fs-extra: "npm:^10.1.0"
    http-proxy-agent: "npm:^5.0.0"
    https-proxy-agent: "npm:^5.0.1"
    is-ci: "npm:^3.0.0"
    js-yaml: "npm:^4.1.0"
    source-map-support: "npm:^0.5.19"
    stat-mode: "npm:^1.0.0"
    temp-file: "npm:^3.4.0"
  checksum: 10c0/4f6654a73eaca8cb2a6d83e5a73318d47843df72d0eaa28392cdc0e38d8e343b91c6019bae0274eba4dfde9e82abd94e0eee75157f1fba7e8a8590631624987a
  languageName: node
  linkType: hard

"cac@npm:^6.7.14":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 10c0/4ee06aaa7bab8981f0d54e5f5f9d4adcd64058e9697563ce336d8a3878ed018ee18ebe5359b2430eceae87e0758e62ea2019c3f52ae6e211b1bd2e133856cd10
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.2
  resolution: "cacache@npm:18.0.2"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: 10c0/7992665305cc251a984f4fdbab1449d50e88c635bc43bf2785530c61d239c61b349e5734461baa461caaee65f040ab14e2d58e694f479c0810cffd181ba5eabc
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 10c0/a6547fb4954b318aa831cbdd2f7b376824bc784fb1fa67610e4147099e3074726072d9af89f12efb69121415a0e1f2918a8ddd4aafcbcf4e91fbeef4a59cd42c
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.4
  resolution: "cacheable-request@npm:7.0.4"
  dependencies:
    clone-response: "npm:^1.0.2"
    get-stream: "npm:^5.1.0"
    http-cache-semantics: "npm:^4.0.0"
    keyv: "npm:^4.0.0"
    lowercase-keys: "npm:^2.0.0"
    normalize-url: "npm:^6.0.1"
    responselike: "npm:^2.0.0"
  checksum: 10c0/0834a7d17ae71a177bc34eab06de112a43f9b5ad05ebe929bec983d890a7d9f2bc5f1aa8bb67ea2b65e07a3bc74bea35fa62dd36dbac52876afe36fdcf83da41
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 10c0/4c9ac55efd356d37ac483bad3093758236ab686192751d1c9daa43188cc5a07b09bd431eb7458a4efd9ca22424bba23253e7b353feb35d7c749ba040de2385fb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001587":
  version: 1.0.30001609
  resolution: "caniuse-lite@npm:1.0.30001609"
  checksum: 10c0/a7631f6c9a741c7cb16100e115572f70e3d28622af9749891b7285d392113fcb8683ba2ded29e2e9d8e9fa215139d06d2bf15151b0b83df3bbfbbf2b495b74e5
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0, chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chromium-pickle-js@npm:^0.2.0":
  version: 0.2.0
  resolution: "chromium-pickle-js@npm:0.2.0"
  checksum: 10c0/0a95bd280acdf05b0e08fa1a0e1db58c815dd24e92d639add8f494d23a8a49c26e4829721224d68f2f0e57a69047714db29bcff6deb5d029332321223416cb29
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10c0/6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"cjk-regex@npm:^3.1.0":
  version: 3.1.0
  resolution: "cjk-regex@npm:3.1.0"
  dependencies:
    regexp-util: "npm:^2.0.0"
    unicode-regex: "npm:^4.0.0"
  checksum: 10c0/985bdcd1649c739a70e07cc8829519ccb9b2b74c8c43fa60de674b7dfc0937b6327dca5f3228d76eadec0a262a2912289fd3dc1c1a5479148c52cff8801a640a
  languageName: node
  linkType: hard

"classnames@npm:2.x, classnames@npm:^2.2.1, classnames@npm:^2.2.5, classnames@npm:^2.2.6, classnames@npm:^2.3.1, classnames@npm:^2.3.2":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: 10c0/afff4f77e62cea2d79c39962980bf316bacb0d7c49e13a21adaadb9221e1c6b9d3cdb829d8bb1b23c406f4e740507f37e1dcf506f7e3b7113d17c5bab787aa69
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10c0/1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"cli-truncate@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-truncate@npm:2.1.0"
  dependencies:
    slice-ansi: "npm:^3.0.0"
    string-width: "npm:^4.2.0"
  checksum: 10c0/dfaa3df675bcef7a3254773de768712b590250420345a4c7ac151f041a4bacb4c25864b1377bee54a39b5925a030c00eabf014e312e3a4ac130952ed3b3879e9
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: "npm:^1.0.0"
  checksum: 10c0/06a2b611824efb128810708baee3bd169ec9a1bf5976a5258cd7eb3f7db25f00166c6eee5961f075c7e38e194f373d4fdf86b8166ad5b9c7e82bbd2e333a6087
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10c0/34dead8bee24f5e96f6e7937d711978380647e936a22e76380290e35486afd8634966ce300fc4b74a32f3762c7d4c0303f442c3e259f4ce02374eb0c82834f27
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.1.0
  resolution: "clsx@npm:2.1.0"
  checksum: 10c0/c09c00ad14f638366ca814097e6cab533dfa1972a358da5b557be487168acbb25b4c1395e89ffa842a8a61ba87a462d2b4885bc9d4f8410b598f3cb339599cdb
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.1.4, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"colorette@npm:^2.0.20":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10c0/e94116ff33b0ff56f3b83b9ace895e5bf87c2a7a47b3401b8c3f3226e050d5ef76cf4072fb3325f9dc24d1698f9b730baf4e05eeaf861d74a1883073f4c98a40
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^5.0.0":
  version: 5.1.0
  resolution: "commander@npm:5.1.0"
  checksum: 10c0/da9d71dbe4ce039faf1fe9eac3771dca8c11d66963341f62602f7b66e36d2a3f8883407af4f9a37b1db1a55c59c0c1325f186425764c2e963dc1d67aec2a4b6d
  languageName: node
  linkType: hard

"compare-version@npm:^0.1.2":
  version: 0.1.2
  resolution: "compare-version@npm:0.1.2"
  checksum: 10c0/f38b853cf0d244c0af5f444409abde3d2198cd97312efa1dbc4ab41b520009327c2a63db59bbaf2d69288eff6167ef22be9788dc5476157d073ecdff1a8eeb2d
  languageName: node
  linkType: hard

"computeds@npm:^0.0.1":
  version: 0.0.1
  resolution: "computeds@npm:0.0.1"
  checksum: 10c0/8a8736f1f43e4a99286519785d71a10ece8f444a2fa1fc2fe1f03dedf63f3477b45094002c85a2826f7631759c9f5a00b4ace47456997f253073fc525e8983de
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"config-file-ts@npm:^0.2.4":
  version: 0.2.6
  resolution: "config-file-ts@npm:0.2.6"
  dependencies:
    glob: "npm:^10.3.10"
    typescript: "npm:^5.3.3"
  checksum: 10c0/ae4c213550aaa1c50671938ff0106495b7610b99a810fed9e35b5ca94dd49fcdd4f22cf132d3368cd19d67e37fff761352d63559f0e8866105a877f476a07be7
  languageName: node
  linkType: hard

"consola@npm:^3.2.3":
  version: 3.2.3
  resolution: "consola@npm:3.2.3"
  checksum: 10c0/c606220524ec88a05bb1baf557e9e0e04a0c08a9c35d7a08652d99de195c4ddcb6572040a7df57a18ff38bbc13ce9880ad032d56630cef27bef72768ef0ac078
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 10c0/980a37a93956d0de8a828ce508f9b9e3317039d68922ca79995421944146700e4aaf490a6dbfebcb1c5292a7184600c7710b957d724be1e37b8254c6bc0fe246
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"crc@npm:^3.8.0":
  version: 3.8.0
  resolution: "crc@npm:3.8.0"
  dependencies:
    buffer: "npm:^5.1.0"
  checksum: 10c0/1a0da36e5f95b19cd2a7b2eab5306a08f1c47bdd22da6f761ab764e2222e8e90a877398907cea94108bd5e41a6d311ea84d7914eaca67da2baa4050bd6384b3d
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/5738c312387081c98d69c98e105b6327b069197f864a60593245d64c8089c8a0a744e16349281210d56835bb9274130d825a78b2ad6853ca13cfbeffc0c31750
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: "npm:2.0.30"
    source-map-js: "npm:^1.0.1"
  checksum: 10c0/6f8c1a11d5e9b14bf02d10717fc0351b66ba12594166f65abfbd8eb8b5b490dd367f5c7721db241a3c792d935fc6751fbc09f7e1598d421477ad9fadc30f4f24
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10c0/6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.10, dayjs@npm:^1.11.3":
  version: 1.11.10
  resolution: "dayjs@npm:1.11.10"
  checksum: 10c0/4de9af50639d47df87f2e15fa36bb07e0f9ed1e9c52c6caa1482788ee9a384d668f1dbd00c54f82aaab163db07d61d2899384b8254da3a9184fc6deca080e2fe
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 10c0/7058ce58abd6dfc123dd204e36be3797abd419b59482a634605420f47ae97639d0c183ec5d1b904f308a01033f473673897afc2bd59bc620ebf1658763ef4291
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10c0/bd89d23141b96d80577e70c54fb226b2f40e74a6817652b80a116d7befb8758261ad073a8895648a29cc0a5947021ab66705cb542fa9c143c82022b27c5b175e
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 10c0/625ce28e1b5ad10cf77057b9a6a727bf84780c17660f6644dab61dd34c23de3001f03cedc401f7d30a4ed9965c2e8a7336e220a329146f2cf85d4eddea429782
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"defu@npm:^6.1.4":
  version: 6.1.4
  resolution: "defu@npm:6.1.4"
  checksum: 10c0/2d6cc366262dc0cb8096e429368e44052fdf43ed48e53ad84cc7c9407f890301aa5fcb80d0995abaaf842b3949f154d060be4160f7a46cb2bc2f7726c81526f5
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"destr@npm:^2.0.3":
  version: 2.0.3
  resolution: "destr@npm:2.0.3"
  checksum: 10c0/10e7eff5149e2839a4dd29a1e9617c3c675a3b53608d78d74fc6f4abc31daa977e6de08e0eea78965527a0d5a35467ae2f9624e0a4646d54aa1162caa094473e
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 10c0/f039f601790f2e9d4654e499913259a798b1f5246ae24f86ab5e8bd4aaf3bce50484234c494f11fb00aecb0c6e2733aa7b1cf3f530865640b65fbbd65b2c4e09
  languageName: node
  linkType: hard

"dingbat-to-unicode@npm:^1.0.1":
  version: 1.0.1
  resolution: "dingbat-to-unicode@npm:1.0.1"
  checksum: 10c0/4def812dadd17122929ad31df574539e4066d85d07b0f824fc70533ff44ce75733e25cdd5817b80604d0c6e6091159a51f9b67487b926611518718c796354e26
  languageName: node
  linkType: hard

"dir-compare@npm:^3.0.0":
  version: 3.3.0
  resolution: "dir-compare@npm:3.3.0"
  dependencies:
    buffer-equal: "npm:^1.0.0"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/bafcb225a629994f1d5808eeb11b3b8adf49356c86bdc87588b30f22f8709044166e3b368c050b6a72bc54397c52d7e8013d5b5741306ee3511bf6b924f66212
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"dmg-builder@npm:24.13.3":
  version: 24.13.3
  resolution: "dmg-builder@npm:24.13.3"
  dependencies:
    app-builder-lib: "npm:24.13.3"
    builder-util: "npm:24.13.1"
    builder-util-runtime: "npm:9.2.4"
    dmg-license: "npm:^1.0.11"
    fs-extra: "npm:^10.1.0"
    iconv-lite: "npm:^0.6.2"
    js-yaml: "npm:^4.1.0"
  dependenciesMeta:
    dmg-license:
      optional: true
  checksum: 10c0/1eb9e2d1396a9072d156657e537191ceb9d468e4884ef501ac58931f8d423e02ec48644e417cbb52e916d738d691d8a7254fe487b4882848527d58b8658b94f6
  languageName: node
  linkType: hard

"dmg-license@npm:^1.0.11":
  version: 1.0.11
  resolution: "dmg-license@npm:1.0.11"
  dependencies:
    "@types/plist": "npm:^3.0.1"
    "@types/verror": "npm:^1.10.3"
    ajv: "npm:^6.10.0"
    crc: "npm:^3.8.0"
    iconv-corefoundation: "npm:^1.1.7"
    plist: "npm:^3.0.4"
    smart-buffer: "npm:^4.0.2"
    verror: "npm:^1.10.0"
  bin:
    dmg-license: bin/dmg-license.js
  conditions: os=darwin
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/c96bdccabe9d62ab6fea9399fdff04a66e6563c1d6fb3a3a063e8d53c3bb136ba63e84250bbf63d00086a769ad53aef92d2bd483f03f837fc97b71cbee6b2520
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10c0/f735074d66dd759b36b158fa26e9d00c9388ee0e8c9b16af941c38f014a37fc80782de83afefd621681b19ac0501034b4f1c4a3bff5caa1b8667f0212b5e124c
  languageName: node
  linkType: hard

"dom-serializer@npm:0":
  version: 0.2.2
  resolution: "dom-serializer@npm:0.2.2"
  dependencies:
    domelementtype: "npm:^2.0.1"
    entities: "npm:^2.0.0"
  checksum: 10c0/5cb595fb77e1a23eca56742f47631e6f4af66ce1982c7ed28b3d0ef21f1f50304c067adc29d3eaf824c572be022cee88627d0ac9b929408f24e923f3c7bed37b
  languageName: node
  linkType: hard

"dom-walk@npm:^0.1.0":
  version: 0.1.2
  resolution: "dom-walk@npm:0.1.2"
  checksum: 10c0/4d2ad9062a9423d890f8577aa202b597a6b85f9489bdde656b9443901b8b322b289655c3affefc58ec2e41931e0828dfee0a1d2db6829a607d76def5901fc5a9
  languageName: node
  linkType: hard

"domelementtype@npm:1, domelementtype@npm:^1.3.1":
  version: 1.3.1
  resolution: "domelementtype@npm:1.3.1"
  checksum: 10c0/6d4f5761060a21eaf3c96545501e9d188745c7e1c31b8d141bf15d8748feeadba868f4ea32877751b8678b286fb1afbe6ae905ca3fb8f0214d8322e482cdbec0
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^2.3.0":
  version: 2.4.2
  resolution: "domhandler@npm:2.4.2"
  dependencies:
    domelementtype: "npm:1"
  checksum: 10c0/6670cab73e97e3c6771dcf22b537db3f6a0be0ad6b370f03bb5f1b585d3b563d326787fdabe1190b7ca9d81c804e9b3f8a1431159c27c44f6c05f94afa92be2d
  languageName: node
  linkType: hard

"domutils@npm:^1.5.1":
  version: 1.7.0
  resolution: "domutils@npm:1.7.0"
  dependencies:
    dom-serializer: "npm:0"
    domelementtype: "npm:1"
  checksum: 10c0/437fcd2d6d6be03f488152e73c6f953e289c58496baa22be9626b2b46f9cfd40486ae77d144487ff6b102929a3231cdb9a8bf8ef485fb7b7c30c985daedc77eb
  languageName: node
  linkType: hard

"dotenv-expand@npm:^5.1.0":
  version: 5.1.0
  resolution: "dotenv-expand@npm:5.1.0"
  checksum: 10c0/24ac633de853ef474d0421cc639328b7134109c8dc2baaa5e3afb7495af5e9237136d7e6971e55668e4dce915487eb140967cdd2b3e99aa439e0f6bf8b56faeb
  languageName: node
  linkType: hard

"dotenv@npm:^9.0.2":
  version: 9.0.2
  resolution: "dotenv@npm:9.0.2"
  checksum: 10c0/535f04d59e0bf58fe0c7966886eff42fb5e0227e2f7bfa38d37439bbf6b3c25d1b085bd235c9b98e7e9a032b1cd310904366e5588b320c29335d359660fab0d4
  languageName: node
  linkType: hard

"duck@npm:^0.1.12":
  version: 0.1.12
  resolution: "duck@npm:0.1.12"
  dependencies:
    underscore: "npm:^1.13.1"
  checksum: 10c0/dfbe163481cae832c783016c5026f228e95e32bd5dfc9636607d981faf5b1e7aaa5ac27cf181ceefba6a01d42b54239908dee41ae3af2e7cea42e4fa925dbff3
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 10c0/c57bcd4bdf7e623abab2df43a7b5b23d18152154529d166c1e0da6bee341d84c432d157d7e97b32fecb1bf3a8b8857dd85ed81a915789f550637ed25b8e64fc2
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ejs@npm:^3.1.8":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"electron-builder@npm:^24.9.1":
  version: 24.13.3
  resolution: "electron-builder@npm:24.13.3"
  dependencies:
    app-builder-lib: "npm:24.13.3"
    builder-util: "npm:24.13.1"
    builder-util-runtime: "npm:9.2.4"
    chalk: "npm:^4.1.2"
    dmg-builder: "npm:24.13.3"
    fs-extra: "npm:^10.1.0"
    is-ci: "npm:^3.0.0"
    lazy-val: "npm:^1.0.5"
    read-config-file: "npm:6.3.2"
    simple-update-notifier: "npm:2.0.0"
    yargs: "npm:^17.6.2"
  bin:
    electron-builder: cli.js
    install-app-deps: install-app-deps.js
  checksum: 10c0/497b83ef6c95e1756eb11f824cc52e434b1a83c3abe24df6958a348b6faf77b274892a09ca21ef60a241aac8d55804b321e443471fc8afdbac2dc563dea321c5
  languageName: node
  linkType: hard

"electron-dl@npm:^3.5.2":
  version: 3.5.2
  resolution: "electron-dl@npm:3.5.2"
  dependencies:
    ext-name: "npm:^5.0.0"
    pupa: "npm:^2.0.1"
    unused-filename: "npm:^2.1.0"
  checksum: 10c0/1141188c66b187917050cca1954d2abc9922b6687efc81d85a4e9c8c14948a64791c338d9713f48133666db71fd464113d4661e886ed92f2f469eda5c6eaafa1
  languageName: node
  linkType: hard

"electron-publish@npm:24.13.1":
  version: 24.13.1
  resolution: "electron-publish@npm:24.13.1"
  dependencies:
    "@types/fs-extra": "npm:^9.0.11"
    builder-util: "npm:24.13.1"
    builder-util-runtime: "npm:9.2.4"
    chalk: "npm:^4.1.2"
    fs-extra: "npm:^10.1.0"
    lazy-val: "npm:^1.0.5"
    mime: "npm:^2.5.2"
  checksum: 10c0/d31e14f836c7cb4e56f897fcebedbe4c13c32974688d5c3a77681df882a24229188de4c0d46e9ae4981df6f308889118671a6ef5279105f8e6b920c34e1fcc2c
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.668":
  version: 1.4.736
  resolution: "electron-to-chromium@npm:1.4.736"
  checksum: 10c0/f3acb515dcb9333b318f9a8ee80a0c3975162da6cc45d9c00152e0de7004b2bfe1246fe6cdf0c41a1b7016780bd4324aff848830aba4227cb55480c1aa32d248
  languageName: node
  linkType: hard

"electron-updater@npm:^6.1.7":
  version: 6.2.1
  resolution: "electron-updater@npm:6.2.1"
  dependencies:
    builder-util-runtime: "npm:9.2.4"
    fs-extra: "npm:^10.1.0"
    js-yaml: "npm:^4.1.0"
    lazy-val: "npm:^1.0.5"
    lodash.escaperegexp: "npm:^4.1.2"
    lodash.isequal: "npm:^4.5.0"
    semver: "npm:^7.3.8"
    tiny-typed-emitter: "npm:^2.1.0"
  checksum: 10c0/b376e13bf2b4675ca853c4164a4caf9454d0d41e797c0f8fd011d66693d4eb5ece020953f3d06c0a63d9d5077a9ae9e8447f26c602da317edb1521c4bd99e2f8
  languageName: node
  linkType: hard

"electron-vite@npm:^2.0.0":
  version: 2.1.0
  resolution: "electron-vite@npm:2.1.0"
  dependencies:
    "@babel/core": "npm:^7.23.5"
    "@babel/plugin-transform-arrow-functions": "npm:^7.23.3"
    cac: "npm:^6.7.14"
    esbuild: "npm:^0.19.8"
    magic-string: "npm:^0.30.5"
    picocolors: "npm:^1.0.0"
  peerDependencies:
    "@swc/core": ^1.0.0
    vite: ^4.0.0 || ^5.0.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
  bin:
    electron-vite: bin/electron-vite.js
  checksum: 10c0/0edb6ed6ca56da142710b7b722f39fe6a95fcb48451aa1a083a72bc9675e680d691b71c763c42edcb01ec6c0b4e0d33db67e342e36a7cadf92011809a064da64
  languageName: node
  linkType: hard

"electron@npm:^28.2.0":
  version: 28.3.0
  resolution: "electron@npm:28.3.0"
  dependencies:
    "@electron/get": "npm:^2.0.0"
    "@types/node": "npm:^18.11.18"
    extract-zip: "npm:^2.0.1"
  bin:
    electron: cli.js
  checksum: 10c0/e02c0ba6e9664d668da10a649dab29ac4ac24f0c82f4ad753c04e6e120936d24094acb28b430d0402ea4fafceb998471285c33371fd08689feab008ffe9cabab
  languageName: node
  linkType: hard

"element-plus@npm:^2.6.3":
  version: 2.7.0
  resolution: "element-plus@npm:2.7.0"
  dependencies:
    "@ctrl/tinycolor": "npm:^3.4.1"
    "@element-plus/icons-vue": "npm:^2.3.1"
    "@floating-ui/dom": "npm:^1.0.1"
    "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash": "npm:^4.14.182"
    "@types/lodash-es": "npm:^4.17.6"
    "@vueuse/core": "npm:^9.1.0"
    async-validator: "npm:^4.2.5"
    dayjs: "npm:^1.11.3"
    escape-html: "npm:^1.0.3"
    lodash: "npm:^4.17.21"
    lodash-es: "npm:^4.17.21"
    lodash-unified: "npm:^1.0.2"
    memoize-one: "npm:^6.0.0"
    normalize-wheel-es: "npm:^1.2.0"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10c0/38512e46b9d4179f5f9e6b850d4ef75270c5640146282425cea9de870f7d6d87d8251cec9c61e37a7d8fd367f8074986b8a5c767bb403877dd6a44606f134f39
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"ent@npm:^2.0.0":
  version: 2.2.0
  resolution: "ent@npm:2.2.0"
  checksum: 10c0/d12c504d93afb8b22551323f78f60f0a2660289cf2de2210bdd2fdb07ac204956da23510a7711bf48079aa0aa726e21724224de6c6289120ddcf27652b30cb17
  languageName: node
  linkType: hard

"entities@npm:^1.1.1":
  version: 1.1.2
  resolution: "entities@npm:1.1.2"
  checksum: 10c0/5b12fa8c4fb942f88af6f8791bbe7be0a59ebd91c8933cee091d94455efd1eeb200418c7b1bc8dd0f74cdd4db8cf4538eb043db14cfd1919130c25d8c6095215
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 10c0/7fba6af1f116300d2ba1c5673fc218af1961b20908638391b4e1e6d5850314ee2ac3ec22d741b3a8060479911c99305164aed19b6254bde75e7e6b1b2c3f3aa3
  languageName: node
  linkType: hard

"entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error@npm:^4.3.0":
  version: 4.4.0
  resolution: "error@npm:4.4.0"
  dependencies:
    camelize: "npm:^1.0.0"
    string-template: "npm:~0.2.0"
    xtend: "npm:~4.0.0"
  checksum: 10c0/6aecddafbc14db142a47df295b2d832630dd4af6ec62a52dbf00eefa7adea249a3182a6c920c5e24974204f7234d02ed2b073f6f8eb164ba2e620d93218e2e66
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-define-property@npm:1.0.0"
  dependencies:
    get-intrinsic: "npm:^1.2.4"
  checksum: 10c0/6bf3191feb7ea2ebda48b577f69bdfac7a2b3c9bcf97307f55fd6ef1bbca0b49f0c219a935aca506c993d8c5d8bddd937766cb760cd5e5a1071351f2df9f9aa4
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es6-error@npm:^4.1.1":
  version: 4.1.1
  resolution: "es6-error@npm:4.1.1"
  checksum: 10c0/357663fb1e845c047d548c3d30f86e005db71e122678f4184ced0693f634688c3f3ef2d7de7d4af732f734de01f528b05954e270f06aa7d133679fb9fe6600ef
  languageName: node
  linkType: hard

"esbuild@npm:^0.19.8":
  version: 0.19.12
  resolution: "esbuild@npm:0.19.12"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.19.12"
    "@esbuild/android-arm": "npm:0.19.12"
    "@esbuild/android-arm64": "npm:0.19.12"
    "@esbuild/android-x64": "npm:0.19.12"
    "@esbuild/darwin-arm64": "npm:0.19.12"
    "@esbuild/darwin-x64": "npm:0.19.12"
    "@esbuild/freebsd-arm64": "npm:0.19.12"
    "@esbuild/freebsd-x64": "npm:0.19.12"
    "@esbuild/linux-arm": "npm:0.19.12"
    "@esbuild/linux-arm64": "npm:0.19.12"
    "@esbuild/linux-ia32": "npm:0.19.12"
    "@esbuild/linux-loong64": "npm:0.19.12"
    "@esbuild/linux-mips64el": "npm:0.19.12"
    "@esbuild/linux-ppc64": "npm:0.19.12"
    "@esbuild/linux-riscv64": "npm:0.19.12"
    "@esbuild/linux-s390x": "npm:0.19.12"
    "@esbuild/linux-x64": "npm:0.19.12"
    "@esbuild/netbsd-x64": "npm:0.19.12"
    "@esbuild/openbsd-x64": "npm:0.19.12"
    "@esbuild/sunos-x64": "npm:0.19.12"
    "@esbuild/win32-arm64": "npm:0.19.12"
    "@esbuild/win32-ia32": "npm:0.19.12"
    "@esbuild/win32-x64": "npm:0.19.12"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/0f2d21ffe24ebead64843f87c3aebe2e703a5ed9feb086a0728b24907fac2eb9923e4a79857d3df9059c915739bd7a870dd667972eae325c67f478b592b8582d
  languageName: node
  linkType: hard

"esbuild@npm:^0.20.1":
  version: 0.20.2
  resolution: "esbuild@npm:0.20.2"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.20.2"
    "@esbuild/android-arm": "npm:0.20.2"
    "@esbuild/android-arm64": "npm:0.20.2"
    "@esbuild/android-x64": "npm:0.20.2"
    "@esbuild/darwin-arm64": "npm:0.20.2"
    "@esbuild/darwin-x64": "npm:0.20.2"
    "@esbuild/freebsd-arm64": "npm:0.20.2"
    "@esbuild/freebsd-x64": "npm:0.20.2"
    "@esbuild/linux-arm": "npm:0.20.2"
    "@esbuild/linux-arm64": "npm:0.20.2"
    "@esbuild/linux-ia32": "npm:0.20.2"
    "@esbuild/linux-loong64": "npm:0.20.2"
    "@esbuild/linux-mips64el": "npm:0.20.2"
    "@esbuild/linux-ppc64": "npm:0.20.2"
    "@esbuild/linux-riscv64": "npm:0.20.2"
    "@esbuild/linux-s390x": "npm:0.20.2"
    "@esbuild/linux-x64": "npm:0.20.2"
    "@esbuild/netbsd-x64": "npm:0.20.2"
    "@esbuild/openbsd-x64": "npm:0.20.2"
    "@esbuild/sunos-x64": "npm:0.20.2"
    "@esbuild/win32-arm64": "npm:0.20.2"
    "@esbuild/win32-ia32": "npm:0.20.2"
    "@esbuild/win32-x64": "npm:0.20.2"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/66398f9fb2c65e456a3e649747b39af8a001e47963b25e86d9c09d2a48d61aa641b27da0ce5cad63df95ad246105e1d83e7fee0e1e22a0663def73b1c5101112
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.2
  resolution: "escalade@npm:3.1.2"
  checksum: 10c0/6b4adafecd0682f3aa1cd1106b8fff30e492c7015b178bc81b2d2f75106dabea6c6d6e8508fc491bd58e597c74abb0e8e2368f943ecb9393d4162e3c2f3cf287
  languageName: node
  linkType: hard

"escape-goat@npm:^2.0.0":
  version: 2.1.1
  resolution: "escape-goat@npm:2.1.1"
  checksum: 10c0/fc0ad656f89c05e86a9641a21bdc5ea37b258714c057430b68a834854fa3e5770cda7d41756108863fc68b1e36a0946463017b7553ac39eaaf64815be07816fc
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.0.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/6d332694b36bc9ac6fdb18d3ca2f6ac42afa2ad61f0493e89226950a7091e38981b66bac2b47ba39d15b73fff2cd32c78b850a9cf9eed9ca9a96bfb2f3a2f10d
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.0.0":
  version: 5.1.3
  resolution: "eslint-plugin-prettier@npm:5.1.3"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.8.6"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: "*"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10c0/f45d5fc1fcfec6b0cf038a7a65ddd10a25df4fe3f9e1f6b7f0d5100e66f046a26a2492e69ee765dddf461b93c114cf2e1eb18d4970aafa6f385448985c136e09
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:^9.20.1":
  version: 9.24.1
  resolution: "eslint-plugin-vue@npm:9.24.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    globals: "npm:^13.24.0"
    natural-compare: "npm:^1.4.0"
    nth-check: "npm:^2.1.1"
    postcss-selector-parser: "npm:^6.0.15"
    semver: "npm:^7.6.0"
    vue-eslint-parser: "npm:^9.4.2"
    xml-name-validator: "npm:^4.0.0"
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 10c0/a20f2004f3e4993d5abce4da94f05c8927a0f61d2179cfc78dc3ea0e597f9dfecddf33fae3ba24f9ead44ea22d7c7c79e622c75e188e524e8d62491a4a6e4d3b
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1, eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/613c267aea34b5a6d6c00514e8545ef1f1433108097e857225fed40d397dd6b1809dffd11c2fde23b37ca53d7bf935fe04d2a18e6fc932b31837b6ad67e1c116
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint@npm:^8.56.0":
  version: 8.57.0
  resolution: "eslint@npm:8.57.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.6.1"
    "@eslint/eslintrc": "npm:^2.1.4"
    "@eslint/js": "npm:8.57.0"
    "@humanwhocodes/config-array": "npm:^0.11.14"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@nodelib/fs.walk": "npm:^1.2.8"
    "@ungap/structured-clone": "npm:^1.2.0"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.2.2"
    eslint-visitor-keys: "npm:^3.4.3"
    espree: "npm:^9.6.1"
    esquery: "npm:^1.4.2"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    globals: "npm:^13.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    is-path-inside: "npm:^3.0.3"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    strip-ansi: "npm:^6.0.1"
    text-table: "npm:^0.2.0"
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/00bb96fd2471039a312435a6776fe1fd557c056755eaa2b96093ef3a8508c92c8775d5f754768be6b1dddd09fdd3379ddb231eeb9b6c579ee17ea7d68000a529
  languageName: node
  linkType: hard

"espree@npm:^9.3.1, espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10c0/1a2e9b4699b715347f62330bcc76aee224390c28bb02b31a3752e9d07549c473f5f986720483c6469cf3cfb3c9d05df612ffc69eb1ee94b54b739e67de9bb460
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0, esquery@npm:^1.4.2":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/a084bd049d954cc88ac69df30534043fb2aee5555b56246493f42f27d1e168f00d9e5d4192e46f10290d312dc30dc7d58994d61a609c579c1219d636996f9213
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10c0/53a6c54e2019b8c914dc395890153ffdc2322781acf4bd7d1a32d7aedc1710807bdcd866ac133903d5629ec601fbb50abe8c2e5553c7f5a0afdd9b6af6c945af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"ev-store@npm:^7.0.0":
  version: 7.0.0
  resolution: "ev-store@npm:7.0.0"
  dependencies:
    individual: "npm:^3.0.0"
  checksum: 10c0/a470c07142a34ebc1aff20291fafe56675f4231ac5077de2dd6df6134e257a603b45efd4865c2f368084e40b1ac8efcec1459a182ce4bcd52256ae70e1b3dd5d
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10c0/4ba5c00c506e6c786b4d6262cfbce90ddc14c10d4667e5c83ae993c9de88aa856033994dd2b35b83e8dc1170e224e66a319fa80adc4c32adcd2379bbc75da814
  languageName: node
  linkType: hard

"execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10c0/160456d2d647e6019640bd07111634d8c353038d9fa40176afb7cd49b0548bdae83b56d05e907c2cce2300b81cae35d800ef92fefb9d0208e190fa3b7d6bb579
  languageName: node
  linkType: hard

"ext-list@npm:^2.0.0":
  version: 2.2.2
  resolution: "ext-list@npm:2.2.2"
  dependencies:
    mime-db: "npm:^1.28.0"
  checksum: 10c0/bfdb435f333dccbf3f9698dc9d8e38eb47b42d756800bfafa9ec0c1c8aace877c40095baf36f691bcfd09bb88ed247c6e51596e75a158280fa19cf8588a7e258
  languageName: node
  linkType: hard

"ext-name@npm:^5.0.0":
  version: 5.0.0
  resolution: "ext-name@npm:5.0.0"
  dependencies:
    ext-list: "npm:^2.0.0"
    sort-keys-length: "npm:^1.0.0"
  checksum: 10c0/6750b34636bb6dca78e1bcc797615af68ecf50d62cf774624a32ee7879da99c949b5c41e8aa56ede4eb15c6abad6b1a8858d0934faab75ff6e2fd6f408debe18
  languageName: node
  linkType: hard

"extract-zip@npm:^2.0.1":
  version: 2.0.1
  resolution: "extract-zip@npm:2.0.1"
  dependencies:
    "@types/yauzl": "npm:^2.9.1"
    debug: "npm:^4.1.1"
    get-stream: "npm:^5.1.0"
    yauzl: "npm:^2.10.0"
  dependenciesMeta:
    "@types/yauzl":
      optional: true
  bin:
    extract-zip: cli.js
  checksum: 10c0/9afbd46854aa15a857ae0341a63a92743a7b89c8779102c3b4ffc207516b2019337353962309f85c66ee3d9092202a83cdc26dbf449a11981272038443974aee
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: 10c0/e10e2769985d0e9b6c7199b053a9957589d02e84de42832c295798cb422a025e6d4a92e0259c1fb4d07090f5bfde6b55fd9f880ac5855bd61d775f8ab75a7ab0
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2, fast-diff@npm:^1.3.0":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10c0/5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-equals@npm:^4.0.3":
  version: 4.0.3
  resolution: "fast-equals@npm:4.0.3"
  checksum: 10c0/87fd2609c945ee61e9ed4d041eb2a8f92723fc02884115f67e429dd858d880279e962334894f116b3e9b223f387d246e3db5424ae779287849015ddadbf5ff27
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10c0/42baad7b9cd40b63e42039132bde27ca2cb3a4950d0a0f9abe4639ea1aa9d3e3b40f98b1fe31cbc0cc17b664c9ea7447d911a152fa34ec5b72977b125a6fc845
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/1095f16cea45fb3beff558bb3afa74ca7a9250f5a670b65db7ed585f92b4b48381445cd328b3d87323da81e43232b5d5978a8201bde84e0cd514310f1ea6da34
  languageName: node
  linkType: hard

"fd-slicer@npm:~1.1.0":
  version: 1.1.0
  resolution: "fd-slicer@npm:1.1.0"
  dependencies:
    pend: "npm:~1.2.0"
  checksum: 10c0/304dd70270298e3ffe3bcc05e6f7ade2511acc278bc52d025f8918b48b6aa3b77f10361bddfadfe2a28163f7af7adbdce96f4d22c31b2f648ba2901f0c5fc20e
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-saver@npm:^2.0.5":
  version: 2.0.5
  resolution: "file-saver@npm:2.0.5"
  checksum: 10c0/0a361f683786c34b2574aea53744cb70d0a6feb0fa5e3af00f2fcb6c9d40d3049cc1470e38c6c75df24219f247f6fb3076f86943958f580e62ee2ffe897af8b1
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/7cdad7d426ffbaadf45aeb5d15ec675bbd77f7597ad5399e3d2766987ed20bda24d5fac64b3ee79d93276f5865608bb22344a26b9b1ae6c4d00bd94bf611623f
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.3"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/b76f611bd5f5d68f7ae632e3ae503e678d205cf97a17c6ab5b12f6ca61188b5f1f7464503efae6dc18683ed8f0b41460beb48ac4b9ac63fe6201296a91ba2f75
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.1
  resolution: "flatted@npm:3.3.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.6
  resolution: "follow-redirects@npm:1.15.6"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/9ff767f0d7be6aa6870c82ac79cf0368cd73e01bbc00e9eb1c2a16fbb198ec105e3c9b6628bb98e9f3ac66fe29a957b9645bcb9a490bb7aa0d35f908b6b85071
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/9700a0285628abaeb37007c9a4d92bd49f67210f09067638774338e146c8e9c825c5c877f072b2f75f41dc6a2d0be8664f79ffc03f6576649f54a84fb9b47de0
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/cb6f3ac49180be03ff07ba3ff125f9eba2ff0b277fb33c7fc47569fc5e616882c5b1c69b9904c4c4187e97dd0419dd03b134174756f296dec62041e6527e2c6e
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0, fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10c0/259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0, fs-extra@npm:^9.0.1":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.4":
  version: 1.2.4
  resolution: "get-intrinsic@npm:1.2.4"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    hasown: "npm:^2.0.0"
  checksum: 10c0/0a9b82c16696ed6da5e39b1267104475c47e3a9bdbe8b509dfe1710946e38a87be70d759f4bb3cda042d76a41ef47fe769660f3b7c0d1f68750299344ffb15b7
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 10c0/43797ffd815fbb26685bf188c8cfebecb8af87b3925091dd7b9a9c915993293d78e3c9e1bce125928ff92f2d0796f3889b92b5ec6d58d1041b574682132e0a80
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.3.12
  resolution: "glob@npm:10.3.12"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^2.3.6"
    minimatch: "npm:^9.0.1"
    minipass: "npm:^7.0.4"
    path-scurry: "npm:^1.10.2"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/f60cefdc1cf3f958b2bb5823e1b233727f04916d489dc4641d76914f016e6704421e06a83cbb68b0cb1cb9382298b7a88075b844ad2127fc9727ea22b18b0711
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.6":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"global-agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-agent@npm:3.0.0"
  dependencies:
    boolean: "npm:^3.0.1"
    es6-error: "npm:^4.1.1"
    matcher: "npm:^3.0.0"
    roarr: "npm:^2.15.3"
    semver: "npm:^7.3.2"
    serialize-error: "npm:^7.0.1"
  checksum: 10c0/bb8750d026b25da437072762fd739098bad92ff72f66483c3929db4579e072f5523960f7e7fd70ee0d75db48898067b5dc1c9c1d17888128cff008fcc34d1bd3
  languageName: node
  linkType: hard

"global@npm:^4.3.0":
  version: 4.4.0
  resolution: "global@npm:4.4.0"
  dependencies:
    min-document: "npm:^2.19.0"
    process: "npm:^0.11.10"
  checksum: 10c0/4a467aec6602c00a7c5685f310574ab04e289ad7f894f0f01c9c5763562b82f4b92d1e381ce6c5bbb12173e2a9f759c1b63dda6370cfb199970267e14d90aa91
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^13.19.0, globals@npm:^13.24.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10c0/d3c11aeea898eb83d5ec7a99508600fbe8f83d2cf00cbb77f873dbf2bcb39428eff1b538e4915c993d8a3b3473fa71eeebfe22c9bb3a3003d1e26b1f2c8a42cd
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.1":
  version: 1.0.3
  resolution: "globalthis@npm:1.0.3"
  dependencies:
    define-properties: "npm:^1.1.3"
  checksum: 10c0/0db6e9af102a5254630351557ac15e6909bc7459d3e3f6b001e59fe784c96d31108818f032d9095739355a88467459e6488ff16584ee6250cd8c27dec05af4b0
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.1.3"
  checksum: 10c0/505c05487f7944c552cee72087bf1567debb470d4355b1335f2c262d218ebbff805cd3715448fe29b4b380bae6912561d0467233e4165830efd28da241418c63
  languageName: node
  linkType: hard

"got@npm:^11.8.5":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": "npm:^4.0.0"
    "@szmarczak/http-timer": "npm:^4.0.5"
    "@types/cacheable-request": "npm:^6.0.1"
    "@types/responselike": "npm:^1.0.0"
    cacheable-lookup: "npm:^5.0.3"
    cacheable-request: "npm:^7.0.2"
    decompress-response: "npm:^6.0.0"
    http2-wrapper: "npm:^1.0.0-beta.5.2"
    lowercase-keys: "npm:^2.0.0"
    p-cancelable: "npm:^2.0.0"
    responselike: "npm:^2.0.0"
  checksum: 10c0/754dd44877e5cf6183f1e989ff01c648d9a4719e357457bd4c78943911168881f1cfb7b2cb15d885e2105b3ad313adb8f017a67265dd7ade771afdb261ee8cb1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: "npm:^0.1.2"
  checksum: 10c0/4ccb924626c82125897a997d1c84f2377846a6ef57fbee38f7c0e6b41387fba4d00422274440747b58008b5d60114bac2349c2908e9aba55188345281af40a3f
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.3
  resolution: "has-proto@npm:1.0.3"
  checksum: 10c0/35a6989f81e9f8022c2f4027f8b48a552de714938765d019dbea6bb547bd49ce5010a3c7c32ec6ddac6e48fc546166a3583b128f5a7add8b058a6d8b4afec205
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: 10c0/e6922b4345a3f37069cdfe8600febbca791c94988c01af3394d86ca3360b4b93928bbf395859158f88099cb10b19d98e3bbab7c9ff2c1bd09cf665ee90afa2c3
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10c0/a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.1.0":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: "npm:^6.0.0"
  checksum: 10c0/150fbcb001600336d17fdbae803264abed013548eea7946c2264c49ebe2ebd8c4441ba71dd23dd8e18c65de79d637f98b22d4760ba5fb2e0b15d62543d0fff07
  languageName: node
  linkType: hard

"html-docx-js-extends@npm:^0.1.7":
  version: 0.1.7
  resolution: "html-docx-js-extends@npm:0.1.7"
  dependencies:
    jszip: "npm:^3.4.0"
    tslib: "npm:^1.13.0"
  checksum: 10c0/7d7e2fb979cf919f3e46a6a633472927c6c43d9d5d2ef6311276f15e93726b6164462e2dd7a5afbb60c6d0ff9fa8989d6e7fd2fb40a2969f3f58f57eea2e1f5d
  languageName: node
  linkType: hard

"html-entities@npm:^2.3.3":
  version: 2.5.2
  resolution: "html-entities@npm:2.5.2"
  checksum: 10c0/f20ffb4326606245c439c231de40a7c560607f639bf40ffbfb36b4c70729fd95d7964209045f1a4e62fe17f2364cef3d6e49b02ea09016f207fde51c2211e481
  languageName: node
  linkType: hard

"html-to-docx@npm:^1.8.0":
  version: 1.8.0
  resolution: "html-to-docx@npm:1.8.0"
  dependencies:
    "@oozcitak/dom": "npm:1.15.6"
    "@oozcitak/util": "npm:8.3.4"
    color-name: "npm:^1.1.4"
    html-entities: "npm:^2.3.3"
    html-to-vdom: "npm:^0.7.0"
    image-size: "npm:^1.0.0"
    image-to-base64: "npm:^2.2.0"
    jszip: "npm:^3.7.1"
    lodash: "npm:^4.17.21"
    mime-types: "npm:^2.1.35"
    nanoid: "npm:^3.1.25"
    virtual-dom: "npm:^2.1.1"
    xmlbuilder2: "npm:2.1.2"
  checksum: 10c0/710ea9cb72f0aed028fe25acb4b0ec33010cf1f78b946f86753330ff89a03a5cafd4d642b4f76f45fd16b60166968c8d93815620a4e1582ab1e2cc9484ce60f6
  languageName: node
  linkType: hard

"html-to-vdom@npm:^0.7.0":
  version: 0.7.0
  resolution: "html-to-vdom@npm:0.7.0"
  dependencies:
    ent: "npm:^2.0.0"
    htmlparser2: "npm:^3.8.2"
  checksum: 10c0/dab52318010e6f4c1d04ebed9902f53e5c24a39b773c8c5584d4dd00254ce7c6addeaa102a1d8d4a1ae30b91d04acafa0afc2fc0de05f636a1ba86c71543a9bb
  languageName: node
  linkType: hard

"htmlparser2@npm:^3.8.2":
  version: 3.10.1
  resolution: "htmlparser2@npm:3.10.1"
  dependencies:
    domelementtype: "npm:^1.3.1"
    domhandler: "npm:^2.3.0"
    domutils: "npm:^1.5.1"
    entities: "npm:^1.1.1"
    inherits: "npm:^2.0.1"
    readable-stream: "npm:^3.1.1"
  checksum: 10c0/b1424536ff062088501efa06a2afd478545d3134a5ad2e28bbe02dc2d092784982286b90f1c87fa3d86692958dbfb8936352dfd71d1cb2ff7cb61208c00fcdb1
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": "npm:2"
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/32a05e413430b2c1e542e5c74b38a9f14865301dd69dff2e53ddb684989440e3d2ce0c4b64d25eb63cf6283e6265ff979a61cf93e3ca3d23047ddfdc8df34a32
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.0.0"
  checksum: 10c0/6a9b72a033e9812e1476b9d776ce2f387bc94bc46c88aea0d5dab6bd47d0a539b8178830e77054dd26d1142c866d515a28a4dc7c3ff4232c88ff2ebe4f5d12d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.1":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.4
  resolution: "https-proxy-agent@npm:7.0.4"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:4"
  checksum: 10c0/bc4f7c38da32a5fc622450b6cb49a24ff596f9bd48dcedb52d2da3fa1c1a80e100fb506bd59b326c012f21c863c69b275c23de1a01d0b84db396822fdf25e52b
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"iconv-corefoundation@npm:^1.1.7":
  version: 1.1.7
  resolution: "iconv-corefoundation@npm:1.1.7"
  dependencies:
    cli-truncate: "npm:^2.1.0"
    node-addon-api: "npm:^1.6.3"
  conditions: os=darwin
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.3.1
  resolution: "ignore@npm:5.3.1"
  checksum: 10c0/703f7f45ffb2a27fb2c5a8db0c32e7dee66b33a225d28e8db4e1be6474795f606686a6e3bcc50e1aa12f2042db4c9d4a7d60af3250511de74620fbed052ea4cd
  languageName: node
  linkType: hard

"image-size@npm:^1.0.0":
  version: 1.1.1
  resolution: "image-size@npm:1.1.1"
  dependencies:
    queue: "npm:6.0.2"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/2660470096d12be82195f7e80fe03274689fbd14184afb78eaf66ade7cd06352518325814f88af4bde4b26647889fe49e573129f6e7ba8f5ff5b85cc7f559000
  languageName: node
  linkType: hard

"image-to-base64@npm:^2.2.0":
  version: 2.2.0
  resolution: "image-to-base64@npm:2.2.0"
  dependencies:
    node-fetch: "npm:^2.6.0"
  checksum: 10c0/8900bbd8e5dbc086d30ad99355b7aae9f0acc1879966354acb9c85a35c31fa0d89a6132e4844e39e23a231a8f2222b89bcca22fb5893d2148db697cd7ff99a93
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: 10c0/f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.5
  resolution: "immutable@npm:4.3.5"
  checksum: 10c0/63d2d7908241a955d18c7822fd2215b6e89ff5a1a33cc72cd475b013cbbdef7a705aa5170a51ce9f84a57f62fdddfaa34e7b5a14b33d8a43c65cc6a881d6e894
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/7f882953aa6b740d1f0e384d0547158bc86efbf2eea0f1483b8900a6f65c5a5123c2cf09b0d542cc419d0b98a759ecaeb394237e97ea427f2da221dc3cd80cc3
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"individual@npm:^3.0.0":
  version: 3.0.0
  resolution: "individual@npm:3.0.0"
  checksum: 10c0/1d5b7af8833a4af77755a98abc0f69e0f54396ca379a5b2287f0b4dafbbbd9ac896e413e780ce18e61476b9bbfe4144b8a36d218770a7a707d490c09d428ea1b
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-ci@npm:^3.0.0":
  version: 3.0.1
  resolution: "is-ci@npm:3.0.1"
  dependencies:
    ci-info: "npm:^3.2.0"
  bin:
    is-ci: bin.js
  checksum: 10c0/0e81caa62f4520d4088a5bef6d6337d773828a88610346c4b1119fb50c842587ed8bef1e5d9a656835a599e7209405b5761ddf2339668f2d0f4e889a92fe6051
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 10c0/85fee098ae62ba6f1e24cf22678805473c7afd0fb3978a3aa260e354cb7bcb3a5806cf0a98403188465efedec41ab4348e8e4e79305d409601323855b3839d4d
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-object@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-object@npm:1.0.2"
  checksum: 10c0/9cfb80c3a850f453d4a77297e0556bc2040ac6bea5b6e418aee208654938b36bab768169bef3945ccfac7a9bb460edd8034e7c6d8973bcf147d7571e1b53e764
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10c0/cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 10c0/daaee1805add26f781b413fdf192fc91d52409583be30ace35c82607d440da63cc4cac0ac55136716688d6c0a2c6ef3edb2254fecbd1fe06056d6bd15975ee8c
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isbinaryfile@npm:^4.0.8":
  version: 4.0.10
  resolution: "isbinaryfile@npm:4.0.10"
  checksum: 10c0/0703d8cfeb69ed79e6d173120f327450011a066755150a6bbf97ffecec1069a5f2092777868315b21359098c84b54984871cad1abce877ad9141fb2caf3dcabf
  languageName: node
  linkType: hard

"isbinaryfile@npm:^5.0.0":
  version: 5.0.2
  resolution: "isbinaryfile@npm:5.0.2"
  checksum: 10c0/9696f20cf995e375ba8bfdba3ff7d1c0435346f6fc5dd9c049a55514c56e9f49342bbf8c240dc9f56e104bd3a69176c0421922bcb34d72b3c943f4117ade3f53
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"jackspeak@npm:^2.3.6":
  version: 2.3.6
  resolution: "jackspeak@npm:2.3.6"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/f01d8f972d894cd7638bc338e9ef5ddb86f7b208ce177a36d718eac96ec86638a6efa17d0221b10073e64b45edc2ce15340db9380b1f5d5c5d000cbc517dc111
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.8.7
  resolution: "jake@npm:10.8.7"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/89326d01a8bc110d02d973729a66394c79a34b34461116f5c530a2a2dbc30265683fe6737928f75df9178e9d369ff1442f5753fb983d525e740eefdadc56a103
  languageName: node
  linkType: hard

"jiti@npm:^1.21.0":
  version: 1.21.0
  resolution: "jiti@npm:1.21.0"
  bin:
    jiti: bin/jiti.js
  checksum: 10c0/7f361219fe6c7a5e440d5f1dba4ab763a5538d2df8708cdc22561cf25ea3e44b837687931fca7cdd8cdd9f567300e90be989dd1321650045012d8f9ed6aab07f
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/dbf59312e0ebf2b4405ef413ec2b25abb5f8f4d9bc5fb8d9f90381622ebca5f2af6a6aa9a8578f65903f9e33990a6dc798edd0ce5586894bf0e9e31803a1de88
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10c0/7dbf35cd0411d1d648dceb6d59ce5857ec939e52e4afc37601aa3da611f0987d5cee5b38d58329ceddf3ed48bd7215229c8d52059ab01f2444a338bf24ed0f37
  languageName: node
  linkType: hard

"json5@npm:^2.2.0, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonc-parser@npm:^3.2.0":
  version: 3.2.1
  resolution: "jsonc-parser@npm:3.2.1"
  checksum: 10c0/ada66dec143d7f9cb0e2d0d29c69e9ce40d20f3a4cb96b0c6efb745025ac7f9ba647d7ac0990d0adfc37a2d2ae084a12009a9c833dbdbeadf648879a99b9df89
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jszip@npm:^3.4.0, jszip@npm:^3.7.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: "npm:~3.3.0"
    pako: "npm:~1.0.2"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:^1.0.5"
  checksum: 10c0/58e01ec9c4960383fb8b38dd5f67b83ccc1ec215bf74c8a5b32f42b6e5fb79fada5176842a11409c4051b5b94275044851814a31076bf49e1be218d3ef57c863
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0, keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kolorist@npm:^1.8.0":
  version: 1.8.0
  resolution: "kolorist@npm:1.8.0"
  checksum: 10c0/73075db44a692bf6c34a649f3b4b3aea4993b84f6b754cbf7a8577e7c7db44c0bad87752bd23b0ce533f49de2244ce2ce03b7b1b667a85ae170a94782cc50f9b
  languageName: node
  linkType: hard

"lazy-val@npm:^1.0.4, lazy-val@npm:^1.0.5":
  version: 1.0.5
  resolution: "lazy-val@npm:1.0.5"
  checksum: 10c0/28ba7a0e704895a444eed47d110274090f485b991f2ea6fff2ab0878c529c53f60f2eb2d944cbbd68b91408e7455eabc62861c48289d4757fa9c818b97454f24
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lie@npm:3.1.1":
  version: 3.1.1
  resolution: "lie@npm:3.1.1"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/d62685786590351b8e407814acdd89efe1cb136f05cb9236c5a97b2efdca1f631d2997310ad2d565c753db7596799870140e4777c9c9b8c44a0f6bf42d1804a1
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/56dd113091978f82f9dc5081769c6f3b947852ecf9feccaf83e14a123bc630c2301439ce6182521e5fbafbde88e88ac38314327a4e0493a1bea7e0699a7af808
  languageName: node
  linkType: hard

"linebreak@npm:^1.1.0":
  version: 1.1.0
  resolution: "linebreak@npm:1.1.0"
  dependencies:
    base64-js: "npm:0.0.8"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/b350c90d7b10db30345ed56cdb869548110ce73ccdc4337100eaee50755eed78e9823490e6f2d7ed0adde14f7ed2a12d8583015e072c54f34dc70b316fde133d
  languageName: node
  linkType: hard

"local-pkg@npm:^0.5.0":
  version: 0.5.0
  resolution: "local-pkg@npm:0.5.0"
  dependencies:
    mlly: "npm:^1.4.2"
    pkg-types: "npm:^1.0.3"
  checksum: 10c0/f61cbd00d7689f275558b1a45c7ff2a3ddf8472654123ed880215677b9adfa729f1081e50c27ffb415cdb9fa706fb755fec5e23cdd965be375c8059e87ff1cc9
  languageName: node
  linkType: hard

"localforage@npm:^1.10.0":
  version: 1.10.0
  resolution: "localforage@npm:1.10.0"
  dependencies:
    lie: "npm:3.1.1"
  checksum: 10c0/00f19f1f97002e6721587ed5017f502d58faf80dae567d5065d4d1ee0caf0762f40d2e2dba7f0ef7d3f14ee6203242daae9ecad97359bfc10ecff36df11d85a3
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10c0/fb407355f7e6cd523a9383e76e6b455321f0f153a6c9625e21a8827d10c54c2a2341bd2ae8d034358b60e07325e1330c14c224ff582d04612a46a4f0479ff2f2
  languageName: node
  linkType: hard

"lodash-unified@npm:^1.0.2":
  version: 1.0.3
  resolution: "lodash-unified@npm:1.0.3"
  peerDependencies:
    "@types/lodash-es": "*"
    lodash: "*"
    lodash-es: "*"
  checksum: 10c0/eb82553ecca72d217677df73f13e99bd04fb14f0981a21fb56aed687cf5130ecec8f6fb25a9bfef9457df0e5964e2a0768b69e44c6c9f0cb114c941d759cb7dd
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10c0/2caf0e4808f319d761d2939ee0642fa6867a4bbf2cfce43276698828380756b99d4c4fa226d881655e6ac298dd453fe12a5ec8ba49861777759494c534936985
  languageName: node
  linkType: hard

"lodash.escaperegexp@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.escaperegexp@npm:4.1.2"
  checksum: 10c0/484ad4067fa9119bb0f7c19a36ab143d0173a081314993fe977bd00cf2a3c6a487ce417a10f6bac598d968364f992153315f0dbe25c9e38e3eb7581dd333e087
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10c0/dfdb2356db19631a4b445d5f37868a095e2402292d59539a987f134a8778c62a2810c2452d11ae9e6dcac71fc9de40a6fedcb20e2952a15b431ad8b29e50e28f
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lop@npm:^0.4.1":
  version: 0.4.1
  resolution: "lop@npm:0.4.1"
  dependencies:
    duck: "npm:^0.1.12"
    option: "npm:~0.2.1"
    underscore: "npm:^1.13.1"
  checksum: 10c0/d6d9f5e7d6757a139885bdc20a65e55a9771bbc3f685a5c3b2c44a6944de6002a96db822440aa36309ea80fdae8e8813ef3a70ca695ca9af8dbefd41a925a727
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 10c0/f82a2b3568910509da4b7906362efa40f5b54ea14c2584778ddb313226f9cbf21020a5db35f9b9a0e95847a9b781d548601f31793d736b22a2b8ae8eb9ab1082
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.2.0
  resolution: "lru-cache@npm:10.2.0"
  checksum: 10c0/c9847612aa2daaef102d30542a8d6d9b2c2bb36581c1bf0dc3ebf5e5f3352c772a749e604afae2e46873b930a9e9523743faac4e5b937c576ab29196774712ee
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.5, magic-string@npm:^0.30.7, magic-string@npm:^0.30.8":
  version: 0.30.9
  resolution: "magic-string@npm:0.30.9"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.4.15"
  checksum: 10c0/edbeea35b4f90b58815d8b13899fa412b5bc1e81cae14fe6d24d5c383c5f04331fce2c5a75bfb7926203ab6fc8c71290cdab56703a5b82432d8a1e144d6042e1
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.0
  resolution: "make-fetch-happen@npm:13.0.0"
  dependencies:
    "@npmcli/agent": "npm:^2.0.0"
    cacache: "npm:^18.0.0"
    http-cache-semantics: "npm:^4.1.1"
    is-lambda: "npm:^1.0.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^3.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^0.6.3"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^10.0.0"
  checksum: 10c0/43b9f6dcbc6fe8b8604cb6396957c3698857a15ba4dbc38284f7f0e61f248300585ef1eb8cc62df54e9c724af977e45b5cdfd88320ef7f53e45070ed3488da55
  languageName: node
  linkType: hard

"mammoth@npm:^1.7.1":
  version: 1.7.1
  resolution: "mammoth@npm:1.7.1"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.6"
    argparse: "npm:~1.0.3"
    base64-js: "npm:^1.5.1"
    bluebird: "npm:~3.4.0"
    dingbat-to-unicode: "npm:^1.0.1"
    jszip: "npm:^3.7.1"
    lop: "npm:^0.4.1"
    path-is-absolute: "npm:^1.0.0"
    underscore: "npm:^1.13.1"
    xmlbuilder: "npm:^10.0.0"
  bin:
    mammoth: bin/mammoth
  checksum: 10c0/0c1182b7169fdaccae476396b465eb7e7d455f96d5164b14e907d7feb45318274a4d1c4a482a671c32baa38504dd829dbd1656c911b80a59a1ae612f1a93ec2a
  languageName: node
  linkType: hard

"matcher@npm:^3.0.0":
  version: 3.0.0
  resolution: "matcher@npm:3.0.0"
  dependencies:
    escape-string-regexp: "npm:^4.0.0"
  checksum: 10c0/2edf24194a2879690bcdb29985fc6bc0d003df44e04df21ebcac721fa6ce2f6201c579866bb92f9380bffe946f11ecd8cd31f34117fb67ebf8aca604918e127e
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: 10c0/a2c472ea16cee3911ae742593715aa4c634eb3d4b9f1e6ada0902aa90df13dcbb7285d19435f3ff213ebaa3b2e0c0265c1eb0e3fb278fda7f8919f046a410cd9
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: 10c0/45c88e064fd715166619af72e8cf8a7a17224d6edf61f7a8633d740ed8c8c0558a4373876c9b8ffc5518c2b65a960266adf403cc215cb1e90f7e262b58991f54
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: "npm:^3.0.2"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/3d6505b20f9fa804af5d8c596cb1c5e475b9b0cd05f652c5b56141cf941bd72adaeb7a436fda344235cef93a7f29b7472efc779fcdb83b478eab0867b95cdeff
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:^1.28.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.35":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:^2.5.2":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 10c0/c5381a5eae997f1c3b5e90ca7f209ed58c3615caeee850e85329c598f0c000ae7bec40196580eef1781c60c709f47258131dab237cad8786f8f56750594f27fa
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10c0/0d6f07ce6e03e9e4445bee655202153bdb8a98d67ee8dc965ac140900d7a2688343e6b4c9a72cfc9ef2f7944dfd76eef4ab2482eb7b293a68b84916bac735362
  languageName: node
  linkType: hard

"min-document@npm:^2.19.0":
  version: 2.19.0
  resolution: "min-document@npm:2.19.0"
  dependencies:
    dom-walk: "npm:^0.1.0"
  checksum: 10c0/783724da716fc73a51c171865d7b29bf2b855518573f82ef61c40d214f6898d7b91b5c5419e4d22693cdb78d4615873ebc3b37d7639d3dd00ca283e5a07c7af9
  languageName: node
  linkType: hard

"minimatch@npm:9.0.3":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/85f407dcd38ac3e180f425e86553911d101455ca3ad5544d6a7cec16286657e4f8a9aa6695803025c55e31e35a91a2252b5dc8e7d527211278b8b65b4dbd5eac
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1, minimatch@npm:^5.1.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1, minimatch@npm:^9.0.3":
  version: 9.0.4
  resolution: "minimatch@npm:9.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/2c16f21f50e64922864e560ff97c587d15fd491f65d92a677a344e970fe62aafdbeafe648965fa96d33c061b4d0eabfe0213466203dd793367e7f28658cf6414
  languageName: node
  linkType: hard

"minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.4
  resolution: "minipass-fetch@npm:3.0.4"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^2.1.2"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/1b63c1f3313e88eeac4689f1b71c9f086598db9a189400e3ee960c32ed89e06737fa23976c9305c2d57464fb3fcdc12749d3378805c9d6176f5569b0d0ee8a75
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4":
  version: 7.0.4
  resolution: "minipass@npm:7.0.4"
  checksum: 10c0/6c7370a6dfd257bf18222da581ba89a5eaedca10e158781232a8b5542a90547540b4b9b7e7f490e4cda43acfbd12e086f0453728ecf8c19e0ef6921bc5958ac5
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mlly@npm:^1.2.0, mlly@npm:^1.4.2, mlly@npm:^1.5.0":
  version: 1.6.1
  resolution: "mlly@npm:1.6.1"
  dependencies:
    acorn: "npm:^8.11.3"
    pathe: "npm:^1.1.2"
    pkg-types: "npm:^1.0.3"
    ufo: "npm:^1.3.2"
  checksum: 10c0/a7bf26b3d4f83b0f5a5232caa3af44be08b464f562f31c11d885d1bc2d43b7d717137d47b0c06fdc69e1b33ffc09f902b6d2b18de02c577849d40914e8785092
  languageName: node
  linkType: hard

"modify-filename@npm:^1.1.0":
  version: 1.1.0
  resolution: "modify-filename@npm:1.1.0"
  checksum: 10c0/9f98b69aa1ecdeae81a31917dc32da3259d3ec2f4519491de0b390abc81cd96388ab5778c4e18b83b2a9d9812382314184699693072bb42ead81cc1d94b82502
  languageName: node
  linkType: hard

"mrmime@npm:^2.0.0":
  version: 2.0.0
  resolution: "mrmime@npm:2.0.0"
  checksum: 10c0/312b35ed288986aec90955410b21ed7427fd1e4ee318cb5fc18765c8d029eeded9444faa46589e5b1ed6b35fb2054a802ac8dcb917ddf6b3e189cb3bf11a965c
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"muggle-string@npm:^0.3.1":
  version: 0.3.1
  resolution: "muggle-string@npm:0.3.1"
  checksum: 10c0/489b0575fa76e30914393915a36638590052409fca2206a6bef0fb0ad7b181c1cbf99761191bfd16fe402c6f5a3164897965422fa32ef20ada1b44024ba46ab6
  languageName: node
  linkType: hard

"nanoid@npm:5.0.7":
  version: 5.0.7
  resolution: "nanoid@npm:5.0.7"
  bin:
    nanoid: bin/nanoid.js
  checksum: 10c0/a3fb1c157e3e35378f44e5a7130c70f80c9037f66c9a37285e5e3d8298e8405fcb2399baaa420980b0fe5fd9c2e4186a6a31c3526f21de03cf34c1b459871401
  languageName: node
  linkType: hard

"nanoid@npm:^3.1.25, nanoid@npm:^3.3.7":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/e3fb661aa083454f40500473bb69eedb85dc160e763150b9a2c567c7e9ff560ce028a9f833123b618a6ea742e311138b591910e795614a629029e86e180660f3
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"next-tick@npm:^0.2.2":
  version: 0.2.2
  resolution: "next-tick@npm:0.2.2"
  checksum: 10c0/ccecbe773004af4ef06870dec192b5c8ad8cfe42425306bff1a90738339c6e6e3a520e65536f8a46a76b45ebf82a7049c8252b3518ec7bac9900859b786aed5b
  languageName: node
  linkType: hard

"node-addon-api@npm:^1.6.3":
  version: 1.7.2
  resolution: "node-addon-api@npm:1.7.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/bcf526f2ce788182730d3c3df5206585873d1e837a6e1378ff84abccf2f19cf3f93a8274f9c1245af0de63a0dbd1bb95ca2f767ecf5c678d6930326aaf396c4e
  languageName: node
  linkType: hard

"node-fetch-native@npm:^1.6.3":
  version: 1.6.4
  resolution: "node-fetch-native@npm:1.6.4"
  checksum: 10c0/78334dc6def5d1d95cfe87b33ac76c4833592c5eb84779ad2b0c23c689f9dd5d1cfc827035ada72d6b8b218f717798968c5a99aeff0a1a8bf06657e80592f9c3
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.1.0
  resolution: "node-gyp@npm:10.1.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^13.0.0"
    nopt: "npm:^7.0.0"
    proc-log: "npm:^3.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.2"
    which: "npm:^4.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/9cc821111ca244a01fb7f054db7523ab0a0cd837f665267eb962eb87695d71fb1e681f9e21464cc2fd7c05530dc4c81b810bca1a88f7d7186909b74477491a3c
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.14":
  version: 2.0.14
  resolution: "node-releases@npm:2.0.14"
  checksum: 10c0/199fc93773ae70ec9969bc6d5ac5b2bbd6eb986ed1907d751f411fef3ede0e4bfdb45ceb43711f8078bea237b6036db8b1bf208f6ff2b70c7d615afd157f3ab9
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.0
  resolution: "nopt@npm:7.2.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/9bd7198df6f16eb29ff16892c77bcf7f0cc41f9fb5c26280ac0def2cf8cf319f3b821b3af83eba0e74c85807cc430a16efe0db58fe6ae1f41e69519f585b6aff
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 10c0/95d948f9bdd2cfde91aa786d1816ae40f8262946e13700bf6628105994fe0ff361662c20af3961161c38a119dc977adeb41fc0b41b1745eb77edaaf9cb22db23
  languageName: node
  linkType: hard

"normalize-wheel-es@npm:^1.2.0":
  version: 1.2.0
  resolution: "normalize-wheel-es@npm:1.2.0"
  checksum: 10c0/48b5961f3f2fb902213ae8b4389fa043a134b3f15415e58f170aa414ba205896ba03410f457812baaaf50bd0a4a2899f35a390315163c1b2f24dddacffbdc89f
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"nth-check@npm:^2.1.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"numeral@npm:^2.0.6":
  version: 2.0.6
  resolution: "numeral@npm:2.0.6"
  checksum: 10c0/5ed008d3fae05cfa4986b77a85ca10bff29ae6e1fa41a04cce05ea21f08a8a104226f88868930e2a94e3239708d6985d111b5d1291e8b9a3049ffc5365c332d4
  languageName: node
  linkType: hard

"numfmt@npm:^2.5.2":
  version: 2.5.2
  resolution: "numfmt@npm:2.5.2"
  checksum: 10c0/9c56632956dc3f81aeb0089c08c05b27a057ed9e92ef9d940242848492ef849f4589097052f91406d7a3e00119fd3a5bed6d0539c5ab8dd4a414a6b81293117b
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"ofetch@npm:^1.3.4":
  version: 1.3.4
  resolution: "ofetch@npm:1.3.4"
  dependencies:
    destr: "npm:^2.0.3"
    node-fetch-native: "npm:^1.6.3"
    ufo: "npm:^1.5.3"
  checksum: 10c0/39855005c3f8aa11c11d3a3b0c4366b67d316da58633f4cf5d4a5af0a61495fd68699f355e70deda70355ead25f27b41c3bde2fdd1d24ce3f85ac79608dd8677
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"option@npm:~0.2.1":
  version: 0.2.4
  resolution: "option@npm:0.2.4"
  checksum: 10c0/b605e5f3f65b21e0a9ec49a4ead50acb953696678109bb0decd80cc4cc4b466691c14472b2e281866cef513fc63f0310a09677c2d4cedd1e0d9607be1ce25831
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": "npm:^1.2.3"
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
  checksum: 10c0/66fba794d425b5be51353035cf3167ce6cfa049059cbb93229b819167687e0f48d2bc4603fcb21b091c99acb516aae1083624675b15c4765b2e4693a085e959c
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 10c0/8c6dc1f8dd4154fd8b96a10e55a3a832684c4365fb9108056d89e79fbf21a2465027c04a59d0d797b5ffe10b54a61a32043af287d5c4860f1e996cbdbc847f01
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"pako@npm:^0.2.5":
  version: 0.2.9
  resolution: "pako@npm:0.2.9"
  checksum: 10c0/79c1806ebcf325b60ae599e4d7227c2e346d7b829dc20f5cf24cef07c934079dc3a61c5b3c8278a2f7a190c4a613e343ea11e5302dbe252efd11712df4b6b041
  languageName: node
  linkType: hard

"pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parchment@npm:3.0.0-rc.1":
  version: 3.0.0-rc.1
  resolution: "parchment@npm:3.0.0-rc.1"
  checksum: 10c0/c0a00101c33d792172a317edd26ce3e6d0cdef3af5893304219480d8d46d760bf2c8879229f95966805e68a1df6c8730925a7957a8b879963d2b2c9b6063b706
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10c0/8b8c3fd5c66bd340272180590ae4ff139769e9ab79522e2eb82e3d571a89b8117c04147f65ad066dccfb42fcad902e5b7d794b3d35e0fd840491a8ddbedf8c66
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.2":
  version: 1.10.2
  resolution: "path-scurry@npm:1.10.2"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/d723777fbf9627f201e64656680f66ebd940957eebacf780e6cce1c2919c29c116678b2d7dbf8821b3a2caa758d125f4444005ccec886a25c8f324504e48e601
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"pathe@npm:^1.1.0, pathe@npm:^1.1.2":
  version: 1.1.2
  resolution: "pathe@npm:1.1.2"
  checksum: 10c0/64ee0a4e587fb0f208d9777a6c56e4f9050039268faaaaecd50e959ef01bf847b7872785c36483fa5cdcdbdfdb31fef2ff222684d4fc21c330ab60395c681897
  languageName: node
  linkType: hard

"pend@npm:~1.2.0":
  version: 1.2.0
  resolution: "pend@npm:1.2.0"
  checksum: 10c0/8a87e63f7a4afcfb0f9f77b39bb92374afc723418b9cb716ee4257689224171002e07768eeade4ecd0e86f1fa3d8f022994219fb45634f2dbd78c6803e452458
  languageName: node
  linkType: hard

"perfect-debounce@npm:^1.0.0":
  version: 1.0.0
  resolution: "perfect-debounce@npm:1.0.0"
  checksum: 10c0/e2baac416cae046ef1b270812cf9ccfb0f91c04ea36ac7f5b00bc84cb7f41bdbba087c0ab21b4e02a7ef3a1f1f6db399f137cecec46868bd7d8d88c2a9ee431f
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: 10c0/20a5b249e331c14479d94ec6817a182fd7a5680debae82705747b2db7ec50009a5f6648d0621c561b0572703f84dbef0858abcbd5856d3c5511426afcb1961f7
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pinia@npm:^2.1.7":
  version: 2.1.7
  resolution: "pinia@npm:2.1.7"
  dependencies:
    "@vue/devtools-api": "npm:^6.5.0"
    vue-demi: "npm:>=0.14.5"
  peerDependencies:
    "@vue/composition-api": ^1.4.0
    typescript: ">=4.4.4"
    vue: ^2.6.14 || ^3.3.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
    typescript:
      optional: true
  checksum: 10c0/f4380a4db04b5b8565ed8a6843821d91f8f650d79dd9f0094005248bd963521b8a73419032fda76541e59b895b0e7852e67ca9d0408162cc391ce5a1bcbda445
  languageName: node
  linkType: hard

"pkg-types@npm:^1.0.3":
  version: 1.0.3
  resolution: "pkg-types@npm:1.0.3"
  dependencies:
    jsonc-parser: "npm:^3.2.0"
    mlly: "npm:^1.2.0"
    pathe: "npm:^1.1.0"
  checksum: 10c0/7f692ff2005f51b8721381caf9bdbc7f5461506ba19c34f8631660a215c8de5e6dca268f23a319dd180b8f7c47a0dc6efea14b376c485ff99e98d810b8f786c4
  languageName: node
  linkType: hard

"plist@npm:^3.0.4, plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.5.1"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/db19ba50faafc4103df8e79bcd6b08004a56db2a9dd30b3e5c8b0ef30398ef44344a674e594d012c8fc39e539a2b72cb58c60a76b4b4401cbbc7c8f6b028d93d
  languageName: node
  linkType: hard

"postcss-pxtorem@npm:^6.1.0":
  version: 6.1.0
  resolution: "postcss-pxtorem@npm:6.1.0"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10c0/dbf72b4683962ccff49033a25ad0c2e514f7a4a3a203d648314246d6a81b793912b3e00ff70f54da22774d032f4c4c109d6ffbb1f13c834c6cf483a5226bd5db
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.15":
  version: 6.0.16
  resolution: "postcss-selector-parser@npm:6.0.16"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/0e11657cb3181aaf9ff67c2e59427c4df496b4a1b6a17063fae579813f80af79d444bf38f82eeb8b15b4679653fd3089e66ef0283f9aab01874d885e6cf1d2cf
  languageName: node
  linkType: hard

"postcss@npm:^8.4.35, postcss@npm:^8.4.38":
  version: 8.4.38
  resolution: "postcss@npm:8.4.38"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.2.0"
  checksum: 10c0/955407b8f70cf0c14acf35dab3615899a2a60a26718a63c848cf3c29f2467b0533991b985a2b994430d890bd7ec2b1963e36352b0774a19143b5f591540f7c06
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10c0/81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:^3.2.4":
  version: 3.2.5
  resolution: "prettier@npm:3.2.5"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/ea327f37a7d46f2324a34ad35292af2ad4c4c3c3355da07313339d7e554320f66f65f91e856add8530157a733c6c4a897dc41b577056be5c24c40f739f5ee8c6
  languageName: node
  linkType: hard

"proc-log@npm:^3.0.0":
  version: 3.0.0
  resolution: "proc-log@npm:3.0.0"
  checksum: 10c0/f66430e4ff947dbb996058f6fd22de2c66612ae1a89b097744e17fb18a4e8e7a86db99eda52ccf15e53f00b63f4ec0b0911581ff2aac0355b625c8eac509b0dc
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: 10c0/1697e07cb1068055dbe9fe858d242368ff5d2073639e652b75a7eb1f2a1a8d4afd404d719de23c7b48481a6aa0040686310e2dac2f53d776daa2176d3f96369c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-types@npm:15.x, prop-types@npm:^15.6.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/bbdeda4f747cdf47db97428f3a135728669e56a0ae5f354a9ac5b74556556f5446a46f720a8f14ca2ece5be9b4d5d23c346db02b555f46739934cc6c093a5478
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"pupa@npm:^2.0.1":
  version: 2.1.1
  resolution: "pupa@npm:2.1.1"
  dependencies:
    escape-goat: "npm:^2.0.0"
  checksum: 10c0/d2346324780ebae4be847cad052b830e004d816851dd4750fc73faa6cd360f443e358f6b1c83641fd4c904c6055dcb545807f55259a20a52ad86d9477746c724
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: 10c0/a24cba5da8cec30d70d2484be37622580f64765fb6390a928b17f60cd69e8dbd32a954b3ff9176fa1b86d86ff2ba05252fae55dc4d40d0291c60412b0ad096da
  languageName: node
  linkType: hard

"quill-better-table@npm:^1.2.10":
  version: 1.2.10
  resolution: "quill-better-table@npm:1.2.10"
  checksum: 10c0/08d0a9efab40ca57b53b69979db814d297e0e22798b9be5cc4c71ac1d6e56d134de6983eb94e2c9c3d4514537acb36733aed8a4785d985f1f1c34c59b010ebfc
  languageName: node
  linkType: hard

"quill-delta@npm:^5.1.0":
  version: 5.1.0
  resolution: "quill-delta@npm:5.1.0"
  dependencies:
    fast-diff: "npm:^1.3.0"
    lodash.clonedeep: "npm:^4.5.0"
    lodash.isequal: "npm:^4.5.0"
  checksum: 10c0/a99462b96177f4559e5a659be0f51bbfe090c11b61c53aa19afabd3fdf8a6495173bbacd84b75acce680ed7c157a024907e74ff077ddd6a135b4da15bf71ada2
  languageName: node
  linkType: hard

"quill@npm:2.0.0-rc.5":
  version: 2.0.0-rc.5
  resolution: "quill@npm:2.0.0-rc.5"
  dependencies:
    eventemitter3: "npm:^5.0.1"
    lodash-es: "npm:^4.17.21"
    parchment: "npm:3.0.0-rc.1"
    quill-delta: "npm:^5.1.0"
  checksum: 10c0/03f8d792c441f2bfc35a8982d9d34ea1e76474c81ccb1cf1e8c960d224675015ec04f6eaa221a2666a47f9615778c7a0a0555f2cc65e7f1034bef4ca4b2c8cd5
  languageName: node
  linkType: hard

"rc-dialog@npm:^9.4.0":
  version: 9.4.0
  resolution: "rc-dialog@npm:9.4.0"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    "@rc-component/portal": "npm:^1.0.0-8"
    classnames: "npm:^2.2.6"
    rc-motion: "npm:^2.3.0"
    rc-util: "npm:^5.21.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/22804e44d7cf5952cce29d92bd180f86bffc9c182165144911e40f209169850aece8ec76a532867ad14272fe5e1ca1b59313af0f62bd7d0f3e37fe2695757677
  languageName: node
  linkType: hard

"rc-dropdown@npm:^4.2.0":
  version: 4.2.0
  resolution: "rc-dropdown@npm:4.2.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@rc-component/trigger": "npm:^2.0.0"
    classnames: "npm:^2.2.6"
    rc-util: "npm:^5.17.0"
  peerDependencies:
    react: ">=16.11.0"
    react-dom: ">=16.11.0"
  checksum: 10c0/e808172c922f4c3f8bf4fb4641834e34929df9e0af00981e5554d72852d2df016a85c5339119a61f740ea70da7e33c89ed95c11ee8b9c5538533072675287a3a
  languageName: node
  linkType: hard

"rc-input-number@npm:^9.0.0":
  version: 9.0.0
  resolution: "rc-input-number@npm:9.0.0"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    "@rc-component/mini-decimal": "npm:^1.0.1"
    classnames: "npm:^2.2.5"
    rc-input: "npm:~1.4.0"
    rc-util: "npm:^5.28.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/7a8567716c1f566224d4ccd64dbe477bddf803c42f95cb0378f2ce31f6b0a18f52961a3e7437405f8335f6e585ce496f9df5b3e8d3ab1c2b671edefc40b9b90a
  languageName: node
  linkType: hard

"rc-input@npm:^1.4.5, rc-input@npm:~1.4.0":
  version: 1.4.5
  resolution: "rc-input@npm:1.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.11.1"
    classnames: "npm:^2.2.1"
    rc-util: "npm:^5.18.1"
  peerDependencies:
    react: ">=16.0.0"
    react-dom: ">=16.0.0"
  checksum: 10c0/9546c18ab3fd90429c746638e008579933a5aba0b738c6bb33b34fdb963d23edce1d6a34b1f658ac60122208caa5fcf4f9e45dbb82e7d1457003c4a6e0e34fbc
  languageName: node
  linkType: hard

"rc-menu@npm:^9.13.0":
  version: 9.13.0
  resolution: "rc-menu@npm:9.13.0"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    "@rc-component/trigger": "npm:^2.0.0"
    classnames: "npm:2.x"
    rc-motion: "npm:^2.4.3"
    rc-overflow: "npm:^1.3.1"
    rc-util: "npm:^5.27.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/7b488367c43e2a13391cf6b9debae58d7fa8fd663d596ded8763431158ed9e6d8d0fef37d05424791b86081e86130ab3214a40813b2ce30046fc881d54ebf279
  languageName: node
  linkType: hard

"rc-motion@npm:^2.0.0, rc-motion@npm:^2.0.1, rc-motion@npm:^2.3.0, rc-motion@npm:^2.4.3, rc-motion@npm:^2.4.4, rc-motion@npm:^2.9.0":
  version: 2.9.0
  resolution: "rc-motion@npm:2.9.0"
  dependencies:
    "@babel/runtime": "npm:^7.11.1"
    classnames: "npm:^2.2.1"
    rc-util: "npm:^5.21.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/dcef9e79e92c7a45d84ed63acf5ca0b1abe6556714512ef1c394ae83a9eec1c79c39ce79961d6ed44d83fc25f2afde9a6708864c9262966a33f9051230207697
  languageName: node
  linkType: hard

"rc-notification@npm:^5.4.0":
  version: 5.4.0
  resolution: "rc-notification@npm:5.4.0"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    classnames: "npm:2.x"
    rc-motion: "npm:^2.9.0"
    rc-util: "npm:^5.20.1"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/f8709d9dfc357a8f12a6f40ad1a5f7c40e06ee3d8936e0f4e68cd7f03f91efb9c3bb69ee992070103f91c9acb241fb70a3fc01b72a4a1359fa110d5dfb5a926a
  languageName: node
  linkType: hard

"rc-overflow@npm:^1.3.1, rc-overflow@npm:^1.3.2":
  version: 1.3.2
  resolution: "rc-overflow@npm:1.3.2"
  dependencies:
    "@babel/runtime": "npm:^7.11.1"
    classnames: "npm:^2.2.1"
    rc-resize-observer: "npm:^1.0.0"
    rc-util: "npm:^5.37.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/1c3741760e21fdc5829ebc1affb2a54fd04bda3abc1c88542b6191845d3490ca255fc423f897f448af1c79311c7eda499ea977ca79bce55ebdaf75397573d125
  languageName: node
  linkType: hard

"rc-picker@npm:^4.3.0":
  version: 4.3.2
  resolution: "rc-picker@npm:4.3.2"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    "@rc-component/trigger": "npm:^2.0.0"
    classnames: "npm:^2.2.1"
    rc-overflow: "npm:^1.3.2"
    rc-resize-observer: "npm:^1.4.0"
    rc-util: "npm:^5.38.1"
  peerDependencies:
    date-fns: ">= 2.x"
    dayjs: ">= 1.x"
    luxon: ">= 3.x"
    moment: ">= 2.x"
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  peerDependenciesMeta:
    date-fns:
      optional: true
    dayjs:
      optional: true
    luxon:
      optional: true
    moment:
      optional: true
  checksum: 10c0/2e2dd2af26cc15d907b9377cde7796572e3bf32a5b39690534cbefa647a4f8267373d8c98f43bd5f03a97c5dc93dde88ee937c629ad3b45a5ea00fa87e275887
  languageName: node
  linkType: hard

"rc-resize-observer@npm:^1.0.0, rc-resize-observer@npm:^1.3.1, rc-resize-observer@npm:^1.4.0":
  version: 1.4.0
  resolution: "rc-resize-observer@npm:1.4.0"
  dependencies:
    "@babel/runtime": "npm:^7.20.7"
    classnames: "npm:^2.2.1"
    rc-util: "npm:^5.38.0"
    resize-observer-polyfill: "npm:^1.5.1"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/50e03fa524e156477e5e083bc11e92b8d2c442b6a9a0caa111d9b9991174f9ac98175d50fba1721e55338472d5344610e30fce8531a65785b3d79c191173d351
  languageName: node
  linkType: hard

"rc-segmented@npm:^2.3.0":
  version: 2.3.0
  resolution: "rc-segmented@npm:2.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.11.1"
    classnames: "npm:^2.2.1"
    rc-motion: "npm:^2.4.4"
    rc-util: "npm:^5.17.0"
  peerDependencies:
    react: ">=16.0.0"
    react-dom: ">=16.0.0"
  checksum: 10c0/8d5f99ca55f1a1b0f3de66801a456e35c8c41a94a8d1b90a98aeb622cc95c86a7430449f2b3384d497f68641f92af0e3bd2689bfb7bbaf6543882cfc12e6c42f
  languageName: node
  linkType: hard

"rc-select@npm:^14.13.0":
  version: 14.13.1
  resolution: "rc-select@npm:14.13.1"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    "@rc-component/trigger": "npm:^2.1.1"
    classnames: "npm:2.x"
    rc-motion: "npm:^2.0.1"
    rc-overflow: "npm:^1.3.1"
    rc-util: "npm:^5.16.1"
    rc-virtual-list: "npm:^3.5.2"
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: 10c0/ca3941dcf70bf1e4aa3f37065fb2ad980c403cb4dc932bf6e9d558aab0a1811a6f589b8eb075b1339032f9141c58bf832ee063244d492793ec366c6d88dfa165
  languageName: node
  linkType: hard

"rc-textarea@npm:^1.6.3":
  version: 1.6.3
  resolution: "rc-textarea@npm:1.6.3"
  dependencies:
    "@babel/runtime": "npm:^7.10.1"
    classnames: "npm:^2.2.1"
    rc-input: "npm:~1.4.0"
    rc-resize-observer: "npm:^1.0.0"
    rc-util: "npm:^5.27.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/fe88d9009511e07e260b4492bf009d51992177683db58ad4c52a3d6f4759415efcf39411e492202ca7a2de8f301cefda375278b7a51a33a9d25a0a805f1c762c
  languageName: node
  linkType: hard

"rc-tooltip@npm:^6.2.0":
  version: 6.2.0
  resolution: "rc-tooltip@npm:6.2.0"
  dependencies:
    "@babel/runtime": "npm:^7.11.2"
    "@rc-component/trigger": "npm:^2.0.0"
    classnames: "npm:^2.3.1"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/5f1f8e51fbb156acb3ebea52f509e61b3c0679869d73f0238bcd6d585fcef3cb5f544b5064cac417fb7137218ac81cf9223dc6d87d2e7820a56c72e9f279198e
  languageName: node
  linkType: hard

"rc-util@npm:^5.16.1, rc-util@npm:^5.17.0, rc-util@npm:^5.18.1, rc-util@npm:^5.20.1, rc-util@npm:^5.21.0, rc-util@npm:^5.24.4, rc-util@npm:^5.27.0, rc-util@npm:^5.28.0, rc-util@npm:^5.36.0, rc-util@npm:^5.37.0, rc-util@npm:^5.38.0, rc-util@npm:^5.38.1, rc-util@npm:^5.39.1":
  version: 5.39.1
  resolution: "rc-util@npm:5.39.1"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    react-is: "npm:^18.2.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/95fac4642664a49dc334ebafbdea9ec73e12cefadca2247a001d3f54d18ab91bb9381d0035dc55dbfcc67dbbb5de26c420a20c03d650da6065645c54d73b6c83
  languageName: node
  linkType: hard

"rc-virtual-list@npm:^3.5.2":
  version: 3.11.4
  resolution: "rc-virtual-list@npm:3.11.4"
  dependencies:
    "@babel/runtime": "npm:^7.20.0"
    classnames: "npm:^2.2.6"
    rc-resize-observer: "npm:^1.0.0"
    rc-util: "npm:^5.36.0"
  peerDependencies:
    react: ">=16.9.0"
    react-dom: ">=16.9.0"
  checksum: 10c0/60b4a578ca3ad6d8c10075e10107e9d04596f9cec48f54155c6fc56a0b51d75a176b1c8b3341951a8ead48226b23f83785435c7777d6fba60cedbe8377426161
  languageName: node
  linkType: hard

"react-draggable@npm:^4.0.3, react-draggable@npm:^4.4.5, react-draggable@npm:^4.4.6":
  version: 4.4.6
  resolution: "react-draggable@npm:4.4.6"
  dependencies:
    clsx: "npm:^1.1.1"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    react: ">= 16.3.0"
    react-dom: ">= 16.3.0"
  checksum: 10c0/1e8cf47414a8554caa68447e5f27749bc40e1eabb4806e2dadcb39ab081d263f517d6aaec5231677e6b425603037c7e3386d1549898f9ffcc98a86cabafb2b9a
  languageName: node
  linkType: hard

"react-grid-layout@npm:^1.4.4":
  version: 1.4.4
  resolution: "react-grid-layout@npm:1.4.4"
  dependencies:
    clsx: "npm:^2.0.0"
    fast-equals: "npm:^4.0.3"
    prop-types: "npm:^15.8.1"
    react-draggable: "npm:^4.4.5"
    react-resizable: "npm:^3.0.5"
    resize-observer-polyfill: "npm:^1.5.1"
  peerDependencies:
    react: ">= 16.3.0"
    react-dom: ">= 16.3.0"
  checksum: 10c0/3ff5715e2f19816d9c231053ca7e4d9c6c19893282d13b9dbfb3b748309d4c687d6bc9cffd6982a1e72df20d9f2560cf6ee87fde2442002a1b848fa9f9613d63
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: 10c0/6eb5e4b28028c23e2bfcf73371e72cd4162e4ac7ab445ddae2afe24e347a37d6dc22fae6e1748632cd43c6d4f9b8f86dcf26bf9275e1874f436d129952528ae0
  languageName: node
  linkType: hard

"react-resizable@npm:^3.0.5":
  version: 3.0.5
  resolution: "react-resizable@npm:3.0.5"
  dependencies:
    prop-types: "npm:15.x"
    react-draggable: "npm:^4.0.3"
  peerDependencies:
    react: ">= 16.3"
  checksum: 10c0/cfe50aa6efb79e0aa09bd681a5beab2fcd1186737c4952eb4c3974ed9395d5d263ccd1130961d06b8f5e24c8f544dd2967b5c740ce68719962d1771de7bdb350
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.5.5"
    dom-helpers: "npm:^5.0.1"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.6.2"
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: 10c0/2ba754ba748faefa15f87c96dfa700d5525054a0141de8c75763aae6734af0740e77e11261a1e8f4ffc08fd9ab78510122e05c21c2d79066c38bb6861a886c82
  languageName: node
  linkType: hard

"read-config-file@npm:6.3.2":
  version: 6.3.2
  resolution: "read-config-file@npm:6.3.2"
  dependencies:
    config-file-ts: "npm:^0.2.4"
    dotenv: "npm:^9.0.2"
    dotenv-expand: "npm:^5.1.0"
    js-yaml: "npm:^4.1.0"
    json5: "npm:^2.2.0"
    lazy-val: "npm:^1.0.4"
  checksum: 10c0/b249d165c342e32497ce2ca9f31cddad150111763c2199601e3b9a33b726ff0f7fefbea7680c19c422e2cac810b4b4daf5d1890c06ebf65ed6feef1173238f91
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regexp-util@npm:^2.0.0":
  version: 2.0.0
  resolution: "regexp-util@npm:2.0.0"
  dependencies:
    tslib: "npm:^2.6.0"
  checksum: 10c0/144afb48140bb91ea2fb01c494401bcddb347213e1a1cbf3fcfa7a28e630214a1069501872383730ea68c269b1236f4276be0bd9a03765838275a5a5c71953dd
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:^1.5.1":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 10c0/5e882475067f0b97dc07e0f37c3e335ac5bc3520d463f777cec7e894bb273eddbfecb857ae668e6fb6881fd6f6bb7148246967172139302da50fa12ea3a15d95
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: 10c0/b70b29c1843bc39781ef946c8cd4482e6d425976599c0f9c138cec8209e4e0736161bf39319b01676a847000085dfdaf63583c6fb4427bf751a10635bd2aa0c4
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: "npm:^2.0.0"
  checksum: 10c0/360b6deb5f101a9f8a4174f7837c523c3ec78b7ca8a7c1d45a1062b303659308a23757e318b1e91ed8684ad1205721142dd664d94771cd63499353fd4ee732b5
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10c0/c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"roarr@npm:^2.15.3":
  version: 2.15.4
  resolution: "roarr@npm:2.15.4"
  dependencies:
    boolean: "npm:^3.0.1"
    detect-node: "npm:^2.0.4"
    globalthis: "npm:^1.0.1"
    json-stringify-safe: "npm:^5.0.1"
    semver-compare: "npm:^1.0.0"
    sprintf-js: "npm:^1.1.2"
  checksum: 10c0/7d01d4c14513c461778dd673a8f9e53255221f8d04173aafeb8e11b23d8b659bb83f1c90cfe81af7f9c213b8084b404b918108fd792bda76678f555340cc64ec
  languageName: node
  linkType: hard

"rollup@npm:^4.13.0":
  version: 4.14.2
  resolution: "rollup@npm:4.14.2"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.14.2"
    "@rollup/rollup-android-arm64": "npm:4.14.2"
    "@rollup/rollup-darwin-arm64": "npm:4.14.2"
    "@rollup/rollup-darwin-x64": "npm:4.14.2"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.14.2"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.14.2"
    "@rollup/rollup-linux-arm64-musl": "npm:4.14.2"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.14.2"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.14.2"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.14.2"
    "@rollup/rollup-linux-x64-gnu": "npm:4.14.2"
    "@rollup/rollup-linux-x64-musl": "npm:4.14.2"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.14.2"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.14.2"
    "@rollup/rollup-win32-x64-msvc": "npm:4.14.2"
    "@types/estree": "npm:1.0.5"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/3826aaa847a16e8ac0459e20e3ec624295110c0aac2c7b4f970f75ca804d7437bbd002a37079410f71137691aab64649e65361018647b0412911358a17b97902
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sanitize-filename@npm:^1.6.3":
  version: 1.6.3
  resolution: "sanitize-filename@npm:1.6.3"
  dependencies:
    truncate-utf8-bytes: "npm:^1.0.0"
  checksum: 10c0/16ff47556a6e54e228c28db096bedd303da67b030d4bea4925fd71324932d6b02c7b0446f00ad33987b25b6414f24ae968e01a1a1679ce599542e82c4b07eb1f
  languageName: node
  linkType: hard

"sass@npm:^1.72.0":
  version: 1.75.0
  resolution: "sass@npm:1.75.0"
  dependencies:
    chokidar: "npm:>=3.0.0 <4.0.0"
    immutable: "npm:^4.0.0"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 10c0/1564ab2c8041c99a330cec93127fe8abcf65ac63eecb471610ed7f3126a2599a58b788a3a98eb8719f7f40b9b04e00c92bc9e11a9c2180ad582b8cba9fb030b0
  languageName: node
  linkType: hard

"sax@npm:^1.2.4":
  version: 1.3.0
  resolution: "sax@npm:1.3.0"
  checksum: 10c0/599dbe0ba9d8bd55e92d920239b21d101823a6cedff71e542589303fa0fa8f3ece6cf608baca0c51be846a2e88365fac94a9101a9c341d94b98e30c4deea5bea
  languageName: node
  linkType: hard

"semver-compare@npm:^1.0.0":
  version: 1.0.0
  resolution: "semver-compare@npm:1.0.0"
  checksum: 10c0/9ef4d8b81847556f0865f46ddc4d276bace118c7cb46811867af82e837b7fc473911981d5a0abc561fa2db487065572217e5b06e18701c4281bcdd2a1affaff1
  languageName: node
  linkType: hard

"semver@npm:^6.2.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.2, semver@npm:^7.3.5, semver@npm:^7.3.6, semver@npm:^7.3.8, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0":
  version: 7.6.0
  resolution: "semver@npm:7.6.0"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fbfe717094ace0aa8d6332d7ef5ce727259815bd8d8815700853f4faf23aacbd7192522f0dc5af6df52ef4fa85a355ebd2f5d39f554bd028200d6cf481ab9b53
  languageName: node
  linkType: hard

"serialize-error@npm:^7.0.1":
  version: 7.0.1
  resolution: "serialize-error@npm:7.0.1"
  dependencies:
    type-fest: "npm:^0.13.1"
  checksum: 10c0/7982937d578cd901276c8ab3e2c6ed8a4c174137730f1fb0402d005af209a0e84d04acc874e317c936724c7b5b26c7a96ff7e4b8d11a469f4924a4b0ea814c05
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-update-notifier@npm:2.0.0":
  version: 2.0.0
  resolution: "simple-update-notifier@npm:2.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/2a00bd03bfbcbf8a737c47ab230d7920f8bfb92d1159d421bdd194479f6d01ebc995d13fbe13d45dace23066a78a3dc6642999b4e3b38b847e6664191575b20c
  languageName: node
  linkType: hard

"sirv@npm:^2.0.3":
  version: 2.0.4
  resolution: "sirv@npm:2.0.4"
  dependencies:
    "@polka/url": "npm:^1.0.0-next.24"
    mrmime: "npm:^2.0.0"
    totalist: "npm:^3.0.0"
  checksum: 10c0/68f8ee857f6a9415e9c07a1f31c7c561df8d5f1b1ba79bee3de583fa37da8718def5309f6b1c6e2c3ef77de45d74f5e49efc7959214443aa92d42e9c99180a4e
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slice-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "slice-ansi@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10c0/88083c9d0ca67d09f8b4c78f68833d69cabbb7236b74df5d741ad572bbf022deaf243fa54009cd434350622a1174ab267710fcc80a214ecc7689797fe00cb27c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.0.2, smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"G-VoTest@workspace:.":
  version: 0.0.0-use.local
  resolution: "G-VoTest@workspace:."
  dependencies:
    "@electron-toolkit/eslint-config": "npm:^1.0.2"
    "@electron-toolkit/eslint-config-ts": "npm:^1.0.1"
    "@electron-toolkit/preload": "npm:^3.0.0"
    "@electron-toolkit/tsconfig": "npm:^1.0.1"
    "@electron-toolkit/utils": "npm:^3.0.0"
    "@element-plus/icons-vue": "npm:^2.3.1"
    "@rushstack/eslint-patch": "npm:^1.7.1"
    "@types/node": "npm:^18.19.9"
    "@univerjs/core": "npm:^0.1.4"
    "@univerjs/design": "npm:^0.1.4"
    "@univerjs/docs": "npm:^0.1.4"
    "@univerjs/docs-ui": "npm:^0.1.4"
    "@univerjs/engine-formula": "npm:^0.1.4"
    "@univerjs/engine-render": "npm:^0.1.4"
    "@univerjs/sheets": "npm:^0.1.4"
    "@univerjs/sheets-formula": "npm:^0.1.4"
    "@univerjs/sheets-ui": "npm:^0.1.4"
    "@univerjs/ui": "npm:^0.1.4"
    "@vitejs/plugin-vue": "npm:^5.0.3"
    "@vue/eslint-config-prettier": "npm:^9.0.0"
    "@vue/eslint-config-typescript": "npm:^12.0.0"
    "@vueuse/core": "npm:^10.9.0"
    axios: "npm:^1.6.8"
    electron: "npm:^28.2.0"
    electron-builder: "npm:^24.9.1"
    electron-dl: "npm:^3.5.2"
    electron-updater: "npm:^6.1.7"
    electron-vite: "npm:^2.0.0"
    element-plus: "npm:^2.6.3"
    eslint: "npm:^8.56.0"
    eslint-plugin-vue: "npm:^9.20.1"
    file-saver: "npm:^2.0.5"
    html-docx-js-extends: "npm:^0.1.7"
    html-to-docx: "npm:^1.8.0"
    mammoth: "npm:^1.7.1"
    pinia: "npm:^2.1.7"
    postcss-pxtorem: "npm:^6.1.0"
    prettier: "npm:^3.2.4"
    quill: "npm:2.0.0-rc.5"
    quill-better-table: "npm:^1.2.10"
    sass: "npm:^1.72.0"
    typescript: "npm:^5.3.3"
    unocss: "npm:0.59.0-beta.1"
    vite: "npm:^5.0.12"
    vue: "npm:^3.4.15"
    vue-router: "npm:^4.3.0"
    vue-tsc: "npm:^1.8.27"
  languageName: unknown
  linkType: soft

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.3
  resolution: "socks-proxy-agent@npm:8.0.3"
  dependencies:
    agent-base: "npm:^7.1.1"
    debug: "npm:^4.3.4"
    socks: "npm:^2.7.1"
  checksum: 10c0/4950529affd8ccd6951575e21c1b7be8531b24d924aa4df3ee32df506af34b618c4e50d261f4cc603f1bfd8d426915b7d629966c8ce45b05fb5ad8c8b9a6459d
  languageName: node
  linkType: hard

"socks@npm:^2.7.1":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/d54a52bf9325165770b674a67241143a3d8b4e4c8884560c4e0e078aace2a728dffc7f70150660f51b85797c4e1a3b82f9b7aa25e0a0ceae1a243365da5c51a7
  languageName: node
  linkType: hard

"sort-keys-length@npm:^1.0.0":
  version: 1.0.1
  resolution: "sort-keys-length@npm:1.0.1"
  dependencies:
    sort-keys: "npm:^1.0.0"
  checksum: 10c0/4567d08aa859c7e48b7e2cba14a8ae09a100f6a3bd7cf5d21dccd808d6332c945b9a7e2230a95c16e0e6eac1a943cd050ae51a5d1b4c8ec4b1e89a5801be9aa2
  languageName: node
  linkType: hard

"sort-keys@npm:^1.0.0":
  version: 1.1.2
  resolution: "sort-keys@npm:1.1.2"
  dependencies:
    is-plain-obj: "npm:^1.0.0"
  checksum: 10c0/5dd383b0299a40277051f7498c3999520138e2eb50d422962f658738341c9e82349fad4a3024d5ba1a3122688fbaf958f2a472d4c53bade55515097c2ce15420
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0":
  version: 1.2.0
  resolution: "source-map-js@npm:1.2.0"
  checksum: 10c0/7e5f896ac10a3a50fe2898e5009c58ff0dc102dcb056ed27a354623a0ece8954d4b2649e1a1b2b52ef2e161d26f8859c7710350930751640e71e374fe2d321a4
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.19":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.2, sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.5
  resolution: "ssri@npm:10.0.5"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/b091f2ae92474183c7ac5ed3f9811457e1df23df7a7e70c9476eaa9a0c4a0c8fc190fb45acefbf023ca9ee864dd6754237a697dc52a0fb182afe65d8e77443d8
  languageName: node
  linkType: hard

"stat-mode@npm:^1.0.0":
  version: 1.0.0
  resolution: "stat-mode@npm:1.0.0"
  checksum: 10c0/89b66a538dbfd45038fefdaf5b2104dc6e911605af1c201793e9629592ed9fdc7bdd1bca42806d0d4167c6d9cacac1f3fda41ddfe334a5c1f898113da38fae74
  languageName: node
  linkType: hard

"string-template@npm:~0.2.0":
  version: 0.2.1
  resolution: "string-template@npm:0.2.1"
  checksum: 10c0/5dc9bd8741e50aaf1ebb616c64fdada32301dc52718692a7a13088285b96fecd1010ab612b348ef29c08dff4df4f96c8e80689ca855a578d01cc182e48199182
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"sumchecker@npm:^3.0.1":
  version: 3.0.1
  resolution: "sumchecker@npm:3.0.1"
  dependencies:
    debug: "npm:^4.1.0"
  checksum: 10c0/43c387be9dfe22dbeaf39dfa4ffb279847aeb37a42a8988c0b066f548bbd209aa8c65e03da29f2b29be1a66b577801bf89fff0007df4183db2f286263a9569e5
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"synckit@npm:^0.8.6":
  version: 0.8.8
  resolution: "synckit@npm:0.8.8"
  dependencies:
    "@pkgr/core": "npm:^0.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c3d3aa8e284f3f84f2f868b960c9f49239b364e35f6d20825a448449a3e9c8f49fe36cdd5196b30615682f007830d46f2ea354003954c7336723cb821e4b6519
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.12, tar@npm:^6.1.2":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"temp-file@npm:^3.4.0":
  version: 3.4.0
  resolution: "temp-file@npm:3.4.0"
  dependencies:
    async-exit-hook: "npm:^2.0.1"
    fs-extra: "npm:^10.0.0"
  checksum: 10c0/70e441909097346a930ae02278df9b0133cd02dddf0b49e5ddaade735fef1410a50a448a2a812106f97c045294c99cc19f26943eb88f1d728d41fbc445a40298
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10c0/02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"tiny-inflate@npm:^1.0.0":
  version: 1.0.3
  resolution: "tiny-inflate@npm:1.0.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"tiny-typed-emitter@npm:^2.1.0":
  version: 2.1.0
  resolution: "tiny-typed-emitter@npm:2.1.0"
  checksum: 10c0/522bed4c579ee7ee16548540cb693a3d098b137496110f5a74bff970b54187e6b7343a359b703e33f77c5b4b90ec6cebc0d0ec3dbdf1bd418723c5c3ce36d8a2
  languageName: node
  linkType: hard

"tmp-promise@npm:^3.0.2":
  version: 3.0.3
  resolution: "tmp-promise@npm:3.0.3"
  dependencies:
    tmp: "npm:^0.2.0"
  checksum: 10c0/23b47dcb2e82b14bbd8f61ed7a9d9353cdb6a6f09d7716616cfd27d0087040cd40152965a518e598d7aabe1489b9569bf1eebde0c5fadeaf3ec8098adcebea4e
  languageName: node
  linkType: hard

"tmp@npm:^0.2.0":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 10c0/3e809d9c2f46817475b452725c2aaa5d11985cf18d32a7a970ff25b568438e2c076c2e8609224feef3b7923fa9749b74428e3e634f6b8e520c534eef2fd24125
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: 10c0/b214d21dbfb4bce3452b6244b336806ffea9c05297148d32ebb428d5c43ce7545bdfc65a1ceb58c9ef4376a65c0cb2854d645f33961658b3e3b4f84910ddcdd7
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 10c0/4bb1fadb69c3edbef91c73ebef9d25b33bbf69afe1e37ce544d5f7d13854cda15e47132f3e0dc4cafe300ddb8578c77c50a65004d8b6e97e77934a69aa924863
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"truncate-utf8-bytes@npm:^1.0.0":
  version: 1.0.2
  resolution: "truncate-utf8-bytes@npm:1.0.2"
  dependencies:
    utf8-byte-length: "npm:^1.0.1"
  checksum: 10c0/af2b431fc4314f119b551e5fccfad49d4c0ef82e13ba9ca61be6567801195b08e732ce9643542e8ad1b3df44f3df2d7345b3dd34f723954b6bb43a14584d6b3c
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.3.0
  resolution: "ts-api-utils@npm:1.3.0"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 10c0/f54a0ba9ed56ce66baea90a3fa087a484002e807f28a8ccb2d070c75e76bde64bd0f6dce98b3802834156306050871b67eec325cb4e918015a360a3f0868c77c
  languageName: node
  linkType: hard

"tslib@npm:^1.13.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.6.0, tslib@npm:^2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 10c0/e03a8a4271152c8b26604ed45535954c0a45296e32445b4b87f8a5abdb2421f40b59b4ca437c4346af0f28179780d604094eb64546bee2019d903d01c6c19bdb
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-fest@npm:^0.13.1":
  version: 0.13.1
  resolution: "type-fest@npm:0.13.1"
  checksum: 10c0/0c0fa07ae53d4e776cf4dac30d25ad799443e9eef9226f9fddbb69242db86b08584084a99885cfa5a9dfe4c063ebdc9aa7b69da348e735baede8d43f1aeae93b
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10c0/dea9df45ea1f0aaa4e2d3bed3f9a0bfe9e5b2592bddb92eb1bf06e50bcf98dbb78189668cd8bc31a0511d3fc25539b4cd5c704497e53e93e2d40ca764b10bfc3
  languageName: node
  linkType: hard

"typescript@npm:^5.3.3":
  version: 5.4.5
  resolution: "typescript@npm:5.4.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/2954022ada340fd3d6a9e2b8e534f65d57c92d5f3989a263754a78aba549f7e6529acc1921913560a4b816c46dce7df4a4d29f9f11a3dc0d4213bb76d043251e
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.3.3#optional!builtin<compat/typescript>":
  version: 5.4.5
  resolution: "typescript@patch:typescript@npm%3A5.4.5#optional!builtin<compat/typescript>::version=5.4.5&hash=5adc0c"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/db2ad2a16ca829f50427eeb1da155e7a45e598eec7b086d8b4e8ba44e5a235f758e606d681c66992230d3fc3b8995865e5fd0b22a2c95486d0b3200f83072ec9
  languageName: node
  linkType: hard

"ufo@npm:^1.3.2, ufo@npm:^1.5.3":
  version: 1.5.3
  resolution: "ufo@npm:1.5.3"
  checksum: 10c0/1df10702582aa74f4deac4486ecdfd660e74be057355f1afb6adfa14243476cf3d3acff734ccc3d0b74e9bfdefe91d578f3edbbb0a5b2430fe93cd672370e024
  languageName: node
  linkType: hard

"unconfig@npm:^0.3.11":
  version: 0.3.13
  resolution: "unconfig@npm:0.3.13"
  dependencies:
    "@antfu/utils": "npm:^0.7.7"
    defu: "npm:^6.1.4"
    jiti: "npm:^1.21.0"
  checksum: 10c0/894bbaff814053b09dbde85639ba38d2ff6bd27c098302897f2cf58f3960d4135a8945b79709fcf3599dc9b72599e87dbef3f3fd1871acd1077cb03adef2b0da
  languageName: node
  linkType: hard

"underscore@npm:^1.13.1":
  version: 1.13.6
  resolution: "underscore@npm:1.13.6"
  checksum: 10c0/5f57047f47273044c045fddeb8b141dafa703aa487afd84b319c2495de2e685cecd0b74abec098292320d518b267c0c4598e45aa47d4c3628d0d4020966ba521
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"unicode-regex@npm:^4.0.0":
  version: 4.0.0
  resolution: "unicode-regex@npm:4.0.0"
  dependencies:
    regexp-util: "npm:^2.0.0"
  checksum: 10c0/5aceeb2f02304262a4b7a86397f8e98b799525e22d2eb7d46d90ea5217afd87baa89d81dc1edcebe9fcc87516fce91601e36874fdb25fbffbe0b9e01a230fada
  languageName: node
  linkType: hard

"unicode-trie@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-trie@npm:2.0.0"
  dependencies:
    pako: "npm:^0.2.5"
    tiny-inflate: "npm:^1.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 10c0/6363e40b2fa758eb5ec5e21b3c7fb83e5da8dcfbd866cc0c199d5534c42f03b9ea9ab069769cc388e1d7ab93b4eeef28ef506ab5f18d910ef29617715101884f
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/cb811d9d54eb5821b81b18205750be84cb015c20a4a44280794e915f5a0a70223ce39066781a354e872df3572e8155c228f43ff0cce94c7cbf4da2cc7cbdd635
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10c0/e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unocss@npm:0.59.0-beta.1":
  version: 0.59.0-beta.1
  resolution: "unocss@npm:0.59.0-beta.1"
  dependencies:
    "@unocss/astro": "npm:0.59.0-beta.1"
    "@unocss/cli": "npm:0.59.0-beta.1"
    "@unocss/core": "npm:0.59.0-beta.1"
    "@unocss/extractor-arbitrary-variants": "npm:0.59.0-beta.1"
    "@unocss/postcss": "npm:0.59.0-beta.1"
    "@unocss/preset-attributify": "npm:0.59.0-beta.1"
    "@unocss/preset-icons": "npm:0.59.0-beta.1"
    "@unocss/preset-mini": "npm:0.59.0-beta.1"
    "@unocss/preset-tagify": "npm:0.59.0-beta.1"
    "@unocss/preset-typography": "npm:0.59.0-beta.1"
    "@unocss/preset-uno": "npm:0.59.0-beta.1"
    "@unocss/preset-web-fonts": "npm:0.59.0-beta.1"
    "@unocss/preset-wind": "npm:0.59.0-beta.1"
    "@unocss/reset": "npm:0.59.0-beta.1"
    "@unocss/transformer-attributify-jsx": "npm:0.59.0-beta.1"
    "@unocss/transformer-attributify-jsx-babel": "npm:0.59.0-beta.1"
    "@unocss/transformer-compile-class": "npm:0.59.0-beta.1"
    "@unocss/transformer-directives": "npm:0.59.0-beta.1"
    "@unocss/transformer-variant-group": "npm:0.59.0-beta.1"
    "@unocss/vite": "npm:0.59.0-beta.1"
  peerDependencies:
    "@unocss/webpack": 0.59.0-beta.1
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
  peerDependenciesMeta:
    "@unocss/webpack":
      optional: true
    vite:
      optional: true
  checksum: 10c0/032df2619983e76767944ffe214eab6f10f9c8add880e7cb9685a67645f979504469bdffc784699d10663d6582eb74f9ad8cc9ddfeae28ce3bf9529249776ce9
  languageName: node
  linkType: hard

"unused-filename@npm:^2.1.0":
  version: 2.1.0
  resolution: "unused-filename@npm:2.1.0"
  dependencies:
    modify-filename: "npm:^1.1.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/db2eeb8e42e3198203eefb290c4127dc7f10fc18fd6f877e80cbcd3f04123fe03337096b74f457221d2dd772531816cf9d55a664d54798d8da12d6225cfcad70
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.13":
  version: 1.0.13
  resolution: "update-browserslist-db@npm:1.0.13"
  dependencies:
    escalade: "npm:^3.1.1"
    picocolors: "npm:^1.0.0"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/e52b8b521c78ce1e0c775f356cd16a9c22c70d25f3e01180839c407a5dc787fb05a13f67560cbaf316770d26fa99f78f1acd711b1b54a4f35d4820d4ea7136e6
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"utf8-byte-length@npm:^1.0.1":
  version: 1.0.4
  resolution: "utf8-byte-length@npm:1.0.4"
  checksum: 10c0/78eeae05e7b44cd5cd382f00477fe07f5f14e04e83625cd5680e4b41ec29630fb8f85a553a650ae4131216019ef0569169990015e34619d3a2906380ecac6da8
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"verror@npm:^1.10.0":
  version: 1.10.1
  resolution: "verror@npm:1.10.1"
  dependencies:
    assert-plus: "npm:^1.0.0"
    core-util-is: "npm:1.0.2"
    extsprintf: "npm:^1.2.0"
  checksum: 10c0/293fb060a4c9b07965569a0c3e45efa954127818707995a8a4311f691b5d6687be99f972c759838ba6eecae717f9af28e3c49d2afc7bbdf5f0b675238f1426e8
  languageName: node
  linkType: hard

"virtual-dom@npm:^2.1.1":
  version: 2.1.1
  resolution: "virtual-dom@npm:2.1.1"
  dependencies:
    browser-split: "npm:0.0.1"
    error: "npm:^4.3.0"
    ev-store: "npm:^7.0.0"
    global: "npm:^4.3.0"
    is-object: "npm:^1.0.1"
    next-tick: "npm:^0.2.2"
    x-is-array: "npm:0.1.0"
    x-is-string: "npm:0.1.0"
  checksum: 10c0/15b89e0ae3d259a190c0489c74b3119f10e93fe55d108f394749a1a324e413e22fbc98710538b31cfaafbedd2ab0ff5379c61790e20778b923e33375f47d3cfb
  languageName: node
  linkType: hard

"vite@npm:^5.0.12":
  version: 5.2.8
  resolution: "vite@npm:5.2.8"
  dependencies:
    esbuild: "npm:^0.20.1"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.4.38"
    rollup: "npm:^4.13.0"
  peerDependencies:
    "@types/node": ^18.0.0 || >=20.0.0
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/b5717bb00c2570c08ff6d8ed917655e79184efcafa9dd62d52eea19c5d6dfc5a708ec3de9ebc670a7165fc5d401c2bdf1563bb39e2748d8e51e1593d286a9a13
  languageName: node
  linkType: hard

"vue-demi@npm:*, vue-demi@npm:>=0.14.5, vue-demi@npm:>=0.14.7":
  version: 0.14.7
  resolution: "vue-demi@npm:0.14.7"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 10c0/303216e3e6ee3f6ab5631488dd00a767ef3760a0a14e580c0223b278d093dc9ada8164ecec6bf8d8e12034e0bdf8dbb947c0c6f83095c6a53030a4a6dcbd57ce
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^9.3.1, vue-eslint-parser@npm:^9.4.2":
  version: 9.4.2
  resolution: "vue-eslint-parser@npm:9.4.2"
  dependencies:
    debug: "npm:^4.3.4"
    eslint-scope: "npm:^7.1.1"
    eslint-visitor-keys: "npm:^3.3.0"
    espree: "npm:^9.3.1"
    esquery: "npm:^1.4.0"
    lodash: "npm:^4.17.21"
    semver: "npm:^7.3.6"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10c0/79593073adbce8971565133c70a203f12f0be0f8c5e3a4063796fd56e5de64f1f3ad7f91be5a787a7a3fe751306ed22086ee8369d52725be95f452827ce670de
  languageName: node
  linkType: hard

"vue-router@npm:^4.3.0":
  version: 4.3.0
  resolution: "vue-router@npm:4.3.0"
  dependencies:
    "@vue/devtools-api": "npm:^6.5.1"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10c0/72865ad2e5e17035f1d38311caaec77759f9813f3a627553994c05c43d04ceeaf1f9325260be4c060411c49ed176035579bf4815a4c084b908688c0cc6b3cf72
  languageName: node
  linkType: hard

"vue-template-compiler@npm:^2.7.14":
  version: 2.7.16
  resolution: "vue-template-compiler@npm:2.7.16"
  dependencies:
    de-indent: "npm:^1.0.2"
    he: "npm:^1.2.0"
  checksum: 10c0/66667ffd5095b707f169c902c4f1a011e9d5ab99fc228e4dac14eb5ca7f107ed99bff261b21578a4b391d2f3d320a8050e754404443472acad13ddaa4bd7bae2
  languageName: node
  linkType: hard

"vue-tsc@npm:^1.8.27":
  version: 1.8.27
  resolution: "vue-tsc@npm:1.8.27"
  dependencies:
    "@volar/typescript": "npm:~1.11.1"
    "@vue/language-core": "npm:1.8.27"
    semver: "npm:^7.5.4"
  peerDependencies:
    typescript: "*"
  bin:
    vue-tsc: bin/vue-tsc.js
  checksum: 10c0/6e6ba37eb7a0c8b9cc613225729c74edf8bd0632d265e62aca28b1969b5615b9dbe2de03aefb8aed2e26fdbd4b93f134785c8ab0095f92c2469192e2db5d09fd
  languageName: node
  linkType: hard

"vue@npm:>=3.0.0, vue@npm:^3.4.15":
  version: 3.4.21
  resolution: "vue@npm:3.4.21"
  dependencies:
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/runtime-dom": "npm:3.4.21"
    "@vue/server-renderer": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/70806fdfe2f34387dd043403f3cf0946f43216090832c2a0be06e88d6feabba28a0aa05dc75777a1cb1aa770b24c015206519a20752faf0f6ed4d9b7568f0400
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/449fa5c44ed120ccecfe18c433296a4978a7583bf2391c50abce13f76878d2476defde04d0f79db8165bdf432853c1f8389d0485ca6e8ebce3bbcded513d5e6a
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"x-is-array@npm:0.1.0":
  version: 0.1.0
  resolution: "x-is-array@npm:0.1.0"
  checksum: 10c0/b559d9acfdffb3ea6c31e07b152439df2e85d98cc674846ccb785a003ff597bd3e31475d50eda62296c1503284c69c00bbb1f1dd5b3958b32aa72ea26099c42c
  languageName: node
  linkType: hard

"x-is-string@npm:0.1.0":
  version: 0.1.0
  resolution: "x-is-string@npm:0.1.0"
  checksum: 10c0/1ec001229eb17ac5c08c5bfb03c6d6e415306a6e923c0dbcadc5cb655b6008f61d50f29fcf610c9527bcbb9df40db82fb3cc9aefcb575898fba2affa17864f1c
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: 10c0/c1bfa219d64e56fee265b2bd31b2fcecefc063ee802da1e73bad1f21d7afd89b943c9e2c97af2942f60b1ad46f915a4c81e00039c7d398b53cf410e29d3c30bd
  languageName: node
  linkType: hard

"xmlbuilder2@npm:2.1.2":
  version: 2.1.2
  resolution: "xmlbuilder2@npm:2.1.2"
  dependencies:
    "@oozcitak/dom": "npm:1.15.5"
    "@oozcitak/infra": "npm:1.0.5"
    "@oozcitak/util": "npm:8.3.3"
  checksum: 10c0/150fa7b7e28c3fac9ccaf2389a32dd20d2ad63287ff4cf7785f09bef69378226599259e281307948050bc6766c526560dcdb1404ed1160a5591dcb206f33da51
  languageName: node
  linkType: hard

"xmlbuilder@npm:>=11.0.1, xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 10c0/665266a8916498ff8d82b3d46d3993913477a254b98149ff7cff060d9b7cc0db7cf5a3dae99aed92355254a808c0e2e3ec74ad1b04aa1061bdb8dfbea26c18b8
  languageName: node
  linkType: hard

"xmlbuilder@npm:^10.0.0":
  version: 10.1.1
  resolution: "xmlbuilder@npm:10.1.1"
  checksum: 10c0/26c465e8bd16b4e882d39c2e2a29bb277434d254717aa05df117dd0009041d92855426714b2d1a6a5f76983640349f4edb80073b6ae374e0e6c3d13029ea8237
  languageName: node
  linkType: hard

"xtend@npm:~4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yauzl@npm:^2.10.0":
  version: 2.10.0
  resolution: "yauzl@npm:2.10.0"
  dependencies:
    buffer-crc32: "npm:~0.2.3"
    fd-slicer: "npm:~1.1.0"
  checksum: 10c0/f265002af7541b9ec3589a27f5fb8f11cf348b53cc15e2751272e3c062cd73f3e715bc72d43257de71bbaecae446c3f1b14af7559e8ab0261625375541816422
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard
