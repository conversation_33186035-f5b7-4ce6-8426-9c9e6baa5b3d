			      SoX: Sound eXchange
			      ===================

SoX (Sound eXchange) is the Swiss Army knife of sound processing tools: it
can convert sound files between many different file formats & audio devices,
and can apply many sound effects & transformations, as well as doing basic
analysis and providing input to more capable analysis and plotting tools.

SoX is licensed under the GNU GPL and GNU LGPL.  To be precise, the 'sox'
and 'soxi' programs are distributed under the GPL, while the library
'libsox' (in which most of SoX's functionality resides) is dual-licensed.
Note that some optional components of libsox are GPL only: if you use these,
you must use libsox under the GPL.  See INSTALL for the list of optional
components and their licences.

If this distribution is of source code (as opposed to pre-built binaries),
then you will need to compile and install SoX as described in the 'INSTALL'
file.

Changes between this release and previous releases of SoX can be found in
the 'ChangeLog' file; a summary of the file formats and effects supported in
this release can be found below.  Detailed documentation for using SoX can
be found in the distributed 'man' pages:

  o  sox(1)
  o  soxi(1)
  o  soxformat(7)
  o  libsox(3)

or in plain text or PDF files for those systems without man.

The majority of SoX features and fixes are contributed by SoX users - thank
you very much for making SoX a success!  There are several new features
wanted for SoX, listed on the feature request tracker at the SoX project
home-page:

		    http://sourceforge.net/projects/sox

users are encouraged to implement them!

Please submit bug reports, new feature requests, and patches to the relevant
tracker at the above address, or by email:

		   mailto:<EMAIL>

Also accessible via the project home-page is the SoX users' discussion
mailing list which you can join to discuss all matters SoX with other SoX
users; the mail address for this list is:

		   mailto:<EMAIL>

The current release handles the following audio file formats:


  o  Raw files in various binary formats
  o  Raw textual data
  o  Amiga 8svx files
  o  Apple/SGI AIFF files
  o  SUN .au files
    o  PCM, u-law, A-law
    o  G7xx ADPCM files (read only)
    o  mutant DEC .au files
    o  NeXT .snd files
  o  AVR files
  o  CDDA (Compact Disc Digital Audio format)
  o  CVS and VMS files (continuous variable slope)
  o  Grandstream ring-tone files
  o  GSM files
  o  HTK files
  o  LPC-10 files
  o  Macintosh HCOM files
  o  Amiga MAUD files
  o  AMR-WB & AMR-NB (with optional libamrwb & libamrnb libraries)
  o  MP2/MP3 (with optional libmad, libtwolame and libmp3lame libraries)
  o  Opus files (read only; with optional Opus libraries)

  o  Ogg Vorbis files (with optional Ogg Vorbis libraries)
  o  FLAC files (with optional libFLAC)
  o  IRCAM SoundFile files
  o  NIST SPHERE files
  o  Turtle beach SampleVision files
  o  Sounder & Soundtool (DOS) files
  o  Yamaha TX-16W sampler files
  o  SoundBlaster .VOC files
  o  Dialogic/OKI ADPCM files (.VOX)
  o  Microsoft .WAV files
    o  PCM, floating point
    o  u-law, A-law, MS ADPCM, IMA (DMI) ADPCM
    o  GSM
    o  RIFX (big endian)
  o  WavPack files (with optional libwavpack library)
  o  Psion (palmtop) A-law WVE files and Record voice notes
  o  Maxis XA Audio files
    o  EA ADPCM (read support only, for now)
  o  Pseudo formats that allow direct playing/recording from most audio devices
  o  The "null" pseudo-file that reads and writes from/to nowhere


The audio effects/tools included in this release are as follows:

  o  Tone/filter effects
    o  allpass: RBJ all-pass biquad IIR filter
    o  bandpass: RBJ band-pass biquad IIR filter
    o  bandreject: RBJ band-reject biquad IIR filter
    o  band: SPKit resonator band-pass IIR filter
    o  bass: Tone control: RBJ shelving biquad IIR filter
    o  equalizer: RBJ peaking equalisation biquad IIR filter
    o  firfit+: FFT convolution FIR filter using given freq. response (W.I.P.)
    o  highpass: High-pass filter: Single pole or RBJ biquad IIR
    o  hilbert: Hilbert transform filter (90 degrees phase shift)
    o  lowpass: Low-pass filter: single pole or RBJ biquad IIR
    o  sinc: Sinc-windowed low/high-pass/band-pass/reject FIR
    o  treble: Tone control: RBJ shelving biquad IIR filter

  o  Production effects
    o  chorus: Make a single instrument sound like many
    o  delay: Delay one or more channels
    o  echo: Add an echo
    o  echos: Add a sequence of echos
    o  flanger: Stereo flanger
    o  overdrive: Non-linear distortion
    o  phaser: Phase shifter
    o  repeat: Loop the audio a number of times
    o  reverb: Add reverberation
    o  reverse: Reverse the audio (to search for Satanic messages ;-)
    o  tremolo: Sinusoidal volume modulation

  o  Volume/level effects
    o  compand: Signal level compression/expansion/limiting
    o  contrast: Phase contrast volume enhancement
    o  dcshift: Apply or remove DC offset
    o  fade: Apply a fade-in and/or fade-out to the audio
    o  gain: Apply gain or attenuation; normalise/equalise/balance/headroom
    o  loudness: Gain control with ISO 226 loudness compensation
    o  mcompand: Multi-band compression/expansion/limiting
    o  norm: Normalise to 0dB (or other)
    o  vol: Adjust audio volume

  o  Editing effects
    o  pad: Pad (usually) the ends of the audio with silence
    o  silence: Remove portions of silence from the audio
    o  splice: Perform the equivalent of a cross-faded tape splice
    o  trim: Cuts portions out of the audio
    o  vad: Voice activity detector

  o  Mixing effects
    o  channels: Auto mix or duplicate to change number of channels
    o  divide+: Divide sample values by those in the 1st channel (W.I.P.)
    o  remix: Produce arbitrarily mixed output channels
    o  swap: Swap pairs of channels

  o  Pitch/tempo effects
    o  bend: Bend pitch at given times without changing tempo
    o  pitch: Adjust pitch (= key) without changing tempo
    o  speed: Adjust pitch & tempo together
    o  stretch: Adjust tempo without changing pitch (simple alg.)
    o  tempo: Adjust tempo without changing pitch (WSOLA alg.)

  o  Mastering effects
    o  dither: Add dither noise to increase quantisation SNR
    o  rate: Change audio sampling rate

  o  Specialised filters/mixers
    o  deemph: ISO 908 CD de-emphasis (shelving) IIR filter
    o  earwax: Process CD audio to best effect for headphone use
    o  noisered: Filter out noise from the audio
    o  oops: Out Of Phase Stereo (or `Karaoke') effect
    o  riaa: RIAA vinyl playback equalisation

  o  Analysis `effects'
    o  noiseprof: Produce a DFT profile of the audio (use with noisered)
    o  spectrogram: graph signal level vs. frequency & time (needs `libpng')
    o  stat: Enumerate audio peak & RMS levels, approx. freq., etc.
    o  stats: Multichannel aware `stat'

  o  Miscellaneous effects
    o  ladspa: Apply LADSPA plug-in effects e.g. CMT (Computer Music Toolkit)
    o  synth: Synthesise/modulate audio tones or noise signals
    o  newfile: Create a new output file when an effects chain ends.
    o  restart: Restart 1st effects chain when multiple chains exist.

  o  Low-level signal processing effects
    o  biquad: 2nd-order IIR filter using externally provided coefficients
    o  downsample: Reduce sample rate by discarding samples
    o  fir: FFT convolution FIR filter using externally provided coefficients
    o  upsample: Increase sample rate by zero stuffing

  + Experimental or incomplete effect; may change in future.

Multiple audio files can be combined (and then further processed with
effects) using any one of the following combiner methods:

  o  concatenate
  o  mix
  o  merge: E.g. two mono files to one stereo file
  o  sequence: For playing multiple audio files/streams
