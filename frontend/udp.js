const dgram = require('dgram');

// 创建服务器
const server = dgram.createSocket('udp4');

// 定义当接收到消息时的处理函数
server.on('message', (msg, rinfo) => {
  console.log(`服务器接收到的消息：${msg.toString()}，来自：${rinfo.address}:${rinfo.port}`);
});

// 监听错误事件
server.on('error', (err) => {
  console.error(`服务器发生了错误：\n${err.stack}`);
  server.close();
});

// 当服务器准备监听时
server.on('listening', () => {
  const address = server.address();
  console.log(`UDP 服务器正在监听 ${address.address}:${address.port}`);
});

// 绑定到特定的端口和IP地址
// 这里我们绑定到 40000 端口，监听所有网络接口
server.bind(1080);
