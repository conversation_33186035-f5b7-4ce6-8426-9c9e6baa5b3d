{"name": "G-VoTest", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "mac": {"icon": "build/icons/icon.icns", "artifactName": "${productName}_setup_${version}-${os}-${arch}.${ext}"}, "win": {"icon": "build/icons/icon.ico", "artifactName": "${productName}_setup_${version}-${os}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeader": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true}, "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@element-plus/icons-vue": "^2.3.1", "@univerjs/core": "^0.1.7", "@univerjs/design": "^0.1.7", "@univerjs/docs": "^0.1.7", "@univerjs/docs-ui": "^0.1.7", "@univerjs/engine-formula": "^0.1.7", "@univerjs/engine-render": "^0.1.7", "@univerjs/facade": "^0.1.7", "@univerjs/sheets": "^0.1.7", "@univerjs/sheets-formula": "^0.1.7", "@univerjs/sheets-ui": "^0.1.7", "@univerjs/ui": "^0.1.7", "@vitejs/plugin-vue-jsx": "^4.1.0", "@vueuse/core": "^10.9.0", "ant-design-vue": "^4.2.5", "axios": "^1.6.8", "chimee-player": "^1.4.9", "echarts": "^5.6.0", "electron-dl": "^3.5.2", "electron-log": "^5.2.0", "electron-updater": "^6.1.7", "element-plus": "^2.6.3", "file-saver": "^2.0.5", "html-to-docx": "^1.8.0", "js-audio-recorder": "^1.0.7", "lamejs": "^1.2.1", "mammoth": "^1.7.1", "pinia": "^2.1.7", "postcss-pxtorem": "^6.1.0", "quill": "2.0.0-rc.5", "quill-better-table": "^1.2.10", "sass": "^1.72.0", "uuid": "^9.0.1", "video.js": "^8.19.1", "vite-plugin-node-stdlib-browser": "^0.2.1", "vue-contextmenu": "^1.5.11", "vue-draggable-resizable": "^3.0.0", "vue-echarts": "^7.0.3", "vue-json-pretty": "^2.4.0", "vue-router": "^4.3.0", "vuetify": "^3.7.6", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.7.1", "@types/node": "^18.19.9", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "electron": "^28.2.0", "electron-builder": "^24.9.1", "electron-vite": "^2.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "html-docx-js-extends": "^0.1.7", "patch-package": "^8.0.0", "prettier": "^3.2.4", "typescript": "^5.3.3", "unocss": "0.59.0-beta.1", "vite": "^5.0.12", "vue": "^3.4.15", "vue-tsc": "^1.8.27"}, "pnpm": {}}