<svg xmlns="http://www.w3.org/2000/svg" width="78" height="86" viewBox="0 0 78 86" fill="none">
  <path d="M52.9497 46L67.4454 60.4957C68.8122 61.8625 68.8122 64.0786 67.4454 65.4454C66.0786 66.8123 63.8625 66.8123 62.4956 65.4454L48 50.9497L52.9497 46Z" fill="#B2A8B4"/>
  <g filter="url(#filter0_b_147_13089)">
    <circle cx="33.5" cy="31.5" r="31.5" fill="url(#paint0_linear_147_13089)"/>
    <circle cx="33.5" cy="31.5" r="31" stroke="url(#paint1_linear_147_13089)"/>
  </g>
  <g filter="url(#filter1_f_147_13089)">
    <ellipse cx="39" cy="79" rx="35" ry="3" fill="url(#paint2_radial_147_13089)"/>
  </g>
  <defs>
    <filter id="filter0_b_147_13089" x="-2" y="-4" width="71" height="71" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_147_13089"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_147_13089" result="shape"/>
    </filter>
    <filter id="filter1_f_147_13089" x="0" y="72" width="78" height="14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_147_13089"/>
    </filter>
    <linearGradient id="paint0_linear_147_13089" x1="9.5" y1="14" x2="58.5" y2="50" gradientUnits="userSpaceOnUse">
      <stop stop-color="#EAE7F7" stop-opacity="0.89"/>
      <stop offset="0.468714" stop-color="#B9B9D1" stop-opacity="0.4"/>
      <stop offset="1" stop-color="#EEEEEE" stop-opacity="0.41"/>
    </linearGradient>
    <linearGradient id="paint1_linear_147_13089" x1="10.5" y1="13" x2="54.5" y2="55.5" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="0.342395" stop-color="white" stop-opacity="0.49"/>
    </linearGradient>
    <radialGradient id="paint2_radial_147_13089" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(39 79) scale(35 3)">
      <stop stop-color="#B2A8B4"/>
      <stop offset="1" stop-color="#F1F1F4"/>
    </radialGradient>
  </defs>
</svg>
