:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
}

* {
  margin: 0;
  padding: 0;
}

// 表单 grid 两栏布局
.form-column-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 1rem;
  .el-form-item {
    margin-bottom: 0;
  }
}

// 覆盖element-plus表格样式
#app {
  --el-color-primary: #646cff;
  --el-border-radius-base: 2px;
  .el-table {
    --el-table-header-bg-color: #ccc;
    --el-table-header-text-color: #000;
  }
  .el-button {
    border-radius: 4px;

    //background: #335FFA;
    //color: #fff;
  }
  .el-button--primary {
    background: linear-gradient(90deg, #d568cf 0.23%, #335ffa 99.77%);
    color: #fff;
    border: none;
    &.is-disabled{
      background: #a0cfff;
      color: #fff;
    }
  }
}

.no-drag {
  -webkit-app-region: no-drag;
}

.clamp-1 {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.progress-text {
  background: linear-gradient(90deg, #d468d0 0%, #7f64e7 34.38%, #2d4fff 62.02%, #37fafa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.ant-btn-link {
  color: var(--color-button-primary) !important; 
}

.ant-tree-node-content-wrapper {
  padding: 0 !important;
}

.ant-breadcrumb {
  margin-top: 0.5rem !important;
  margin-bottom: 0.3rem !important;
}

.ant-card-head {
  background-color: #f7f7f7 !important;
  min-height: 2.75rem !important;
}

.ant-breadcrumb {
  margin-bottom: 0.5rem !important;
}

.ant-transfer-list-header::before {
  content: "操作" !important;
  /* 在这里填写你想要添加的文字 */

  /* 调整文字与图标之间的间距 */
}