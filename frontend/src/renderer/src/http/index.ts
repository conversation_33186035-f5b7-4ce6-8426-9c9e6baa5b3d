import axios from 'axios'
import { ElMessage } from 'element-plus'
import { ElNotification } from 'element-plus'
// 定义一个标识符，用于判断是否需要更新 baseURL
const LOCAL_STORAGE_KEY = 'serverPort';
const LOCAL_STORAGE_UPDATED_KEY = 'serverPortUpdated';

let urlCustomed;
if (process.env.NODE_ENV !== 'production') {
  urlCustomed = localStorage.getItem(LOCAL_STORAGE_KEY) + 'dev-api';
} else {
  urlCustomed = localStorage.getItem(LOCAL_STORAGE_KEY);
}

console.log(urlCustomed);
console.log(import.meta.env.VITE_APP_BASE_API);
// 获取初始的 baseURL
let baseURL = urlCustomed || (process.env.NODE_ENV === 'production'
  ? import.meta.env.VITE_APP_PRODUCTION
  : import.meta.env.VITE_APP_BASE_API);

if (process.env.NODE_ENV !== 'production') {
  baseURL = import.meta.env.VITE_APP_BASE_API
}
// http://127.0.0.1:8080/dev-api/device/config_get
// 创建 Axios 实例
const http = axios.create({
  baseURL: baseURL, // 设置初始的 baseURL
  timeout: 880000 // 设置请求超时时间
});

// 监听 localStorage 的变化
window.addEventListener('storage', (event) => {
  if (event.key === LOCAL_STORAGE_KEY) {
    const newServerPort = event.newValue;
    if (newServerPort && newServerPort !== baseURL) {
      // 更新 baseURL
      baseURL = newServerPort;
      http.defaults.baseURL = baseURL;
      console.info('Updated baseURL to ' + baseURL);
      // 设置更新标识符
      localStorage.setItem(LOCAL_STORAGE_UPDATED_KEY, Date.now().toString());
    }
  }
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前做一些处理，例如添加请求头、身份验证等
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }

    // 删除请求参数中的下划线开头的参数
    ;[config.params, config.data].forEach((params) => {
      for (const p in params) {
        if (p.startsWith('_')) {
          delete params[p]
        }
        // if(params[p] === "") {
        //   delete params[p];
        // }
      }
    })
    return config
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    if (response.data?.success) {
      ElNotification.success(response.data?.message);
      return response.data;
    }

    if (response.data?.data?.status !== 0 && (response.data?.err_msg || response.data?.error || response.data?.error_msg)) {
      ElNotification.error(response.data?.err_msg || response.data?.error || response.data?.error_msg)
    }
    if (response.status === 500) {
      ElMessage.error(response.data.message)
      return Promise.reject(response)
    }
    response.data.headers = response.headers;
    return response.data
  },
  (error) => {
    const response = error.response;
    if (response.data?.err_msg || response.data?.error || response.data?.error_msg) {
      ElNotification.error(response.data?.err_msg || response.data?.error || response.data?.error_msg);
    }
    if (error.response.status === 401) {
      window.location.href = '#/product'
    }
    // 处理响应错误
    return Promise.reject(error)
  }
)

export { http }