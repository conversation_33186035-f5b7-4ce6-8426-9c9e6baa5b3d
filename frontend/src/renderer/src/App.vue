<script lang="ts" setup>
import { onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from './themeStore'; // 引入 Pinia Store
import zhCN from 'ant-design-vue/es/locale/zh_CN';
const router = useRouter();
const themeStore = useThemeStore();

const buttonColorPrimary = computed(() => themeStore.buttonColorPrimary);

const lightenColor = (color: string, amount: number) => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  const lighten = (c: number) => {
    c = Math.min(255, Math.max(0, c + amount));
    return c.toString(16).padStart(2, '0');
  };

  return `#${lighten(r)}${lighten(g)}${lighten(b)}`;
};

const buttonColorPrimaryHover = computed(() => {
  return lightenColor(buttonColorPrimary.value, 30);
});

const buttonColorPrimaryActive = computed(() => {
  return lightenColor(buttonColorPrimary.value, -30);
});

onMounted(() => {
  router.push('/project')
})

document.addEventListener('keydown', event => {
    // 检测是否同时按下Ctrl + Shift + I,自动打开开发者模式
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
      // 阻止默认行为
      event.preventDefault();

      // 设置应用的控制台打开/关闭
      window.electron.ipcRenderer.send('SET_CONSOLE');
    }
})

</script>

<template>
  <a-config-provider :locale="zhCN" :theme="{
    token: {
      colorPrimary: themeStore.colorPrimary,
    },
    components: {
      Button: {
        colorPrimary: themeStore.buttonColorPrimary,
        colorPrimaryHover: buttonColorPrimaryHover,
        colorPrimaryActive: buttonColorPrimaryActive
      }
    }
  }">
    <router-view></router-view>
  </a-config-provider>
</template>

<style></style>