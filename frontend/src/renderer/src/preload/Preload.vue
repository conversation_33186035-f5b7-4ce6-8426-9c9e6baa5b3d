

<template>
    <div class="container">
        <div v-if="loading" class="cyber-loading">
            <div class="particle-ring">
                <!-- 手动创建12个粒子 -->
                <div class="particle p1"></div>
                <div class="particle p2"></div>
                <div class="particle p3"></div>
                <div class="particle p4"></div>
                <div class="particle p5"></div>
                <div class="particle p6"></div>
                <div class="particle p7"></div>
                <div class="particle p8"></div>
                <div class="particle p9"></div>
                <div class="particle p10"></div>
                <div class="particle p11"></div>
                <div class="particle p12"></div>
            </div>
            <div class="glow-text">
                <div style="display: flex;gap: 4rem">
                    <img :src="logo" class="logo" />
                    <div>西部创源</div>
                </div>
            </div>
        </div>


    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import logo from './logoPurple.jpg';

const loading = ref(true);

</script>

<style>
.container {
    position: relative;
}

.cyber-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.particle-ring {
    position: relative;
    width: 200px;
    height: 200px;
}

.particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #7028e4;
    border-radius: 50%;
    animation: particle 1.5s infinite ease-in-out;
}

/* 手动定义粒子位置 */
.p1 {
    top: 15%;
    left: 30%;
}

.p2 {
    top: 25%;
    left: 80%;
}

.p3 {
    top: 65%;
    left: 20%;
}

.p4 {
    top: 40%;
    left: 90%;
}

.p5 {
    top: 85%;
    left: 70%;
}

.p6 {
    top: 10%;
    left: 60%;
}

.p7 {
    top: 75%;
    left: 40%;
}

.p8 {
    top: 30%;
    left: 10%;
}

.p9 {
    top: 50%;
    left: 50%;
}

.p10 {
    top: 90%;
    left: 30%;
}

.p11 {
    top: 20%;
    left: 70%;
}

.p12 {
    top: 60%;
    left: 85%;
}

/* 定义粒子动画延迟 */
.p1 {
    animation-delay: 0.1s;
}

.p2 {
    animation-delay: 0.2s;
}

.p3 {
    animation-delay: 0.3s;
}

.p4 {
    animation-delay: 0.4s;
}

.p5 {
    animation-delay: 0.5s;
}

.p6 {
    animation-delay: 0.6s;
}

.p7 {
    animation-delay: 0.7s;
}

.p8 {
    animation-delay: 0.8s;
}

.p9 {
    animation-delay: 0.9s;
}

.p10 {
    animation-delay: 1.0s;
}

.p11 {
    animation-delay: 1.1s;
}

.p12 {
    animation-delay: 1.2s;
}

.glow-text {
    margin-top: 2rem;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 10px rgba(195, 64, 173, 0.8);
    letter-spacing: 2px;
    animation: textGlow 4.5s ease-in-out infinite;
}

@keyframes rotate {
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotateReverse {
    to {
        transform: rotate(-360deg);
    }
}

@keyframes particle {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.8;
    }

    50% {
        transform: scale(1.5);
        opacity: 0.2;
    }
}

@keyframes textGlow {

    0%,
    100% {
        opacity: 0.8;
        filter: drop-shadow(0 0 5px purple);
    }

    50% {
        opacity: 0.7;
        filter: drop-shadow(0 0 15px pink);
    }
}

.logo {
    height: 4rem;
    width: 4rem;
    margin-left: 3rem;
    margin-right: -2rem;
}
</style>