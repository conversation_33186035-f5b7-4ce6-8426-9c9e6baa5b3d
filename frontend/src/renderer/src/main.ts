import './assets/styles/index.scss'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'virtual:uno.css'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
const pinia = createPinia()

const app = createApp(App).use(pinia).use(ElementPlus).use(router);
const vuetify = createVuetify({
    components,
    directives,
  });
app.use(Antd);
app.use(vuetify);
app.mount('#app');
