<template>
    <a-tooltip :title="text" placement="top">
      <ExclamationCircleOutlined :style="{ color, fontSize: size, margin: '1rem' }" />
    </a-tooltip>
  </template>
  
  <script setup>
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  
  defineProps({
    // 提示文字
    text: {
      type: String,
      required: true,
    },
    // 图标颜色
    color: {
      type: String,
      default: 'grey', // 默认是 antd 的警告色
    },
    // 图标大小
    size: {
      type: String,
      default: '14px',
    },
  });
  </script>
  
  <style scoped>
  /* 可以在这里添加自定义样式 */
  </style>