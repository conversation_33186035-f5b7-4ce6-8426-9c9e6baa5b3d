<template>
    <div style="margin-bottom: 1rem; display:flex; align-items: center;">
        <h4 style="width: 8rem">误唤醒次数</h4>
        <a-input v-model:value="falseRouseTimes" :disabled="true" style="width: 16.5rem;"></a-input>
        <a-button type="primary" danger @click="stopFalseWakeupTest" style="margin-left: 1rem;">停止测试</a-button>
    </div>
    <a-table :columns="columns" :dataSource="pagedTestResults" :rowKey="record => record.result_id" bordered
        size="small" class="test-results-table" :pagination="false" :expand-column-width="50">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'time'">
                {{ new Date(record.time).toLocaleString() }}
            </template>
            <template v-else-if="column.key === 'relative_interval'">
                <a @click="seekVideo(record.relative_interval)" style="color: blue;">{{ formatTime(record.relative_interval) }}</a>
            </template>
            <template v-else-if="column.key === 'mic_audio_url'">
                <audio :src="getStaticUrl(record.mic_audio_url)" controls></audio>
            </template>
            <template v-else-if="column.key === 'image'">
                <el-image v-for="singleImage in getArray(record.image || [])" style="width: 50px; height: 50px" :src="getStaticPictureUrl(singleImage)" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                    :preview-src-list="[getStaticPictureUrl(singleImage)]" :initial-index="4" fit="cover" />
            </template>
        </template>
    </a-table>
    <a-pagination v-model:current="currentPage" :total="totalResults" :pageSize="pageSize" show-less-items
        @change="onPageChange" style="margin-top: 20px; text-align: center;" />
</template>
<script setup lang="ts">
import { ref, computed, toRef } from 'vue';
import { http } from '@renderer/http';
import { ElMessage } from 'element-plus';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';

const currentPage = ref(1);
const pageSize = ref(10);

const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);

const props = defineProps({
    testResults: {
        type: Array,
        required: true
    },
    previewImage: {
        type: Function,
        required: true
    },
    seekVideo: {
        type: Function,
        required: true
    }
})

const formatTime = (seconds) => {
    if (seconds === null || seconds === undefined) {
        return '--:--'
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

const falseRouseTimes = computed(() => props.testResults?.length || 0);

// 注释掉原来的 testResults
const testResults = toRef(props, 'testResults');

// mock数据
// const testResults = ref([
//     {
//         result_id: '18',
//         time: '2023-10-18T05:00:00Z',
//         asr_result: '唤醒响应18',
//         mic_audio_url: '/Users/<USER>/CVAtest/backend/mic_audio/E2Ey6c/1/E2DOpB/E2ph1M/E2ph1M_mic_0_full.wav',
//         image: '/Users/<USER>/CVAtest/backend/photo/xQ9rEJ/制作武侠立绘.png'
//     },
//     {
//         result_id: '19',
//         time: '2023-10-19T06:00:00Z',
//         asr_result: '唤醒响应19',
//         mic_audio_url: 'http://example.com/mic_audio/audio19.pcm',
//         image: 'http://example.com/photo/image19.jpg'
//     },
//     {
//         result_id: '20',
//         time: '2023-10-20T07:00:00Z',
//         asr_result: '唤醒响应20',
//         mic_audio_url: 'http://example.com/mic_audio/audio20.pcm',
//         image: 'http://example.com/photo/image20.jpg'
//     }
// ]);

const totalResults = computed(() => testResults.value.length);

const pagedTestResults = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    const currentPages = testResults.value.slice(start, end);
    for (const result of currentPages) {
        if (result.image && !Array.isArray(result.image)) {
            result.image = [result.image]
        }
    }
    return currentPages;
});

const onPageChange = (page: number) => {
    currentPage.value = page;
};

const columns = [
    { title: '误唤醒时刻', dataIndex: 'time', key: 'time', align: 'center' },
    { title: '唤醒响应', dataIndex: 'asr_result', key: 'asr_result', align: 'center' },
    { title: '唤醒录音', dataIndex: 'mic_audio_url', key: 'mic_audio_url', align: 'center' },
    { title: '唤醒图像', dataIndex: 'image', key: 'image', align: 'center' },
    { title: '跳转时间', dataIndex: 'relative_interval', key: 'relative_interval', align: 'center' },
];

const getArray = (image) => {
    if (Array.isArray(image)) {
        return image;
    }
    return [image]
}

const visibleMap = ref<{ [key: string]: boolean }>({});

const getStaticUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        return '/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    } else {
        return 'http://127.0.0.1:8080/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    }
}

const getStaticPictureUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        return '/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    } else {
        return 'http://127.0.0.1:8080/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    }
}

// 停止误唤醒测试
const stopFalseWakeupTest = async () => {
  try {
    const response = await http.post('/project/stop_test', {
      project_id: projectDetail.value.project_id
    });
    if (response.status === 0) {
      ElMessage.success('已停止误唤醒测试');
    } else {
      ElMessage.error('停止误唤醒测试失败');
    }
  } catch (error) {
    ElMessage.error('停止误唤醒测试失败');
    console.error(error);
  }
};
</script>