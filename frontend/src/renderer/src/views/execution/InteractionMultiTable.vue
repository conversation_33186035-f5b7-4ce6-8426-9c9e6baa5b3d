<template>
  <div style="margin-bottom: 20px;">
    <!-- 新增按钮 -->
    <a-button type="primary" @click="showRetestModal" :disabled="selectedRowKeys.length === 0"
      :style="{ marginRight: '1rem' }">
      生成复测方案
    </a-button>
    <a-button type="primary" @click="viewRetestPlan" :style="{ marginRight: '1rem' }"
      v-show="planId?.startsWith('retest')">
      复测方案配置
    </a-button>
    <a-button type="primary" @click="showAddToPlanModal" :disabled="selectedRowKeys.length === 0"
      :style="{ marginRight: '1rem' }">
      添加到方案
    </a-button>
    <a-button type="primary" @click="doRetest" v-show="planId?.startsWith('retest')" :style="{ marginRight: '1rem' }"
      :disabled="isCompleted">
      开始复测
    </a-button>

    <!-- 过滤器 -->
    <a-select v-model:value="filterResult" placeholder="请选择测试结果" style="width: 150px; margin-right: 10px;" allowClear>
      <a-select-option value="通过">通过</a-select-option>
      <a-select-option value="不通过">不通过</a-select-option>
    </a-select>

    <a-input v-model:value="filterSignlist" placeholder="搜索结果标记" style="width: 200px;" allowClear />
  </div>
  <a-table :columns="columns" :dataSource="pagedSubResults" :rowKey="record => record.result_id" bordered size="small"
    class="test-results-table" :pagination="false" :expand-column-width="50">
    <template #headerCell="{ column, record }">
      <template v-if="column.key === 'selection'">
        <a-checkbox :checked="isAllSelected" @change="handleSelectAll" />
      </template>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'selection'">
        <a-checkbox :checked="selectedRowKeys.includes(record.multi_result_id)" @change="handleSelectChange(record)" />
      </template>
      <template v-if="column.key === 'time'">
        {{ new Date(getCurrentTestResult(record).time).toLocaleString() }}
      </template>
      <template v-else-if="column.key === 'result'">
        <a-tag v-if="record.result" :color="record.result === '通过' ? 'green' : 'red'">
          {{ record.result }}
          <span style="margin-left: 4px; cursor: pointer;" @click="toggleResult(record)">
            <SwapOutlined />
          </span>
        </a-tag>

      </template>

      <template v-else-if="column.key === 'can_result'">
        <a-tag v-if="record.can_result" :color="record.can_result === '通过' ? 'green' : 'red'">
          {{ record.can_result }}
        </a-tag>
      </template>

      <template v-else-if="column.key === 'action'">
        <a-button type="link" @click="handleAction(record)"> <template #icon>
            <FileSearchOutlined />
          </template>详情</a-button>
      </template>
      <template v-else-if="column.key === 'signlist'">
                <a-tag v-for="(sign, index) in record.signlist" :key="index" style="margin: 2px;"
                    :color="sign === '无' || sign.includes('人工复核') ? '' : 'pink'">
                    <el-tooltip v-if="sign.includes('人工复核')" :content="record.update_result_reason || '无修改原因'"
                        placement="top">
                        <span>{{ sign }}</span>
                    </el-tooltip>
                    <span v-else>{{ sign }}</span>
                    <span style="margin-left: 4px; cursor: pointer;" @click="removeSign(record, sign)"
                        v-if="sign !== '无' && !sign.includes('人工复核')">×</span>
                </a-tag>
            </template>
      <template v-else-if="column.key === 'relative_interval'">
        <a @click="seekVideo(record.relative_interval)" style="color: blue;">{{
          formatTime(record.relative_interval) }}</a>
      </template>
      <template v-else-if="column.key === 'ocr_accuracy_rate'">
        <span style="cursor: pointer;" @click="editOcrAccuracyRate(record)">
          {{ isNaN(record.ocr_accuracy_rate) ? record.ocr_accuracy_rate : parseFloat((record.ocr_accuracy_rate *
            100).toFixed(2)) + '%' }}
          <span style="margin-left: 4px; color: #1890ff;">
            <EditOutlined />
          </span>
        </span>
      </template>
      <template v-else-if="column.key === 'response_time'">
        <template v-if="isNaN(parseFloat(record.response_time)) || record.response_time === ''">
          <a-tag color="skyblue" v-if="record.response_time !== ''">{{ record.response_time }}</a-tag>
        </template>
        <template v-else>
          {{ record.response_time + 'ms' }}
        </template>
      </template>
    </template>
  </a-table>
  <a-pagination v-model:current="currentPage" :total="totalResults" :pageSize="pageSize" show-size-changer
    @showSizeChange="onShowSizeChange" @change="onPageChange" style="margin-top: 20px; text-align: center;"
    :pageSizeOptions="['10', '20', '50', '100', '500', '1000']" />

  <!-- 弹窗 -->
  <a-modal v-model:visible="visible" title="响应数据" @ok="handleOk" width="60%" height="60vh">
    <div style="max-height: 60vh; overflow: auto;">
      <el-descriptions class="margin-top" title="" :column="1" direction="vertical" border>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">
              <el-icon>
                <VideoCamera />
              </el-icon>
              车机监控视频
            </div>
          </template>
          <video :src="getStaticVideoUrl(currentSublist?.[0]?.video_path)" controls
            style="width: 100%; max-width: 400px;"></video>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">
              <el-icon>
                <VideoCamera />
              </el-icon>
              座舱监控视频
            </div>
          </template>
          <video :src="getStaticVideoUrl(currentSublist?.[0]?.cabin_video_path)" controls
            style="width: 100%; max-width: 400px;"></video>
        </el-descriptions-item>
      </el-descriptions>

      <a-card v-for="(record, index) in currentSublist" :key="index" style="margin-bottom: 20px;"
        :title="'第' + (index + 1) + '轮'">
        <div style="margin: 1rem; margin-left: 0;">
          <strong>语料文本: <span class="ml-[1rem]">{{ record.text }}</span></strong>
        </div>
        <div style="margin: 1rem; margin-left: 0;">
          <strong>判断结果: {{ ' ' }}
            <a-tag class="ml-[1rem]" :color="record.result === '通过' ? 'green' : 'pink'"
              @click="toggleSubResult(record)">
              {{ record.result }}
              <span style="margin-left: 4px; cursor: pointer;">
                <SwapOutlined />
              </span>
            </a-tag>
          </strong>
        </div>

        <el-descriptions class="margin-top" title="响应数据:" :column="3" direction="vertical" border>

          <!-- 结果图片 -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Picture />
                </el-icon>
                结果图片
              </div>
            </template>
            <el-image v-for="singleImage in record.image" :src="getStaticPictureUrl(singleImage)"
              style="margin-left: 0.5rem;width: 100px; height: 100px" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
              :preview-src-list="[getStaticPictureUrl(singleImage)]" :initial-index="4" fit="cover" />
          </el-descriptions-item>

          <!-- 得分和响应时间 -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Star />
                </el-icon>
                响应音频文本
              </div>
            </template>
            {{ record.asr_result }}
          </el-descriptions-item>

          <!-- 录音 -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Microphone />
                </el-icon>
                录音
              </div>
            </template>
            <audio :src="getStaticAudioUrl(record.mic_audio_url)" controls></audio>
          </el-descriptions-item>

        </el-descriptions>

        <el-descriptions class="margin-top" :title="'结果判断:'" :column="4" direction="vertical" border
          style="margin-top: 1rem;">
          <!-- 预期结果和模型判断依据 -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Document />
                </el-icon>
                预期结果
              </div>
            </template>
            {{ record.expect_result }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Document />
                </el-icon>
                模型判断依据
              </div>
            </template>
            {{ record.reason }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Document />
                </el-icon>
                模型图片判断置信度
              </div>
            </template>
            {{ record.llm_ui_confidence }}
          </el-descriptions-item>
          <!-- 模型语音判断置信度 -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                <el-icon>
                  <Document />
                </el-icon>
                模型语音判断置信度
              </div>
            </template>
            {{ record.llm_voice_confidence }}
          </el-descriptions-item>
        </el-descriptions>
        <a-table
          :dataSource="(record.can_data || []).map(x => { return { target_signal: x, result_id: record.multi_result_id } })"
          :columns="canDataColumns" :pagination="false" size="small" bordered>
          <template #bodyCell="{ column, record: canDataRecord }">
            <template v-if="column.key === 'target_signal'">
              {{ canDataRecord.target_signal }}
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" @click="handleCanDataAction(canDataRecord)">
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
        <a-modal v-model:visible="isChartModalVisible" title="CAN 数据折线图" width="800px" :footer="null" destroyOnClose>
          <EChartsComponent :data="chartData" :signalName="currentSignalName" />
        </a-modal>
      </a-card>
    </div>
  </a-modal>
  <a-modal v-model:visible="isRetestModalVisible" destroyOnClose title="生成复测方案" @ok="handleRetestModalOk"
    @cancel="handleRetestModalCancel">
    <a-form :model="retestForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="复测方案名称" required>
        <a-input v-model:value="retestForm.retest_plan_name" placeholder="请输入复测方案名称" />
      </a-form-item>
    </a-form>
  </a-modal>
  <RetestModal :corpusModalVisible="corpusModalVisible" :handleCorpusModalCancel="handleCorpusModalCancel"
    :handleCorpusModalOk="handleCorpusModalOk" :type="type" :project_id="projectDetail.project_id" :planId="planId">
  </RetestModal>
  <a-modal v-model:visible="isAddToPlanModalVisible" destroyOnClose title="添加到方案" @ok="handleAddToPlanModalOk"
    @cancel="handleAddToPlanModalCancel">
    <a-form :model="addToPlanForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="选择方案" required>
        <a-select v-model:value="addToPlanForm.selectedPlanId" placeholder="请选择方案">
          <a-select-option v-for="plan in planList" :key="plan.plan_id" :value="plan.plan_id">
            {{ plan.plan_name }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>


</template>

<script setup lang="tsx">
import { ref, computed, toRef, watch } from 'vue';
import { FileSearchOutlined } from '@ant-design/icons-vue';
import CommonTooltip from '../common/CommonToolTip.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { http } from '@renderer/http';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';
import { SwapOutlined, EditOutlined } from '@ant-design/icons-vue'; // 导入图标
import RetestModal from './RetestModal.vue';
import EChartsComponent from './EChartsComponent.vue'; // 引入 ECharts 组件

import { Document, Picture, Star, Timer, Microphone, VideoCamera } from '@element-plus/icons-vue';
const isChartModalVisible = ref(false); // 控制弹窗显示
const chartData = ref([]); // 存储 CAN 数据
const currentSignalName = ref(''); // 当前信号名称

const canDataColumns = ref([
  {
    title: '目标信号',
    dataIndex: 'target_signal',
    key: 'target_signal',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
  },
]);

const currentPage = ref(1);
const pageSize = ref(10);
const visible = ref(false);
const currentSublist = ref([]);
const currentMultiId = ref('');
const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);

const emit = defineEmits<{
  (e: 'generate_retest'): void;
  (e: 'refresh_table'): void;
}>();

const onShowSizeChange = (current: number, newPageSize: number) => {
  pageSize.value = newPageSize;
};

const handleSelectChange = (record) => {
  const uniqueKey = record.multi_result_id;
  if (!selectedRowKeys.value.includes(uniqueKey)) {
    selectedRowKeys.value.push(uniqueKey);
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(x => x !== uniqueKey);
  }
};

const getArray = (image) => {
  if (Array.isArray(image)) {
    return image;
  }
  return [image]
}

const props = defineProps({
  testResults: {
    type: Array,
    required: true
  },
  previewImage: {
    type: Function,
    required: true
  },
  seekVideo: {
    type: Function,
  },
  type: {
    type: String,
  },
  planId: {
    type: String,
  },
  turnId: {
    type: Number,
  },
  isCompleted: {
    type: Boolean,
    required: true
  }
});

const selectedRowKeys = ref<string[]>([]); // 选中的行 keys

const isRetestModalVisible = ref(false); // 控制弹窗显示
const retestForm = ref({
  retest_plan_name: '', // 复测方案名称
});

// 显示弹窗
const showRetestModal = () => {
  isRetestModalVisible.value = true;
};

const handleRetestModalOk = async () => {
  if (!retestForm.value.retest_plan_name) {
    ElMessage.error('请输入复测方案名称');
    return;
  }
  await generateRetest(retestForm.value.retest_plan_name);
  isRetestModalVisible.value = false;
  selectedRowKeys.value = [];
};

// 处理弹窗取消
const handleRetestModalCancel = () => {
  isRetestModalVisible.value = false;
};


const generateRetest = async (retest_plan_name) => {
  try {
    // 去掉 _index，只保留 multi_result_id
    const corpusIds = selectedRowKeys.value;
    console.log(corpusIds);

    // 创建包含id和时间戳的数组
    const corpusWithTime = corpusIds.map(id => {
      // 在testResults中查找对应的测试结果
      const testResult = props.testResults.find(
        tr => tr.multi_result_id === id
      );
      return {
        id,
        timestamp: new Date(testResult.time).getTime()
      };
    });

    // 按时间戳升序排序
    const sortedCorpus = corpusWithTime
      .sort((a, b) => a.timestamp - b.timestamp)
      .map(item => item.id);

    const response = await http.post('/test_project/create_retest_plan', {
      corpus_columns: sortedCorpus.reverse(),  // 使用排序后的id数组
      project_id: projectDetail.value.project_id,
      plan_id: props.planId,
      turn_id: props.turnId,
      retest_plan_name,
    });

    if (response) {
      ElMessage.success('生成复测方案成功');
      emit('generate_retest');
    }
  } catch (e) {
    console.error('生成复测方案失败:', e);
    ElMessage.error('生成复测方案失败');
  }
};

// 监听 testResults 的变化
watch(
  () => props.turnId,
  () => {
    currentPage.value = 1; // 重置 currentPage 为 1
  },
);

const filterResult = ref<string | null>(null);
const filterSignlist = ref<string>('');

watch([filterResult, filterSignlist], () => {
  currentPage.value = 1; // 重置 currentPage 为 1
  selectedRowKeys.value = [];
});

const testResults = toRef(props, 'testResults');

const sub_result_list = computed(() => {
  let filteredResults = testResults.value;

  if (filterResult.value) {
    filteredResults = filteredResults.filter(result => result.result === filterResult.value);
  }

  if (filterSignlist.value) {
    filteredResults = filteredResults.filter(result =>
      result.signlist && result.signlist.join(',').toLowerCase().includes(filterSignlist.value.toLowerCase())
    );
  }
  return filteredResults.flatMap((testResult, index) => {
    const subResults = (testResult?.sub_result || []).map((subResult, subIndex) => {
      return {
        ...subResult,
        _index: index, // 添加 _index
        index: index + 1, // 分配 index，从 1 开始
        multi_result_id: testResult.multi_result_id,
        super_result: testResult.result,
        sub_result: subResult.result,
      };
    });
    return subResults;
  });
});

const handleCanDataAction = async (canDataRecord) => {
  try {
    // 调用接口获取 CAN 数据
    const response = await http.post('/test_project/get_can_data', {
      result_id: canDataRecord.result_id,
      signal_name: canDataRecord.target_signal,
    });

    if (response.status === 0 && response.data) {
      chartData.value = response.data.list; // 存储 CAN 数据
      currentSignalName.value = canDataRecord.signal_name; // 存储信号名称
      isChartModalVisible.value = true; // 显示弹窗
    } else {
      ElMessage.error('获取 CAN 数据失败');
    }
  } catch (error) {
    ElMessage.error('获取 CAN 数据失败');
    chartData.value = [{ time: '2023-10-02', signal_value: 100 },
    { time: '2023-10-02', signal_value: 200 },
    { time: '2023-10-03', signal_value: 150 },
    { time: '2023-10-04', signal_value: 300 },
    { time: '2023-10-05', signal_value: 250 },
    { time: '2023-10-06', signal_value: 400 },
    { time: '2023-10-07', signal_value: 350 },]; // 存储 CAN 数据
    currentSignalName.value = canDataRecord.signal_name; // 存储信号名称
    isChartModalVisible.value = true; // 显示弹窗
  }
};

const totalResults = computed(() => sub_result_list.value.length);

const pagedSubResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  const currentData = sub_result_list.value.slice(start, end);
  const Count = new Map();
  const hasSeen = new Map();
  for (const row of currentData) {
    if (Count.has(row.index)) {
      Count.set(row.index, Count.get(row.index) + 1);
    } else {
      Count.set(row.index, 1);
    }
  }

  for (const row of currentData) {
    if (hasSeen.has(row.index)) {
      row.rowSpan = 0;
      continue;
    }
    hasSeen.set(row.index, true);
    row.rowSpan = Count.get(row.index);
    row.result = row.super_result;
  }

  for (const result of currentData) {
    if (result.image && !Array.isArray(result.image)) {
      result.image = [result.image]
    }
  }

  return currentData;
});

const onPageChange = (page: number) => {
  currentPage.value = page;
};

const handleAction = (record) => {
  const sublist = testResults.value[record.index - 1].sub_result;
  currentSublist.value = sublist.slice().reverse();
  currentMultiId.value = record.multi_result_id
  visible.value = true;
};

const getCurrentTestResult = (record) => {
  const testResult = testResults.value[record.index - 1];
  return testResult;
}

const handleOk = () => {
  visible.value = false;
};

const beforeColumns = ref(!props.seekVideo ? [

  { title: '', dataIndex: 'selection', key: 'selection', align: 'center', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
  { title: '结果ID', dataIndex: 'result_id', key: 'result_id', align: 'center' },
  { title: '时间', dataIndex: 'time', key: 'time', align: 'center', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
  { title: '语料文本', dataIndex: 'text', key: 'text', align: 'center' },
  { title: '响应时间', dataIndex: 'response_time', key: 'response_time', align: 'center' },
  {
    title: (
      <div>
        结果标记
        <CommonTooltip
          text={
            <>
              <div>【结果标记说明】</div>
              <div>1. 未检测到语音响应文本</div>
              <div>① 唤醒应答异常</div>
              <div>② 唤醒时间异常</div>
              <div>2. 未检测出响应时间：唤醒时间 &gt; 2s 或 ≤ 100ms 或 ≤ 0</div>
              <div>3. 需复核结果：图片或音频判断置信度偏低</div>
              <div>4. 重点复核结果：模型判断置信度低</div>
              <div>5. 响应时间异常：响应时间 ≤ 0 或 &gt; 车机最长响应时长</div>
              <div>6. 语音响应异常：未检测到语音响应文本</div>
            </>
          }
        />
      </div>
    ),
    dataIndex: 'signlist',
    key: 'signlist',
    align: 'center',
    width: '20%'
  },
  { title: '测试结果', dataIndex: 'result', key: 'result', align: 'center', width: '15%', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
  { title: 'CAN结果', dataIndex: 'can_result', key: 'can_result', align: 'center', width: '80px' },
  { title: '操作', dataIndex: 'action', key: 'action', align: 'center', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
] : [
  { title: '结果ID', dataIndex: 'result_id', key: 'result_id', align: 'center' },

  { title: '时间', dataIndex: 'time', key: 'time', align: 'center', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
  { title: '语料文本', dataIndex: 'text', key: 'text', align: 'center' },
  { title: '响应时间', dataIndex: 'response_time', key: 'response_time', align: 'center' },
  {
    title: (
      <div>
        结果标记
        <CommonTooltip
          text={
            <>
              <div>【结果标记说明】</div>
              <div>1. 未检测到语音响应文本</div>
              <div>① 唤醒应答异常</div>
              <div>② 唤醒时间异常</div>
              <div>2. 未检测出响应时间：唤醒时间 &gt; 2s 或 ≤ 100ms 或 ≤ 0</div>
              <div>3. 需复核结果：图片或音频判断置信度偏低</div>
              <div>4. 重点复核结果：模型判断置信度低</div>
              <div>5. 响应时间异常：响应时间 ≤ 0 或 &gt; 车机最长响应时长</div>
              <div>6. 语音响应异常：未检测到语音响应文本</div>
            </>
          }
        />
      </div>
    ),
    dataIndex: 'signlist',
    key: 'signlist',
    align: 'center',
    width: '20%'
  },
  { title: '测试结果', dataIndex: 'result', key: 'result', align: 'center', width: '15%', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
  { title: 'CAN结果', dataIndex: 'can_result', key: 'can_result', align: 'center', width: '80px' },
  { title: '跳转时间', dataIndex: 'relative_interval', key: 'relative_interval', align: 'center' },
  { title: '操作', dataIndex: 'action', key: 'action', align: 'center', customCell: (record, index) => ({ rowSpan: record.rowSpan }) },
]);

let columns = computed(() => {
  return beforeColumns.value.filter((column) => {
    if (column.key === 'response_time') {
      return false;
    }
    return true;
  })
})

const getStaticAudioUrl = (url) => {
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    return urlCustomed + 'mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
  } else {
    return 'http://127.0.0.1:8080/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
  }
}

const getStaticPictureUrl = (url) => {
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    return urlCustomed + 'photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
  } else {
    return 'http://127.0.0.1:8080/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
  }
}

const getStaticVideoUrl = (url) => {
  if (!url) return '';
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    return urlCustomed + 'video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
  } else {
    return 'http://127.0.0.1:8080/video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
  }
};

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

const doRetest = async () => {
  try {
    const response = await http.post('/test_project/execute_retest_plan', {
      project_id: projectDetail.value.project_id,
      plan_id: props.planId,
      turn_id: props.turnId,
    });
    if (response.status === 0) {
      ElMessage.success('开始复测');
      emit('generate_retest');
    } else {
      ElMessage.error('开始复测失败');
    }
  } catch (error) {
    ElMessage.error('开始复测失败');
  }
}

const toggleResult = async (record) => {
  console.log(record);
  const sublist = testResults.value[record.index - 1].sub_result;
  try {
    // 首先弹窗询问是否切换结果
    const confirm = await ElMessageBox.confirm(
      `确定要将结果从"${record.result}"切换为"${record.result === '通过' ? '不通过' : '通过'}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        title: '人工复核'
      }
    );

    if (confirm === 'confirm') {
      // 弹窗输入修改原因
      const { value: reason } = await ElMessageBox.prompt(
        '请输入修改原因',
        '修改原因',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            // 修改原因可以为空
            return true;
          }
        }
      );
      let response
      // 大语料，需要传入所有小语料ID
      if (record) {
        response = await http.post('/test_project/update_multi_result', {
          update_result_list: sublist.map(x => x.result_id), // 假设这里存储了所有小语料的ID
          multi_result_list: sublist.map(x => x.result_id), // 假设这里存储了所有结果的ID
          multi_id: record.multi_result_id,
          new_result: record.result === '通过' ? '不通过' : '通过',
          update_result_reason: reason
        });
      }

      if (response) {
        ElMessage.success('结果切换成功');
        record.result = record.result === '通过' ? '不通过' : '通过';
        emit('refresh_table');
      } else {
        ElMessage.error('结果切换失败');
      }
    }
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error('结果切换失败');
    }
  }
};

const editOcrAccuracyRate = async (record) => {
  try {
    // 获取当前的 OCR 准确率
    const currentRate = record.ocr_accuracy_rate;
    const currentRatePercent = isNaN(currentRate) ? 0 : parseFloat((currentRate * 100).toFixed(2));

    // 弹窗让用户输入新的 OCR 准确率
    const { value: newRateStr } = await ElMessageBox.prompt(
      '请输入新的 OCR 准确率（0-100 之间的数字）',
      '修改 OCR 准确率',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'text',
        inputValue: String(currentRatePercent),
        inputValidator: (value) => {
          const num = parseFloat(value);
          if (isNaN(num)) {
            return '请输入有效的数字';
          }
          if (num < 0 || num > 100) {
            return '请输入 0-100 之间的数字';
          }
          return true;
        }
      }
    );

    if (newRateStr) {
      // 将百分比转换为 0-1 之间的小数
      const newRate = parseFloat(newRateStr) / 100;

      // 调用接口更新 OCR 准确率
      const response = await http.post('/test_project/update_result_ocr_accuracy_rate', {
        result_id: record.result_id,
        ocr_accuracy_rate: newRate
      });

      if (response.status === 0) {
        ElMessage.success('OCR 准确率修改成功');
        record.ocr_accuracy_rate = newRate;
        emit('refresh_table');
      } else {
        ElMessage.error('OCR 准确率修改失败');
      }
    }
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error('OCR 准确率修改失败');
      console.error(e);
    }
  }
};

const removeSign = async (record, sign) => {
  try {
    // 二次确认
    const confirm = await ElMessageBox.confirm('确定要移除该标记吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    if (confirm === 'confirm') {
      // 调用接口移除标记
      const response = await http.post('/test_project/remove_sign', {
        project_id: projectDetail.value.project_id,
        plan_id: props.planId,
        turn_id: props.turnId,
        result_id: record.result_id,
        sign: sign,
      });

      if (response) {
        ElMessage.success('标记移除成功');
        // 更新 signlist
        record.signlist = record.signlist.filter(s => s !== sign);
      } else {
        ElMessage.error('标记移除失败');
      }
    }
  } catch (error) {
    ElMessage.error('标记移除失败');
  }
};

const corpusModalVisible = ref(false);
const handleCorpusModalCancel = () => {
  corpusModalVisible.value = false;
}
const handleCorpusModalOk = (corpus: string) => {
  corpusModalVisible.value = false;
}


const viewRetestPlan = async () => {
  corpusModalVisible.value = true;
};

const isAllSelected = computed(() => {
  const currentPageKeys = pagedSubResults.value.map(record => record.multi_result_id);
  return currentPageKeys?.length && currentPageKeys.every(key => selectedRowKeys.value.includes(key));
});

const handleSelectAll = (e) => {
  const currentPageKeys = pagedSubResults.value.map(record => record.multi_result_id);
  if (e.target.checked) {
    // 全选
    selectedRowKeys.value = [...new Set([...selectedRowKeys.value, ...currentPageKeys])];
  } else {
    // 取消全选
    selectedRowKeys.value = selectedRowKeys.value.filter(key => !currentPageKeys.includes(key));
  }
};

const planList = ref([]);
const isAddToPlanModalVisible = ref(false);
const addToPlanForm = ref({
  selectedPlanId: '',
});

const showAddToPlanModal = async () => {
  try {
    const response = await http.post('/test_project/get_plan_list', {
      project_id: projectDetail.value.project_id,
      turn_id: props.turnId,
    });
    if (response.status === 0) {
      planList.value = response.data.list.filter(x => x.plan_id.startsWith('retest'));
      isAddToPlanModalVisible.value = true;
    } else {
      ElMessage.error('获取方案列表失败');
    }
  } catch (error) {
    ElMessage.error('获取方案列表失败');
  }
};

const handleAddToPlanModalOk = async () => {
  if (!addToPlanForm.value.selectedPlanId) {
    ElMessage.error('请选择方案');
    return;
  }

  const corpusIds = selectedRowKeys.value;

  // 创建包含id和时间戳的数组
  const corpusWithTime = corpusIds.map(id => {
    // 在testResults中查找对应的测试结果
    const testResult = props.testResults.find(
      tr => tr.multi_result_id === id
    );
    return {
      id,
      timestamp: new Date(testResult.time).getTime()
    };
  });

  // 按时间戳升序排序
  const sortedCorpus = corpusWithTime
    .sort((a, b) => a.timestamp - b.timestamp)
    .map(item => item.id);

  try {
    const response = await http.post('/test_project/add_to_plan', {
      project_id: projectDetail.value.project_id,
      plan_id: addToPlanForm.value.selectedPlanId,
      corpus_ids: sortedCorpus,
    });
    if (response.status === 0) {
      ElMessage.success('添加到方案成功');
      isAddToPlanModalVisible.value = false;
      selectedRowKeys.value = [];
    } else {
      ElMessage.error('添加到方案失败');
    }
  } catch (error) {
    ElMessage.error('添加到方案失败');
  }
};

const handleAddToPlanModalCancel = () => {
  isAddToPlanModalVisible.value = false;
  addToPlanForm.value.selectedPlanId = '';
};

// 详情弹窗中修改单个小语料结果
// 详情弹窗中修改单个小语料结果
const toggleSubResult = async (record) => {
  console.log(record);
  console.log(record.multi_result_id);
  try {
    const confirm = await ElMessageBox.confirm(
      `确定要将结果从"${record.result}"切换为"${record.result === '通过' ? '不通过' : '通过'}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        title: '人工复核'
      }
    );

    if (confirm === 'confirm') {
      // 弹窗输入修改原因
      const { value: reason } = await ElMessageBox.prompt(
        '请输入修改原因',
        '修改原因',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            // 修改原因可以为空
            return true;
          }
        }
      );

      // 只更新当前小语料的结果
      const response = await http.post('/test_project/update_multi_result', {
        update_result_list: [record.result_id], // 只传当前小语料的result_id
        multi_result_list: currentSublist.value.map(x => x.result_id), // 父级多轮语料的所有result_id
        multi_id: currentMultiId.value, // 多轮语料ID
        new_result: record.result === '通过' ? '不通过' : '通过',
        update_result_reason: reason
      });

      if (response) {
        ElMessage.success('结果切换成功');
        // 更新当前记录的结果
        record.result = record.result === '通过' ? '不通过' : '通过';
        // 如果需要刷新整个表格数据，可以调用emit
        emit('refresh_table');
      } else {
        ElMessage.error('结果切换失败');
      }
    }
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error('结果切换失败');
    }
  }
};
</script>
