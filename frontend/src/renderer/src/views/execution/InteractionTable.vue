<template>
    <div style="margin-bottom: 20px;">
        <!-- 新增按钮 -->
        <a-button type="primary" @click="showRetestModal" :disabled="selectedRowKeys.length === 0"
            :style="{ marginRight: '1rem' }">
            生成复测方案
        </a-button>
        <a-button type="primary" @click="viewRetestPlan" :style="{ marginRight: '1rem' }"
            v-show="planId?.startsWith('retest')">
            复测方案配置
        </a-button>
        <a-button type="primary" @click="showAddToPlanModal" :disabled="selectedRowKeys.length === 0"
            :style="{ marginRight: '1rem' }">
            添加到方案
        </a-button>
        <a-button type="primary" @click="doRetest" v-show="planId?.startsWith('retest')"
            :style="{ marginRight: '1rem' }" :disabled="isCompleted">
            开始复测
        </a-button>

        <!-- 过滤器 -->
        <a-select v-model:value="result" @change="(e) => setResult(e.target.value)" placeholder="请选择测试结果"
            style="width: 150px; margin-right: 10px;" allowClear>
            <a-select-option value="通过">通过</a-select-option>
            <a-select-option value="不通过">不通过</a-select-option>
        </a-select>

        <a-modal v-model:visible="isAddToPlanModalVisible" destroyOnClose title="添加到方案" @ok="handleAddToPlanModalOk"
            @cancel="handleAddToPlanModalCancel">
            <a-form :model="addToPlanForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
                <a-form-item label="选择方案" required>
                    <a-select v-model:value="addToPlanForm.selectedPlanId" placeholder="请选择方案">
                        <a-select-option v-for="plan in planList" :key="plan.plan_id" :value="plan.plan_id">
                            {{ plan.plan_name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>

        <a-input v-model:value="sign" @change="(e) => setSign(e.target.value)" placeholder="搜索结果标记"
            style="width: 200px;" allowClear />
    </div>

    <!-- 表格 -->
    <a-table :columns="columns" :dataSource="testResults" :rowKey="record => record.result_id" bordered size="small"
        class="test-results-table" :pagination="false" :expand-column-width="50"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'time'">
                {{ new Date(record.time).toLocaleString() }}
            </template>
            <template v-else-if="column.key === 'relative_interval'">
                <a @click="seekVideo(record.relative_interval)" style="color: blue;">{{
                    formatTime(record.relative_interval) }}</a>
            </template>
            <template v-else-if="column.key === 'result'">
                <a-tag v-if="record.result" :color="record.result === '通过' ? 'green' : 'red'">
                    {{ record.result }}
                    <span style="margin-left: 4px; cursor: pointer;" @click="toggleResult(record)">
                        <SwapOutlined />
                    </span>
                </a-tag>

            </template>
            <template v-else-if="column.key === 'can_result'">
                <a-tag v-if="record.can_result" :color="record.can_result === '通过' ? 'green' : 'red'">
                    {{ record.can_result }}
                </a-tag>
            </template>
            <template v-else-if="column.key === 'ocr_pic_url'">
                <el-image style="width: 40px; height: 40px" :src="getStaticPictureUrl(record.ocr_pic_url, true)"
                    :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                    :preview-src-list="[getStaticPictureUrl(record.ocr_pic_url, true)]" :initial-index="4"
                    fit="cover" />
            </template>
            <template v-else-if="column.key === 'ocr_accuracy_rate'">
                <span style="cursor: pointer;" @click="editOcrAccuracyRate(record)">
                    {{ isNaN(record.ocr_accuracy_rate) ? record.ocr_accuracy_rate : parseFloat((record.ocr_accuracy_rate *
                        100).toFixed(2)) + '%' }}
                    <span style="margin-left: 4px; color: #1890ff;">
                        <EditOutlined />
                    </span>
                </span>
            </template>
            <template v-else-if="column.key === 'response_time'">
                <template v-if="isNaN(parseFloat(record.response_time)) || record.response_time === ''">
                    <a-tag color="blue" v-if="record.response_time !== ''">{{ record.response_time }}</a-tag>
                </template>
                <template v-else>
                    {{ record.response_time + 'ms' }}
                </template>
            </template>
            <template v-else-if="column.key === 'signlist'">
                <a-tag v-for="(sign, index) in record.signlist" :key="index" style="margin: 2px;"
                    :color="sign === '无' || sign.includes('人工复核') ? '' : 'pink'">
                    <el-tooltip v-if="sign.includes('人工复核')" :content="record.update_result_reason || '无修改原因'"
                        placement="top">
                        <span>{{ sign }}</span>
                    </el-tooltip>
                    <span v-else>{{ sign }}</span>
                    <span style="margin-left: 4px; cursor: pointer;" @click="removeSign(record, sign)"
                        v-if="sign !== '无' && !sign.includes('人工复核')">×</span>
                </a-tag>
            </template>
        </template>
        <template #expandedRowRender="{ record }">
            <div class="expanded-content">
                <el-descriptions class="margin-top" title="响应数据:" :column="3" direction="vertical" border>
                    <!-- 结果图片 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                                结果图片
                            </div>
                        </template>
                        <el-image v-for="singleImage in record.image" :src="getStaticPictureUrl(singleImage)"
                            style="margin-left: 0.5rem; width: 100px; height: 100px" :zoom-rate="1.2" :max-scale="7"
                            :min-scale="0.2" :preview-src-list="[getStaticPictureUrl(singleImage)]" :initial-index="4"
                            fit="cover" />
                    </el-descriptions-item>

                    <!-- 响应音频文本 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Star />
                                </el-icon>
                                响应音频文本
                            </div>
                        </template>
                        {{ record.asr_result }}
                    </el-descriptions-item>

                    <!-- 录音 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Microphone />
                                </el-icon>
                                录音
                            </div>
                        </template>
                        <audio :src="getStaticUrl(record.mic_audio_url)" controls></audio>
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <VideoCamera />
                                </el-icon>
                                车机监控视频
                            </div>
                        </template>
                        <video :src="getStaticVideoUrl(record.video_path)" controls
                            style="width: 100%; max-width: 400px;"></video>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <VideoCamera />
                                </el-icon>
                                座舱监控视频
                            </div>
                        </template>
                        <video :src="getStaticVideoUrl(record.cabin_video_path)" controls
                            style="width: 100%; max-width: 400px;"></video>
                    </el-descriptions-item>
                </el-descriptions>

                <el-descriptions class="margin-top" title="结果判断:" :column="4" direction="vertical" border
                    style="margin-top: 1rem;" v-if="type !== 'rouse'">
                    <!-- 预期结果和模型判断依据 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Document />
                                </el-icon>
                                预期结果
                            </div>
                        </template>
                        {{ record.expect_result }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Document />
                                </el-icon>
                                模型判断依据
                            </div>
                        </template>
                        {{ record.reason }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Percentage />
                                </el-icon>
                                模型图片判断置信度
                            </div>
                        </template>
                        {{ record.llm_ui_confidence }}
                    </el-descriptions-item>
                    <!-- 模型语音判断置信度 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Percentage />
                                </el-icon>
                                模型语音判断置信度
                            </div>
                        </template>
                        {{ record.llm_voice_confidence }}
                    </el-descriptions-item>
                </el-descriptions>

                <el-descriptions class="margin-top" title="字识别结果:" :column="4" direction="vertical" border
                    style="margin-top: 1rem;" v-if="type !== 'rouse'">
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Document />
                                </el-icon>
                                OCR 结果
                            </div>
                        </template>
                        {{ record.ocr_result || '- -' }}
                    </el-descriptions-item>
                    <!-- OCR图片判断置信度 -->
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Percentage />
                                </el-icon>
                                OCR图片判断置信度
                            </div>
                        </template>
                        {{ record.image_confidence || '- -' }}
                    </el-descriptions-item>

                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                                OCR 识别图片
                            </div>
                        </template>
                        <el-image :src="getStaticPictureUrl(record.ocr_pic_url)" style="width: 100px; height: 100px"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                            :preview-src-list="[getStaticPictureUrl(record.ocr_pic_url)]" :initial-index="4"
                            fit="cover" />
                    </el-descriptions-item>
                </el-descriptions>
                <div style="margin: 1rem 0; font-size: 1rem; font-weight: bold">
                    执行结果判断:
                </div>

                <a-table
                    :dataSource="(record.can_data || []).map(x => { return { target_signal: x, result_id: record.result_id } })"
                    :columns="canDataColumns" :pagination="false" size="small" bordered>
                    <template #bodyCell="{ column, record: canDataRecord }">
                        <template v-if="column.key === 'target_signal'">
                            {{ canDataRecord.target_signal }}
                        </template>
                        <template v-else-if="column.key === 'action'">
                            <a-button type="link" @click="handleCanDataAction(canDataRecord)">
                                查看详情
                            </a-button>
                        </template>
                    </template>
                </a-table>

                <!-- ECharts 弹窗 -->
                <a-modal v-model:visible="isChartModalVisible" title="CAN 数据折线图" width="800px" :footer="null"
                    destroyOnClose>
                    <EChartsComponent :data="chartData" :signalName="currentSignalName" />
                </a-modal>
            </div>
        </template>
    </a-table>
    <!-- 分页 -->
    <a-pagination :current="page" :total="total" v-model:pageSize="page_size" @showSizeChange="onShowSizeChange"
        @change="onPageChange" style="margin-top: 20px; text-align: center;" />

    <a-modal v-model:visible="isRetestModalVisible" destroyOnClose title="生成复测方案" @ok="handleRetestModalOk"
        @cancel="handleRetestModalCancel">
        <a-form :model="retestForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="复测方案名称" required>
                <a-input v-model:value="retestForm.retest_plan_name" placeholder="请输入复测方案名称" />
            </a-form-item>
        </a-form>
    </a-modal>

    <RetestModal :corpusModalVisible="corpusModalVisible" :handleCorpusModalCancel="handleCorpusModalCancel"
        :handleCorpusModalOk="handleCorpusModalOk" :type="type" :project_id="projectDetail.project_id" :planId="planId">
    </RetestModal>

</template>

<script setup lang="tsx">
import { ref, computed, toRef, watch, reactive } from 'vue';
import CommonTooltip from '../common/CommonToolTip.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { http } from '@renderer/http'; // 导入 http 模块
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';
import { SwapOutlined, EditOutlined } from '@ant-design/icons-vue'; // 导入图标
import RetestModal from './RetestModal.vue';
import { usePageStore } from '../../pageStore';
import EChartsComponent from './EChartsComponent.vue'; // 引入 ECharts 组件
import { Document, Picture, Star, Timer, Microphone, VideoCamera } from '@element-plus/icons-vue';

const isChartModalVisible = ref(false); // 控制弹窗显示
const chartData = ref([]); // 存储 CAN 数据
const currentSignalName = ref(''); // 当前信号名称
const emit = defineEmits<{
    (e: 'generate_retest'): void;
    (e: 'refresh_table'): void;
}>();

const selectedRowKeys = ref<string[]>([]); // 选中的行 keys
const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);
const pageStore = usePageStore();
const { page, page_size, result, sign, total } = storeToRefs(pageStore);
const { setPage, setPageSize, setResult, setSign, } = pageStore;

const canDataColumns = ref([
    {
        title: '目标信号',
        dataIndex: 'target_signal',
        key: 'target_signal',
        align: 'center',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
    },
]);

watch(total, () => {
    console.log(total);
})

const handleCanDataAction = async (canDataRecord) => {
    try {
        // 调用接口获取 CAN 数据
        const response = await http.post('/test_project/get_can_data', {
            result_id: canDataRecord.result_id,
            signal_name: canDataRecord.target_signal,
        });

        if (response.status === 0 && response.data) {
            chartData.value = response.data.list; // 存储 CAN 数据
            currentSignalName.value = canDataRecord.signal_name; // 存储信号名称
            isChartModalVisible.value = true; // 显示弹窗
        } else {
            ElMessage.error('获取 CAN 数据失败');
        }
    } catch (error) {
        ElMessage.error('获取 CAN 数据失败');
        chartData.value = [{ time: '2023-10-02', signal_value: 100 },
        { time: '2023-10-02', signal_value: 200 },
        { time: '2023-10-03', signal_value: 150 },
        { time: '2023-10-04', signal_value: 300 },
        { time: '2023-10-05', signal_value: 250 },
        { time: '2023-10-06', signal_value: 400 },
        { time: '2023-10-07', signal_value: 350 },]; // 存储 CAN 数据
        currentSignalName.value = canDataRecord.signal_name; // 存储信号名称
        isChartModalVisible.value = true; // 显示弹窗
    }
};

const props = defineProps({
    testResults: {
        type: Array,
        required: true
    },
    previewImage: {
        type: Function,
        required: true
    },
    seekVideo: {
        type: Function,
    },
    planId: {
        type: String,
    },
    type: {
        type: String,
    },
    turnId: {
        type: Number,
    },
    isCompleted: {
        type: Boolean,
        required: true
    }
});

watch([result, sign], () => {
    page.value = 1; // 重置 page 为 1
    selectedRowKeys.value = [];
});

const testResults = toRef(props, 'testResults');


const onPageChange = (page: number) => {
    setPage(page)
};

let beforeColumns = ref(!props.seekVideo ? [
    { title: '结果ID', dataIndex: 'result_id', key: 'result_id', align: 'center' },
    { title: '时间', dataIndex: 'time', key: 'time', align: 'center' },
    { title: '语料文本', dataIndex: 'text', key: 'text', align: 'center' },
    { title: '响应时间', dataIndex: 'response_time', key: 'response_time', align: 'center' },
    { title: '字识别率', dataIndex: 'ocr_accuracy_rate', key: 'ocr_accuracy_rate', align: 'center' },
    { title: '测试结果', dataIndex: 'result', key: 'result', align: 'center', width: '80px' },
    { title: 'CAN结果', dataIndex: 'can_result', key: 'can_result', align: 'center', width: '80px' },
    {
        title: (
            <div>
                结果标记
                <CommonTooltip
                    text={
                        <>
                            <div>【结果标记说明】</div>
                            <div>1. 未检测到语音响应文本</div>
                            <div>① 唤醒应答异常</div>
                            <div>② 唤醒时间异常</div>
                            <div>2. 未检测出响应时间：唤醒时间 &gt; 2s 或 ≤ 100ms 或 ≤ 0</div>
                            <div>3. 需复核结果：图片或音频判断置信度偏低</div>
                            <div>4. 重点复核结果：模型判断置信度低</div>
                            <div>5. 响应时间异常：响应时间 ≤ 0 或 &gt; 车机最长响应时长</div>
                            <div>6. 语音响应异常：未检测到语音响应文本</div>
                        </>
                    }
                />
            </div>
        ),
        dataIndex: 'signlist',
        key: 'signlist',
        align: 'center',
        width: '20%'
    }
] : [
    { title: '结果ID', dataIndex: 'result_id', key: 'result_id', align: 'center' },
    { title: '时间', dataIndex: 'time', key: 'time', align: 'center' },
    { title: '语料文本', dataIndex: 'text', key: 'text', align: 'center' },
    { title: '响应时间', dataIndex: 'response_time', key: 'response_time', align: 'center' },
    { title: '字识别率', dataIndex: 'ocr_accuracy_rate', key: 'ocr_accuracy_rate', align: 'center' },
    { title: '测试结果', dataIndex: 'result', key: 'result', align: 'center', width: '80px' },
    { title: 'CAN结果', dataIndex: 'can_result', key: 'can_result', align: 'center', width: '80px' },
    { title: '跳转时间', dataIndex: 'relative_interval', key: 'relative_interval', align: 'center' },
    {
        title: (
            <div>
                结果标记
                <CommonTooltip
                    text={
                        <>
                            <div>【结果标记说明】</div>
                            <div>1. 未检测到语音响应文本</div>
                            <div>① 唤醒应答异常</div>
                            <div>② 唤醒时间异常</div>
                            <div>2. 未检测出响应时间：唤醒时间 &gt; 2s 或 ≤ 100ms 或 ≤ 0</div>
                            <div>3. 需复核结果：图片或音频判断置信度偏低</div>
                            <div>4. 重点复核结果：模型判断置信度低</div>
                            <div>5. 响应时间异常：响应时间 ≤ 0 或 &gt; 车机最长响应时长</div>
                            <div>6. 语音响应异常：未检测到语音响应文本</div>
                        </>
                    }
                />
            </div>
        ),
        dataIndex: 'signlist',
        key: 'signlist',
        align: 'center',
        width: '20%'
    }
]);

let columns = computed(() => {
    return beforeColumns.value.filter((column) => {
        if (column.key === 'ocr_accuracy_rate' && props.type === 'rouse') {
            return false;
        }
        if (column.key === 'response_time' && props.type === 'interaction-multi') {
            return false;
        }
        return true;
    })
})

const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
};

const isRetestModalVisible = ref(false); // 控制弹窗显示
const retestForm = ref({
    retest_plan_name: '', // 复测方案名称
});

// 显示弹窗
const showRetestModal = () => {
    isRetestModalVisible.value = true;
};

const handleRetestModalOk = async () => {
    if (!retestForm.value.retest_plan_name) {
        ElMessage.error('请输入复测方案名称');
        return;
    }
    await generateRetest(retestForm.value.retest_plan_name);
    isRetestModalVisible.value = false;
    selectedRowKeys.value = [];
};

// 处理弹窗取消
const handleRetestModalCancel = () => {
    isRetestModalVisible.value = false;
};


const generateRetest = async (retest_plan_name) => {
    try {
        const response = await http.post('/test_project/create_retest_plan', {
            corpus_columns: selectedRowKeys.value,
            project_id: projectDetail.value.project_id,
            plan_id: props.planId,
            turn_id: props.turnId,
            retest_plan_name,
        });
        if (response) {
            ElMessage.success('生成复测方案成功');
        } else {
            ElMessage.error('生成复测方案失败');
        }
        emit('generate_retest');
    } catch (error) {
        ElMessage.error('生成复测方案失败');
    }
};

const doRetest = async () => {
    try {
        const response = await http.post('/test_project/execute_retest_plan', {
            project_id: projectDetail.value.project_id,
            plan_id: props.planId,
            turn_id: props.turnId,
        });
        if (response.status === 0) {
            emit('generate_retest');
            ElMessage.success('开始复测');
        } else {
            ElMessage.error('开始复测失败');
        }
    } catch (error) {
        ElMessage.error('开始复测失败');
    }
}

const viewRetestPlan = async () => {
    corpusModalVisible.value = true;
};

const getStaticUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    } else {
        return 'http://127.0.0.1:8080/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    }
}

const getStaticPictureUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    } else {
        return 'http://127.0.0.1:8080/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    }
}

const getStaticVideoUrl = (url) => {
    if (!url) return '';
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
    } else {
        return 'http://127.0.0.1:8080/video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
    }
};

const onShowSizeChange = (current: number, newpage_size: number) => {
    setPageSize(newpage_size);
    setPage(1)
};

const jumpToOCR = (url: string) => {
    if (url) {
        window.electron.ipcRenderer.send('show_path', url);
    } else {
        ElMessage.error('当前无ocr图片文件！');
    }
}

const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

const removeSign = async (record, sign) => {
    try {
        // 二次确认
        const confirm = await ElMessageBox.confirm('确定要移除该标记吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        });

        if (confirm === 'confirm') {
            // 调用接口移除标记
            const response = await http.post('/test_project/remove_sign', {
                project_id: projectDetail.value.project_id,
                plan_id: props.planId,
                turn_id: props.turnId,
                result_id: record.result_id,
                sign: sign,
            });

            if (response) {
                ElMessage.success('标记移除成功');
                // 更新 signlist
                record.signlist = record.signlist.filter(s => s !== sign);
            } else {
                ElMessage.error('标记移除失败');
            }
        }
        emit('refresh_table');
    } catch (error) {
        ElMessage.error('标记移除失败');
    }
};

const editOcrAccuracyRate = async (record) => {
    try {
        // 获取当前的 OCR 准确率
        const currentRate = record.ocr_accuracy_rate;
        const currentRatePercent = isNaN(currentRate) ? 0 : parseFloat((currentRate * 100).toFixed(2));

        // 弹窗让用户输入新的 OCR 准确率
        const { value: newRateStr } = await ElMessageBox.prompt(
            '请输入新的 OCR 准确率（0-100 之间的数字）',
            '修改 OCR 准确率',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputType: 'text',
                inputValue: String(currentRatePercent),
                inputValidator: (value) => {
                    const num = parseFloat(value);
                    if (isNaN(num)) {
                        return '请输入有效的数字';
                    }
                    if (num < 0 || num > 100) {
                        return '请输入 0-100 之间的数字';
                    }
                    return true;
                }
            }
        );

        if (newRateStr) {
            // 将百分比转换为 0-1 之间的小数
            const newRate = parseFloat(newRateStr) / 100;

            // 调用接口更新 OCR 准确率
            const response = await http.post('/test_project/update_result_ocr_accuracy_rate', {
                result_id: record.result_id,
                ocr_accuracy_rate: newRate
            });

            if (response.status === 0) {
                ElMessage.success('OCR 准确率修改成功');
                record.ocr_accuracy_rate = newRate;
                emit('refresh_table');
            } else {
                ElMessage.error('OCR 准确率修改失败');
            }
        }
    } catch (e) {
        if (e !== 'cancel') {
            ElMessage.error('OCR 准确率修改失败');
            console.error(e);
        }
    }
};

const toggleResult = async (record) => {
    try {
        // 首先弹窗询问是否切换结果
        const confirm = await ElMessageBox.confirm(
            `确定要将结果从"${record.result}"切换为"${record.result === '通过' ? '不通过' : '通过'}"吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                title: '人工复核'
            }
        );

        if (confirm === 'confirm') {
            // 弹窗输入修改原因
            const { value: reason } = await ElMessageBox.prompt(
                '请输入修改原因',
                '修改原因',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputValidator: (value) => {
                        return true;
                    }
                }
            );

            // 调用接口更新结果
            const response = await http.post('/test_project/update_result', {
                project_id: projectDetail.value.project_id,
                plan_id: props.planId,
                turn_id: props.turnId,
                result_id: record.result_id,
                new_result: record.result === '通过' ? '不通过' : '通过',
                update_result_reason: reason
            });

            if (response) {
                ElMessage.success('结果切换成功');
                record.result = record.result === '通过' ? '不通过' : '通过';
                emit('refresh_table');
            } else {
                ElMessage.error('结果切换失败');
            }
        }
    } catch (e) {
        if (e !== 'cancel') {
            ElMessage.error('结果切换失败');
        }
    }
};

const corpusModalVisible = ref(false);
const handleCorpusModalCancel = () => {
    corpusModalVisible.value = false;
}
const handleCorpusModalOk = (corpus: string) => {
    corpusModalVisible.value = false;
}

const planList = ref([]);
const isAddToPlanModalVisible = ref(false);
const addToPlanForm = ref({
    selectedPlanId: '',
});

const showAddToPlanModal = async () => {
    try {
        const response = await http.post('/test_project/get_plan_list', {
            project_id: projectDetail.value.project_id,
            turn_id: props.turnId,
        });
        if (response.status === 0) {
            planList.value = response.data.list.filter(x => x.plan_id.startsWith('retest'));
            isAddToPlanModalVisible.value = true;
        } else {
            ElMessage.error('获取方案列表失败');
        }
    } catch (error) {
        ElMessage.error('获取方案列表失败');
    }
};

const handleAddToPlanModalOk = async () => {
    if (!addToPlanForm.value.selectedPlanId) {
        ElMessage.error('请选择方案');
        return;
    }

    const corpus_columns = selectedRowKeys.value.map(x => {
        const single = testResults.value.find(item => item.result_id === x);
        return single
    }).map(x => x.corpus_id)

    try {
        const response = await http.post('/test_project/add_to_plan', {
            project_id: projectDetail.value.project_id,
            plan_id: addToPlanForm.value.selectedPlanId,
            corpus_ids: selectedRowKeys.value,
        });
        if (response.status === 0) {
            ElMessage.success('添加到方案成功');
            isAddToPlanModalVisible.value = false;
            selectedRowKeys.value = [];
        } else {
            ElMessage.error('添加到方案失败');
        }
    } catch (error) {
        ElMessage.error('添加到方案失败');
    }
};

const handleAddToPlanModalCancel = () => {
    isAddToPlanModalVisible.value = false;
    addToPlanForm.value.selectedPlanId = '';
};


</script>

<style>
/* 屏蔽 aria-label="rotate-right" 的元素 */
:deep([aria-label="rotate-right"]) {
    display: none !important;
}

/* 屏蔽 aria-label="rotate-left" 的元素 */
:deep([aria-label="rotate-left"]) {
    display: none !important;
}

/* 新增展开区域样式 */
.expanded-content {
    background: #efefef;
    /* 浅灰色背景 */
    border: 1px solid #e8e8e8;
    /* 添加边框 */
    border-radius: 4px;
    padding: 32px;
    margin: 8px 0;
}
</style>
