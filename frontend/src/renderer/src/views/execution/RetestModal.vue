<template>
    <a-modal title="复测语料配置" :visible="corpusModalVisible" @ok="handleOk" @cancel="handleCancel" okText="提交修改"
        cancelText="取消" width="70%">
        <a-tabs default-active-key="1" @change="handleLoading">
            <a-tab-pane key="1" tab="唤醒语料">
                <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
                <a-spin :spinning="loading">
                    <a-transfer show-search :filter-option="filterOption" :locale="{
                        searchPlaceholder: '请输入 文本/标签/发声人/语种'
                    }" :dataSource="filteredRouseCorpusList" :list-style="{
                        width: '50%',
                        height: '100%',
                        'max-height': '60vh',
                        'overflow': 'auto'
                    }" :titles="['可用语料', '已选语料']" :targetKeys="rouseCorpusKey || []" :render="renderItem"
                        :pagination="{ pageSize: 100 }" @change="handleRouseTransferChange" />
                </a-spin>
            </a-tab-pane>
            <a-tab-pane key="2" tab="测试语料" v-if="type !== 'rouse'">
                <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
                <a-spin :spinning="loading">
                    <a-transfer show-search :filter-option="filterOption" :locale="{
                        searchPlaceholder: '请输入 文本/标签/发声人/语种'
                    }" :dataSource="filteredTestCorpusList" :list-style="{
                        width: '50%',
                        height: '100%',
                        'max-height': '60vh',
                        'overflow': 'auto'
                    }" :titles="['可用语料', '已选语料']" :targetKeys="testCorpusKeys" :render="renderItem"
                        :pagination="{ pageSize: 100 }" @change="handleTransferChange" showSelectAll />
                </a-spin>

            </a-tab-pane>
            <a-tab-pane key="3" tab="干扰语料">
                <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
                <a-spin :spinning="loading">
                    <a-transfer show-search :filter-option="filterOption" :locale="{
                        searchPlaceholder: '请输入 文本/标签/发声人/语种'
                    }" :dataSource="filteredDisturbCorpusList" :list-style="{
                            width: '50%',
                            height: '100%',
                            'max-height': '60vh',
                            'overflow': 'auto'
                        }" :titles="['可用语料', '已选语料']" :targetKeys="disturbCorpusKeys || []"
                        :render="item => `${item.text} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`"
                        :pagination="{ pageSize: 100 }" @change="handleDisturbTransferChange" />
                </a-spin>
            </a-tab-pane>
        </a-tabs>
    </a-modal>
</template>

<script setup lang="tsx">
import { useProjectStore } from '@renderer/stores/useProject';
import { defineProps, ref, computed, onMounted, reactive, watch, toRef } from 'vue';
import { storeToRefs } from 'pinia';
import CustomPlanFilter from '../customPlan/CustomPlanFilter.vue';
import { http } from '@renderer/http';
import { ElMessage } from 'element-plus';

const male_voice_map = {
    '1': '男声1',
    '2': '男声2',
    '3': '男声3',
    '4': '男声4',
    '5': '男声5',
    '6': '男声6',
    '7': '童声',
    '8': '东北话',
    '9': '天津话',
};

const female_voice_map = {
    '1': '女声1',
    '2': '女声2',
    '3': '女声3',
    '4': '女声4',
    '5': '女声5',
    '6': '女声6',
    '7': '女声7',
    '8': '童声',
    '9': '四川话',
    '10': '粤语',
    '11': '东北话',
};

const props = defineProps({
    corpusModalVisible: {
        type: Boolean,
        default: false
    },
    handleCorpusModalOk: {
        type: Function,
        required: true
    },
    handleCorpusModalCancel: {
        type: Function,
        required: true
    },
    type: {
        type: String,
    },
    project_id: {
        type: String,
    },
    planId: {
        type: String
    }
})

const corpusModalVisible = toRef(props, 'corpusModalVisible')
const play_config_id = ref('')


watch(corpusModalVisible, async (newVal) => {
    const result = (await http.post('/test_project/get_plan_detail', {
        project_id: props.project_id,
        plan_id: props.planId,
    })).data;
    testCorpusKeys.value = [...(result.testCorpusList || [])];
    disturbCorpusKeys.value = [...(result.disturbCorpusList || [])];
    rouseCorpusKey.value = rouseCorpusKey.value = result.rouseCorpusList;
    play_config_id.value = result.play_config_id;
})

const handleCancel = () => {
    props.handleCorpusModalCancel();
}

const handleOk = async () => {
    props.handleCorpusModalOk();
    console.log('rouse')
    const result = await http.post('/test_project/save_plan_detail', {
        project_id: props.project_id,
        plan_id: props.planId,
        play_config_id: play_config_id.value,
        rouseCorpusList: rouseCorpusKey.value,
        testCorpusList: testCorpusKeys.value,
        disturbCorpusList: disturbCorpusKeys.value,
    })
    if (result?.status === 0) {
        ElMessage.success('更新成功');
    } else {
        ElMessage.error('更新失败');
    }

}

onMounted(async () => {
    // 获取测试语料列表
    testCorpusList.value = (await http.post('/corpus/get_test_corpus_list', { is_multi: false })).data.list;

    // 获取唤醒语料列表
    rouseCorpusList.value = (await http.post('/corpus/get_rouse_corpus_list', {})).data.list;

    disturbCorpusList.value = (await http.post('/corpus/get_disturb_corpus_list', {})).data.list;

    // 获取多轮语料列表
    let multiCorpusList = (await http.post('/multi-corpus/list', { label: "" })).data.list;

    multiCorpusList = multiCorpusList.map(x => {
        x.type = "multi";
        return x;
    })
    // 将多轮语料列表拼接到 testCorpusList 中
    testCorpusList.value = testCorpusList.value.concat(multiCorpusList);

    testCorpusList.value.map(item => {
        item.key = `${item.corpus_id}`;
        if (typeof item.speaker === 'string') {
            item.speaker = item.speaker === 'male' ? '男声' : '女声';
            item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
        }
        return {
            title: item.text,
            key: `${item.corpus_id}`,
        }
    });

    rouseCorpusList.value.map(item => {
        if (typeof item.speaker === 'string') {
            item.speaker = item.speaker === 'male' ? '男声' : '女声';
            item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
        }
        item.key = `${item.corpus_id}`;
        item.label = `${item.text || item.corpus_id} - 时长: ${item.audio_duration || '0'}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`
        return {
            title: item.text,
            key: `${item.corpus_id}`,
            label: item.label,
        }
    });

    disturbCorpusList.value.map(item => {
        item.key = `${item.corpus_id}`;
        item.label = `${item.text || item.corpus_id} - 时长: ${item.audio_duration || '0'}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`;
        if (typeof item.speaker === 'string') {
            item.speaker = item.speaker === 'male' ? '男声' : '女声';
            item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
        }
        return {
            title: item.text,
            key: `${item.corpus_id}`,
            label: item.label,
        }
    })

})


const handleLoading = () => {
    loading.value = true;
    setTimeout(() => {
        loading.value = false;
    }, 340)
}

const filterItem = reactive({
    test_type: undefined,
    test_scenario: undefined,
    language: undefined,
    evaluation_metric: undefined,
    speaker: undefined,
    car_function: undefined,
    expect_result: undefined,
});



const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);
const loading = ref(false);
const filterOption = (inputValue, option) => {
    return (option.label || '').indexOf(inputValue) > -1 || (option.text || '').indexOf(inputValue) > -1 || option.language.indexOf(inputValue) > -1 || option.speaker?.indexOf(inputValue) > -1;
};
const testCorpusKeys = ref([]);
const rouseCorpusKey = ref([]);
const disturbCorpusKeys = ref([]);

const testCorpusList = ref([]);
const rouseCorpusList = ref([]);
const disturbCorpusList = ref([]);

const renderItem = (item) => {
    return (
        <div style={{ width: '100%' }}>
            <div style={{ width: '100%' }} title={`${item.text || item.corpus_name || item.corpus_id} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`}>
                {/* 普通语料/多轮对话语料标签 */}
                <div style={{
                    border: '1px solid #4CAF50',
                    display: 'inline-block',
                    fontSize: '12px',
                    borderRadius: '12px',
                    padding: '4px 8px',
                    marginRight: '0.3rem',
                    backgroundColor: '#E8F5E9',
                    color: '#2E7D32',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    fontWeight: '500'
                }}>
                    {item.type === 'multi' ? '多轮对话语料' : '普通语料'}
                </div>
                {`${item.text || item.corpus_name || item.corpus_id} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`}
                <pre hidden={item.type !== 'multi'}>
                    {(item.corpusItems || []).map(x => x.text).join(', ')}
                </pre>
            </div>
            <div hidden={item.type === 'multi'} style={{ marginTop: '4px', display: 'flex', alignItems: 'center', width: '100%' }} title={item.expect_result}>
                {/* 预期结果标签 */}
                <div style={{
                    border: '1px solid #2196F3',
                    display: 'inline-flex',
                    fontSize: '12px',
                    borderRadius: '12px',
                    padding: '4px 8px',
                    marginRight: '0.3rem',
                    backgroundColor: '#E3F2FD',
                    color: '#1565C0',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    fontWeight: '500',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    预期结果
                </div>
                <pre style="display: inline-block">
                    {item.expect_result}
                </pre>

            </div>
        </div>
    );
};

const filteredRouseCorpusList = computed(() => {
    return rouseCorpusList.value.filter(item => {
        return (
            (!filterItem.language || item.language === filterItem.language) &&
            (!filterItem.speaker || item.speaker === filterItem.speaker) &&
            (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
        );
    });
});

const filteredTestCorpusList = computed(() => {
    return testCorpusList.value.filter(item => {
        return (
            (!filterItem.language || item.language === filterItem.language) &&
            (!filterItem.speaker || item.speaker === filterItem.speaker) &&
            (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
        );
    });
});

const handleRouseTransferChange = (nextTargetKeys) => {
    rouseCorpusKey.value = nextTargetKeys;
};

const handleTransferChange = (nextTargetKeys) => {
    console.log(nextTargetKeys);
    testCorpusKeys.value = nextTargetKeys;
};

const handleDisturbTransferChange = (nextTargetKeys) => {
    disturbCorpusKeys.value = nextTargetKeys;
};

const filteredDisturbCorpusList = computed(() => {
    return disturbCorpusList.value.filter(item => {
        return (
            (!filterItem.language || item.language === filterItem.language) &&
            (!filterItem.speaker || item.speaker === filterItem.speaker) &&
            (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
        );
    });
});
</script>

<style scoped></style>