<template>
  <div
    style="display: flex; justify-content: space-between; height: 2.5rem; background-color: #f3f3f3; align-items: center;">
    <a-breadcrumb style="margin: 1rem;">
      <a-breadcrumb-item><a @click="goToProject">项目管理</a></a-breadcrumb-item>
      <a-breadcrumb-item>项目执行</a-breadcrumb-item>
    </a-breadcrumb>
  </div>

  <div style="height: calc(100vh - 14rem); padding: 1rem; display: flex; flex-direction: column;">

    <a-row justify="space-between" align="middle" style="flex: 1;">
      <a-col :span="8">
        <a-row justify="start">
          <a-button @click="startTest">
            <template #icon>
              <PlayCircleFilled />
            </template>
            开始测试
          </a-button>
          <a-button style="margin-left: 10px;" @click="stopTest">
            <template #icon>
              <CloseCircleFilled />
            </template>
            停止测试
          </a-button>
          <a-button style="margin-left: 10px;" @click="pauseTest" v-if="selectedType !== 'false-rouse'">
            <template #icon>
              <PauseCircleFilled />
            </template>
            暂停测试
          </a-button>
        </a-row>
      </a-col>

      <a-col :span="8">
        <div style="display: flex; justify-content: center; align-items: center;">
          <DeviceInit :project_id="projectDetail.project_id" style="margin-left: 10px;"></DeviceInit>
        </div>
      </a-col>

      <a-col :span="8">
        <div style="display: flex; justify-content: flex-end; align-items: center;">
          <a-progress type="line" :percent="progress" :stroke-color="{
            '0%': themeStore.colorPrimary,
            '100%': themeStore.colorPrimary,
          }" style="width: 80%;" />
          <div class="status-text" style="margin-left: 10px;">{{ statusText }}</div>
        </div>
      </a-col>
    </a-row>

    <a-col :span="24" style="margin-top: 1rem;">
      <a-card title="测试结果" class="test-results-card" style="margin-left: 8px; margin-bottom: 16px;">
        <template #extra class="test-results-header">
          <div style="float: right; transform: scale(0.85); margin-right: -2rem;">
            <a-button type="primary" @click="generateReport">生成报告</a-button>
            <a-button type="primary" style="margin-left: 1rem;" @click="exportResults">导出结果</a-button>
            <a-button type="primary" style="margin-left: 1rem;" @click="jumpToCheck">结果复核</a-button>
            <a-button type="primary" style="margin-left: 1rem" @click="refreshContinuously">持续刷新</a-button>
            <a-button type="primary" style="margin-left: 1rem;" @click="stopRefresh">停止刷新</a-button>
          </div>
        </template>
        <div style="display: flex; width: 100%">
          <div style="width: 14rem; margin-right: 1rem;">
            <p>方案选择</p>
            <a-col :span="8" style="text-align: right;">
              <div
                style="display: flex; align-items: center; border: 1px solid #e0e0e0; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); background-color: #ffffff;padding: 1rem; width: 14rem; height: 100%; overflow: auto;">
                <a-tree v-model:selectedKeys="selectedKeys" v-model:expandedKeys="expandedKeys" :tree-data="treeData"
                  @select="onSelect" style="width: 100%;">
                  <template #title="title">
                    <span v-if="title.key.startsWith('retest')"> <a-tag :color="themeStore.buttonColorPrimary">复</a-tag>
                    </span>
                    {{ title.title }}
                    <a-tag v-if="title.key.startsWith('retest')" :color="title.is_completed ? 'green' : 'pink'">{{
                      title.is_completed ? '已复测' : '未复测' }}</a-tag>
                  </template>
                </a-tree>
              </div>
            </a-col>
          </div>
          <div style="flex: 1">
            <a-col :span="8">
              <div style="display: flex; align-items: center;">
                <label style="margin-right: 10px;">Turn:</label>
                <a-select v-model:value="selectedTurnId" style="width: 12rem;" @change="onTurnChange">
                  <a-select-option v-for="turn in turns" :key="turn.turn_id" :value="turn.turn_id">
                    {{ turn.time }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-tabs v-model:activeKey="selectedType" @change="onTypeChange">
              <a-tab-pane key="interaction" tab="单次对话" v-if="selectedType === 'interaction'"></a-tab-pane>
              <a-tab-pane key="interaction-multi" tab="连续对话" v-if="selectedType === 'interaction-multi'"></a-tab-pane>
              <a-tab-pane key="rouse" tab="唤醒" v-if="selectedType === 'rouse'"></a-tab-pane>
              <a-tab-pane key="false-rouse" tab="误唤醒" v-if="selectedType === 'false-rouse'"></a-tab-pane>
            </a-tabs>
            <a-alert show-icon type="success" description="误唤醒测试已完成, 未检测到错误响应"
              style="padding: 0.5rem; margin-bottom: 1rem;"
              v-if="selectedType === 'false-rouse' && falseRouseTimes === 0 && statusText === '已完成'"></a-alert>

            <InteractionTable v-if="['rouse', 'interaction'].includes(selectedType)" :previewImage="previewImage"
              :testResults="testResults" :planId="selectedPlanId" :type="selectedType" :turn-id="selectedTurnId"
              @generate_retest="fetchPlanList(false)" @refresh_table="refreshTable" :isCompleted="isCompleted">
            </InteractionTable>
            <InteractionMultiTable v-if="selectedType === 'interaction-multi'" :testResults="testResults"
              :previewImage="previewImage" :planId="selectedPlanId" :type="selectedType" :turn-id="selectedTurnId"
              @generate_retest="fetchPlanList(false)" @refresh_table="refreshTable" :isCompleted="isCompleted">
            </InteractionMultiTable>
            <FalseRouseTable v-if="selectedType === 'false-rouse'" :previewImage="previewImage"
              :testResults="testResults" :falseRouseTimes="falseRouseTimes" :planId="selectedPlanId"
              :type="selectedType" :turn-id="selectedTurnId" @generate_retest="fetchPlanList"
              :isCompleted="isCompleted"></FalseRouseTable>
          </div>
        </div>
      </a-card>
    </a-col>

    <!-- 图片预览模态框 -->
    <a-modal v-model:visible="imagePreviewVisible" :footer="null" @cancel="imagePreviewVisible = false"
      style="height: 80vh; width: 80vw" okText="确定" cancelText="取消">
      <div ref="imageContainer" @wheel="handleWheel"
        style="display: flex; justify-content: center; align-items: center;">
        <img ref="previewImageRef" alt="example" :src="imagePreviewUrl" style="max-width: 100%; max-height: 100%;" />
      </div>
    </a-modal>

    <!-- 报告生成模态框 -->
    <a-modal v-model:visible="reportModalVisible" title="生成报告" @ok="handleReportSubmit"
      @cancel="reportModalVisible = false" okText="确定" cancelText="取消">
      <a-form :model="reportForm" layout="vertical">
        <a-form-item label="报告名称">
          <a-input v-model:value="reportForm.reportName" placeholder="请输入报告名称" />
        </a-form-item>
        <a-form-item label="报告编号">
          <a-input v-model:value="reportForm.reportNumber" placeholder="请输入报告编号" />
        </a-form-item>
        <a-form-item label="送检单位">
          <a-input v-model:value="reportForm.testingUnit" placeholder="请输入检测单位" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 导出结果模态框 -->
    <a-modal v-model:visible="exportModalVisible" title="选择导出的列" @ok="handleExportSubmit"
      @cancel="exportModalVisible = false" okText="确定" cancelText="取消" width="50%">
      <a-checkbox-group v-model:value="selectedColumns">
        <a-row>
          <a-col :span="6" v-for="column in getColumnsForCurrentType()" :key="column.key">
            <a-checkbox :value="column.label">{{ column.label }}</a-checkbox>
          </a-col>
        </a-row>
      </a-checkbox-group>
    </a-modal>
  </div>
</template>

<script lang="tsx" setup>
import { PauseCircleFilled, PlayCircleFilled, CloseCircleFilled, SmileOutlined } from '@ant-design/icons-vue';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';
import { http } from '@renderer/http';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import InteractionTable from './InteractionTable.vue';
import InteractionMultiTable from './InteractionMultiTable.vue';
import FalseRouseTable from './FalseRouseTable.vue';
import DeviceInit from './DeviceInit.vue';
import { useThemeStore } from '../../themeStore';
import { usePageStore } from '../../pageStore';

const isInit = ref(false);
const themeStore = useThemeStore();
const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);
const pageStore = usePageStore();
const { setPage, setPageSize, setResult, setSign, setTotal } = pageStore;
const { page, page_size, result, sign, total } = storeToRefs(pageStore);

watch([page, page_size, result, sign], () => {
  fetchTestInfo();
})

const testResults = ref([]);
const progress = ref(0);
const treeData = ref([]);
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref([]);
const logContent = ref('');
const statusText = ref('进行中');

const imagePreviewVisible = ref(false);
const imagePreviewUrl = ref('');
const turns = ref([]);
const selectedTurnId = ref(1);
const imageContainer = ref(null);
const previewImageRef = ref(null);
const scale = ref(1);
const selectedType = ref('interaction'); // 默认选中唤醒
const isCompleted = ref(false);

const selectedPlanId = ref('');

const falseRouseTimes = ref(0);

const reportModalVisible = ref(false);
const reportForm = ref({
  reportName: '',
  reportNumber: '',
  testingUnit: ''
});

let turnInterval = null;

onMounted(async () => {
  await fetchTurns();
  fetchPlanList().finally(() => {
    if (treeData.value.length > 0) {
      // selectedKeys.value = [treeData.value[0].children[0].key];
      // selectedPlanId.value = treeData.value[0].children[0].key;
    }
    ;
    clearInterval(turnInterval);
    turnInterval = setInterval(() => {
      fetchTurnsNotSet();
    }, 5000);
    fetchTestInfo();
  });

  window.electron.ipcRenderer.on('showpic', (event, { data }) => {
    const blob = new Blob([data], { type: 'image/jpeg' });
    const url = URL.createObjectURL(blob);
    imagePreviewUrl.value = url;
    imagePreviewVisible.value = true;
  });

  window.electron.ipcRenderer.on('download-template-selected', (event, fileName) => {
    window.electron.ipcRenderer.send('start-download', base64String.value, fileName, 'results.xlsx', 'default', projectDetail.value.project_id);
  });
});

onUnmounted(() => {
  clearInterval(interval);
  clearInterval(turnInterval);
  window.electron.ipcRenderer.removeAllListeners('showpic');
});

const stopRefresh = () => {
  ElMessage.success('已停止刷新');
  clearInterval(interval);
};

const fetchPlanList = async (repairIndex = true) => {
  const response = (await http.post('/test_project/get_plan_list', {
    project_id: projectDetail.value.project_id,
    turn_id: selectedTurnId.value,
  })).data;

  const rouseConfigs = [];
  const falseRouseConfigs = [];
  const interactionConfigs = [];
  const interactionMultiConfigs = [];
  console.log(response.list);
  response.list.forEach((item, index) => {
    if (index === 0 && repairIndex) {
      selectedPlanId.value = item.plan_id;
      selectedKeys.value = [item.plan_id];
      selectedType.value = item.type;
      isCompleted.value = item.is_completed;
    }
    switch (item.type) {
      case 'rouse':
        rouseConfigs.push({
          title: item.plan_name,
          key: item.plan_id,
          type: item.type,
          is_completed: item.is_completed
        });
        break;
      case 'false-rouse':
        falseRouseConfigs.push({
          title: item.plan_name,
          key: item.plan_id,
          type: item.type,
          is_completed: item.is_completed,
        });
        break;
      case 'interaction':
        interactionConfigs.push({
          title: item.plan_name,
          key: item.plan_id,
          type: item.type,
          is_completed: item.is_completed,
        });
        break;
      case 'interaction-multi':
        interactionMultiConfigs.push({
          title: item.plan_name,
          key: item.plan_id,
          type: item.type,
          is_completed: item.is_completed,
        });
        break;
    }
  });

  treeData.value = [
    {
      title: '唤醒场景',
      key: 'rouse',
      selectable: false,
      children: rouseConfigs,
    },
    {
      title: '误唤醒场景',
      key: 'false-rouse',
      selectable: false,
      children: falseRouseConfigs,
    },
    {
      title: '智能交互场景',
      key: 'interaction',
      selectable: false,
      children: interactionConfigs,
    },
    {
      title: '连续对话场景',
      key: 'interaction-multi',
      selectable: false,
      children: interactionMultiConfigs,
    },
  ];
  console.log(treeData.value);
  expandedKeys.value = ['rouse', 'false-rouse', 'interaction', 'interaction-multi'];
};

const columnMapping = {
  'interaction': [
    { key: '项目名称', label: '项目名称' },
    { key: '方案名称', label: '方案名称' },
    { key: '结果id', label: '结果id' },
    { key: '测试时间', label: '测试时间' },
    { key: '语料id', label: '语料id' },
    { key: '第几轮测试', label: '第几轮测试' },
    { key: '测试场景', label: '测试场景' },
    { key: '语料文本', label: '语料文本' },
    { key: '预期结果', label: '预期结果' },
    { key: 'OCR图片判断置信度', label: 'OCR图片判断置信度' },
    { key: '模型图片判断置信度', label: '模型图片判断置信度' },
    { key: '模型语音判断置信度', label: '模型语音判断置信度' },
    { key: '判断结果', label: '判断结果' },
    { key: '判断分数', label: '判断分数' },
    { key: '评估理由', label: '评估理由' },
    { key: '修改测试结果理由', label: '修改测试结果理由'},
    { key: '语料播出前图片', label: '语料播出前图片' },
    { key: '语料播出后图片', label: '语料播出后图片' },
    { key: '车机响应识别', label: '车机响应识别' },
    { key: '录音路径', label: '录音路径' },
    { key: '车机识别结果', label: '车机识别结果' },
    { key: '结果标记', label: '结果标记' }
  ],
  'interaction-multi': [
    { key: '项目名称', label: '项目名称' },
    { key: '方案名称', label: '方案名称' },
    { key: '结果id', label: '结果id' },
    { key: '测试时间', label: '测试时间' },
    { key: '语料id', label: '语料id' },
    { key: '第几轮测试', label: '第几轮测试' },
    { key: '测试场景', label: '测试场景' },
    { key: '语料文本', label: '语料文本' },
    { key: '预期结果', label: '预期结果' },
    { key: '判断结果', label: '判断结果' },
    { key: '修改测试结果理由', label: '修改测试结果理由'},
    { key: '判断分数', label: '判断分数' },
    { key: '评估理由', label: '评估理由' },
    { key: '车机响应识别', label: '车机响应识别' },
    { key: '录音路径', label: '录音路径' },
    { key: '语料播出前图片', label: '语料播出前图片' },
    { key: '语料播出后图片', label: '语料播出后图片' },
    { key: '车机识别结果', label: '车机识别结果' },
    { key: '结果标记', label: '结果标记' },
    { key: '连续对话判断', label: '连续对话判断' }
  ],
  'rouse': [
    { key: '项目名称', label: '项目名称' },
    { key: '方案名称', label: '方案名称' },
    { key: '结果id', label: '结果id' },
    { key: '测试时间', label: '测试时间' },
    { key: '语料id', label: '语料id' },
    { key: '第几轮测试', label: '第几轮测试' },
    { key: '测试场景', label: '测试场景' },
    { key: '语料文本', label: '语料文本' },
    { key: '判断结果', label: '判断结果' },
    { key: '修改测试结果理由', label: '修改测试结果理由'},
    { key: '车机响应识别', label: '车机响应识别' },
    { key: '录音路径', label: '录音路径' },
    { key: '结果标记', label: '结果标记' }
  ],
  'false-rouse': [
    { key: '项目名称', label: '项目名称' },
    { key: '方案名称', label: '方案名称' },
    { key: '结果id', label: '结果id' },
    { key: '干扰语料id', label: '干扰语料id' },
    { key: '唤醒响应', label: '唤醒响应' },
    { key: '误唤醒时刻', label: '误唤醒时刻' },
    { key: '第几轮测试', label: '第几轮测试' },
    { key: '唤醒录音', label: '唤醒录音' },
    { key: '唤醒图像', label: '唤醒图像' },
  ],
};


const fetchTurns = async (notFetch = true) => {
  await http.post('/test_project/get_turns', {
    project_id: projectDetail.value.project_id,
  }).then((resp) => {
    turns.value = resp.data.list.map((turn_id, index) => ({
      turn_id,
      time: `Turn ${index + 1}`,
    }));
    if (turns.value.length > 0) {
      selectedTurnId.value = turns.value[turns.value.length - 1]?.turn_id;
    }

    if (turns.value.length > 0 && notFetch) {
      setTimeout(() => {
        fetchTestInfo();
      }, 100)

    }
  });
};

const fetchTurnsNotSet = async () => {
  http.post('/test_project/get_turns', {
    project_id: projectDetail.value.project_id,
  }).then((resp) => {
    turns.value = resp.data.list.map((turn_id, index) => ({
      turn_id,
      time: `Turn ${index + 1}`,
    }));
  });
};

const fetchTestInfo = async () => {
  if (!window.location.hash.includes('/execution')) {
    clearInterval(interval);
    return;
  }
  if (!selectedPlanId.value) {
    testResults.value = [];
    return;
  }
  const params = {
    project_id: projectDetail.value.project_id,
    turn_id: String(selectedTurnId.value),
    plan_id: selectedPlanId.value,
    type: selectedType.value,
    page: page.value,
    page_size: page_size.value,
    sign: sign.value || undefined,
    result: result.value,
  }
  if (selectedType.value === 'interaction-multi' || selectedType.value === 'false-rouse') {
    delete params.page;
    delete params.page_size;
    delete params.sign;
    delete params.result;
  }
  const response = (await http.post('/test_project/get_test_info', params)).data;
  progress.value = parseInt(response.process);
  testResults.value = (response.result_list || []);
  setTotal(response.total);
  falseRouseTimes.value = (response.false_wakeup_times || 0);
  logContent.value = response.log;
  statusText.value = response.status === 'completed' ? '已完成' : '进行中';
};

const refreshTable = () => {
  fetchTestInfo();
}

const startTest = async () => {
  const payload = {
    project_id: projectDetail.value.project_id,
  };
  http.post('/test_project/start_test', payload).then(async (resp) => {
    await fetchTurns();
    setTimeout(async () => {
      if (interval) {
        clearInterval(interval);
      }
      interval = setInterval(() => { fetchTestInfo() }, 10000);
    }, 1000);
  }).catch((err) => {
    if (err.response.data.detail) {
      ElMessage.error(err.response.data.detail);
    }
  });
};

const pauseTest = async () => {
  const payload = {
    project_id: projectDetail.value.project_id,
  };
  http.post('/test_project/pause_test', payload).then((resp) => {
  }).catch((err) => {
    if (err.response.data.detail) {
      ElMessage.error(err.response.data.detail);
    }
  });
};

const stopTest = async () => {
  const payload = {
    project_id: projectDetail.value.project_id,
  };
  const result = (await http.post('/test_project/stop_test', payload));
  if (result) {
    ElMessage.success('测试已停止');
  }
  clearInterval(interval);
};

const onSelect = (selectedKeys, info) => {
  if (info.node.children) {
    return;
  }
  selectedPlanId.value = selectedKeys[0];
  if (info.node) {
    selectedType.value = info.node.type || 'interaction';
    isCompleted.value = info.node.isCompleted ?? false;
  }
  fetchTestInfo();
};

const onTurnChange = async () => {
  const params = {
    project_id: projectDetail.value.project_id,
    turn_id: String(selectedTurnId.value),
    plan_id: selectedPlanId.value,
    type: selectedType.value,
    page: page.value,
    page_size: page_size.value,
    sign: sign.value || undefined,
    result: result.value,
  }
  if (selectedType.value === 'interaction-multi' || selectedType.value === 'false-rouse') {
    delete params.page;
    delete params.page_size;
    delete params.sign;
    delete params.result;
  }
  const response = (await http.post('/test_project/get_test_info', params)).data;
  progress.value = parseInt(response.process);
  testResults.value = (response.result_list || []);
  logContent.value = response.log;
  setTotal(response.total);
  setPage(1);
  statusText.value = response.status === 'completed' ? '已完成' : '进行中';
  fetchPlanList(false);
  falseRouseTimes.value = (response.false_wakeup_times || 0);
  if (response.status === 'completed') {
    clearInterval(interval);
  }
};

const onTypeChange = async () => {
  testResults.value = [];
  await fetchTestInfo();
};

const previewImage = (filePath) => {
  window.electron.ipcRenderer.send('getImage', filePath);
};

const exportModalVisible = ref(false);
const selectedColumns = ref<string[]>([]);

const getColumnsForCurrentType = () => {
  return columnMapping[selectedType.value] || [];
};

const handleExportSubmit = async () => {
  try {
    http.post('/test_project/export_results', {
      project_id: projectDetail.value.project_id,
      turn_id: selectedTurnId.value,
      type: selectedType.value,
      columns: selectedColumns.value,
    }, { responseType: 'blob' }).then(response => {
      const timestamp = new Date().getTime();

      // 创建 Blob 对象
      const blob = new Blob([response], { type: 'application/octet-stream' });

      // 获取 content-disposition 头部
      const contentDisposition = response.headers['content-disposition'];

      // 解析文件名
      let fileName = 'exported_file.xlsx'; // 默认文件名
      if (contentDisposition && contentDisposition.includes('filename*=')) {
        // 提取 filename* 部分
        const filenamePart = contentDisposition
          .split('filename*=')[1] // 获取 filename* 后面的部分
          .split(';')[0] // 去掉可能的分号
          .trim(); // 去掉空格

        // 去掉 utf-8'' 前缀并解码 URL 编码
        fileName = decodeURIComponent(filenamePart.replace('utf-8\'\'', ''));
      }

      console.log(fileName, 'filename');

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 设置文件名
      document.body.appendChild(link);

      // 触发下载
      link.click();

      // 移除下载链接
      link.remove();
      window.URL.revokeObjectURL(url);
    })
      .catch(error => {
        console.error('下载文件失败', error);
      });
  } catch (error) {
    console.error('请求失败', error);
  }
};

const exportResults = () => {
  selectedColumns.value = getColumnsForCurrentType().map(column => column.label);
  exportModalVisible.value = true;
};


const router = useRouter();

const jumpToCheck = () => {
  if (!selectedTurnId.value || !selectedPlanId.value) {
    ElMessage.error('当前无数据!');
    return;
  }
  localStorage.setItem('turn_id', selectedTurnId.value);
  localStorage.setItem('plan_id', selectedPlanId.value);
  localStorage.setItem('result_type', selectedType.value);
  router.push('/resultCheck');
};

let interval = null;

const handleWheel = (event) => {
  event.preventDefault();
  const delta = event.deltaY > 0 ? -0.1 : 0.1;
  scale.value += delta;
  scale.value = Math.min(Math.max(0.1, scale.value), 3);
  if (previewImageRef.value) {
    previewImageRef.value.style.transform = `scale(${scale.value})`;
  }
};

let last = 0;

const refreshContinuously = () => {
  if (Date.now() - last < 1000) {
    ElMessage.error('刷新太频繁了, 请等一会');
    return;
  }
  if (interval) {
    clearInterval(interval);
    interval = null;
  }
  fetchTestInfo();
  ElMessage.success('已刷新');
  last = Date.now();

  setTimeout(async () => {
    if (interval) {
      clearInterval(interval);
    }
    interval = setInterval(fetchTestInfo, 5000);
  }, 50);
};

const goToProject = () => {
  router.push('/project');
};

const generateReport = () => {
  reportModalVisible.value = true;
};

const handleReportSubmit = async () => {
  try {
    await http.post('/test_project/generate_report', {
      project_id: projectDetail.value.project_id,
      turn_id: selectedTurnId.value,
      report_name: reportForm.value.reportName,
      report_number: reportForm.value.reportNumber,
      testing_unit: reportForm.value.testingUnit
    }, { responseType: 'blob' }).then(response => {
      const timestamp = new Date().getTime();

      // 创建 Blob 对象
      const blob = new Blob([response], { type: 'application/octet-stream' });
      console.log(blob);

      // 获取 content-disposition 头部
      const contentDisposition = response.headers['content-disposition'];
      console.log(contentDisposition, 'content-disposition');

      // 解析文件名
      let fileName = 'exported_file.xlsx'; // 默认文件名
      if (contentDisposition && contentDisposition.includes('filename*=')) {
        // 提取 filename* 部分
        const filenamePart = contentDisposition
          .split('filename*=')[1] // 获取 filename* 后面的部分
          .split(';')[0] // 去掉可能的分号
          .trim(); // 去掉空格

        // 去掉 utf-8'' 前缀并解码 URL 编码
        fileName = decodeURIComponent(filenamePart.replace('utf-8\'\'', ''));
      }
      if (contentDisposition && contentDisposition.includes('filename=')) {
        // 提取 filename* 部分
        let filenamePart = contentDisposition
          .split('filename=')[1] // 获取 filename* 后面的部分
          .split(';')[0] // 去掉可能的分号
          .trim() // 去掉空格

        filenamePart = filenamePart.slice(1, filenamePart.length - 1);
        // 去掉 utf-8'' 前缀并解码 URL 编码
        fileName = decodeURIComponent(filenamePart.replace('utf-8\'\'', ''));
      }

      console.log(fileName, 'filename');

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 设置文件名
      document.body.appendChild(link);

      // 触发下载
      link.click();

      // 移除下载链接
      link.remove();
      window.URL.revokeObjectURL(url);
    })
      .catch(error => {
        console.error('下载文件失败', error);
      });
  } catch (error) {
    console.error('请求失败', error);
  }
}
</script>

<style scoped>
@import './ExecutionMain.css';
</style>