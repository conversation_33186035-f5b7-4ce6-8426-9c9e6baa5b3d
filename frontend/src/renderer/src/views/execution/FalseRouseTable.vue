<template>
    <div style="margin-bottom: 1rem; display:flex; align-items: center;">
        <h4 style="width: 8rem">误唤醒次数</h4>
        <a-input v-model:value="falseRouseTimes" :disabled="true" style="width: 16.5rem;"></a-input>
        <video
        :src="getStaticVideoUrl(pagedTestResults?.[0]?.video_path)" controls
        style="width: 100%; max-width: 400px; margin-left: 2rem;"></video>
    </div>
    <!-- 新增按钮 -->
    <a-table :columns="columns" :dataSource="pagedTestResults" :rowKey="record => record.result_id" bordered
        size="small" class="test-results-table" :pagination="false" :expand-column-width="50">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'time'">
                {{ new Date(record.time).toLocaleString() }}
            </template>
            <template v-else-if="column.key === 'mic_audio_url'">
                <audio :src="getStaticUrl(record.mic_audio_url)" controls></audio>
            </template>
            <template v-else-if="column.key === 'image'">
                <el-image v-for="singleImage in getArray(record.image || [])" style="width: 50px; height: 50px"
                    :src="getStaticPictureUrl(singleImage)" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                    :preview-src-list="[getStaticPictureUrl(singleImage)]" :initial-index="4" fit="cover" />
            </template>
        </template>
    </a-table>
    <a-pagination v-model:current="currentPage" :total="totalResults" :pageSize="pageSize" show-less-items
        @change="onPageChange" style="margin-top: 20px; text-align: center;" :pageSizeOptions="['10', '20', '50', '100', '500', '1000']"/>
    <a-modal v-model:visible="isRetestModalVisible" destroyOnClose title="生成复测方案" @ok="handleRetestModalOk"
        @cancel="handleRetestModalCancel">
        <a-form :model="retestForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="复测方案名称" required>
                <a-input v-model:value="retestForm.retest_plan_name" placeholder="请输入复测方案名称" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>
<script setup lang="ts">
import { http } from '@renderer/http';
import { ElMessage } from 'element-plus';
import { ref, computed, toRef, watch } from 'vue';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';

const emit = defineEmits<{
    (e: 'generate_retest'): void;
}>();

const currentPage = ref(1);
const pageSize = ref(10);

const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);
const selectedRowKeys = ref<string[]>([]); // 选中的行 keys

const props = defineProps({
    testResults: {
        type: Array,
        required: true
    },
    previewImage: {
        type: Function,
        required: true
    },
    planId: {
        type: String,
    },
    type: {
        type: String,
    },
    turnId: {
        type: Number,
    },
})
watch(
    () => props.testResults,
    () => {
        currentPage.value = 1; // 重置 currentPage 为 1
    },
);
const falseRouseTimes = computed(() => props.testResults?.length || 0);

// 注释掉原来的 testResults
const testResults = toRef(props, 'testResults');

const totalResults = computed(() => testResults.value.length);

const pagedTestResults = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    const currentPages = testResults.value.slice(start, end);
    for (const result of currentPages) {
        if (result.image && !Array.isArray(result.image)) {
            result.image = [result.image]
        }
    }
    return currentPages;
});

const onPageChange = (page: number) => {
    currentPage.value = page;
};

const columns = [
    { title: '误唤醒时刻', dataIndex: 'time', key: 'time', align: 'center' },
    { title: '唤醒响应', dataIndex: 'asr_result', key: 'asr_result', align: 'center' },
    { title: '唤醒录音', dataIndex: 'mic_audio_url', key: 'mic_audio_url', align: 'center' },
    { title: '唤醒图像', dataIndex: 'image', key: 'image', align: 'center' },
];

const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
};

const isRetestModalVisible = ref(false); // 控制弹窗显示
const retestForm = ref({
    retest_plan_name: '', // 复测方案名称
});

// 显示弹窗
const showRetestModal = () => {
    isRetestModalVisible.value = true;
};

const handleRetestModalOk = async () => {
    if (!retestForm.value.retest_plan_name) {
        ElMessage.error('请输入复测方案名称');
        return;
    }
    await generateRetest(retestForm.value.retest_plan_name);
    isRetestModalVisible.value = false;
};

// 处理弹窗取消
const handleRetestModalCancel = () => {
    isRetestModalVisible.value = false;
};

const doRetest = async () => {
    try {
        const response = (await http.post('/test_project/execute_retest_plan', {
            project_id: projectDetail.value.project_id,
            plan_id: props.planId,
            turn_id: props.turnId,
        }));
        if (response) {
            ElMessage.success('开始复测');
        } else {
            ElMessage.error('开始复测失败');
        }
    } catch (error) {
        ElMessage.error('开始复测失败');
    }
}

const generateRetest = async (retest_plan_name) => {
    try {
        const response = (await http.post('/test_project/create_retest_plan', {
            corpus_columns: selectedRowKeys.value.reverse(),
            project_id: projectDetail.value.project_id,
            plan_id: props.planId,
            turn_id: props.turnId,
            retest_plan_name,
        }));
        if (response) {
            ElMessage.success('生成复测方案成功');
        } else {
            ElMessage.error('生成复测方案失败');
        }
        emit('generate_retest');
    } catch (error) {
        ElMessage.error('生成复测方案失败');
    }
};

const getArray = (image) => {
    if (Array.isArray(image)) {
        return image;
    }
    return [image]
}

const visibleMap = ref<{ [key: string]: boolean }>({});

const getStaticUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    } else {
        return 'http://127.0.0.1:8080/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
    }
}

const getStaticPictureUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    } else {
        return 'http://127.0.0.1:8080/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    }
}

const getStaticVideoUrl = (url) => {
  if (!url) return '';
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    return urlCustomed + 'video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
  } else {
    return 'http://127.0.0.1:8080/video/' + ((url || '').replaceAll('\\', '/').split('video')?.[1] || '');
  }
};

// 停止误唤醒测试
const stopFalseWakeupTest = async () => {
  try {
    const response = await http.post('/project/stop_test', {
      project_id: projectDetail.value.project_id
    });
    if (response.status === 0) {
      ElMessage.success('已停止误唤醒测试');
    } else {
      ElMessage.error('停止误唤醒测试失败');
    }
  } catch (error) {
    ElMessage.error('停止误唤醒测试失败');
    console.error(error);
  }
};
</script>