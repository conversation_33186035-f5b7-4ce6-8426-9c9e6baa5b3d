<template>
  <div style="display: flex; flex-direction: column; height: 100vh;">
    <div
      style="display: flex; justify-content: space-between; height: 2.5rem; background-color: #f3f3f3; align-items: center;">
      <!-- 面包屑导航 -->
      <a-breadcrumb style="margin: 1rem;">
        <a-breadcrumb-item>
          <a @click="goToExecution">项目执行</a>
        </a-breadcrumb-item>
        <a-breadcrumb-item>执行结果</a-breadcrumb-item>
      </a-breadcrumb>
      <div class="flex hover-shadow" style="align-items: center;cursor: pointer;" @click="goToExecution">
        <RollbackOutlined style="width: 14px; height: 14px;" />
        <div style="transform: scale(0.8);">返回</div>
      </div>
    </div>


    <div style="display: flex; flex: 1;">
      <!-- 左侧表格 -->
      <div style="flex: 2; padding: 1rem;">
        <a-card :bordered="false" class="review-card">
          <div class="review-header">
            测试结果
          </div>
          <InteractionTable v-if="['rouse', 'interaction'].includes(currentType)" :seekVideo="seekVideo"
            :preview-image="previewImage" :testResults="testResults"></InteractionTable>
          <InteractionMultiTable v-if="currentType === 'interaction-multi'" :testResults="testResults"
            :preview-image="previewImage" :seekVideo="seekVideo"></InteractionMultiTable>
          <FalseRouseTable v-if="currentType === 'false-rouse'" :test-results="testResults"
            :preview-image="previewImage" :seekVideo="seekVideo"></FalseRouseTable>
        </a-card>
      </div>
      <!-- 右侧视频播放器 -->
      <div style="flex: 1; padding: 1rem;">
        <a-card :bordered="false" class="video-card">
          <div class="video-header">
            视频播放
          </div>
          <video ref="videoPlayer" class="video-js" width="640" height="264"></video>
        </a-card>
      </div>
    </div>
    <!-- 图片预览模态框 -->
    <a-modal v-model:visible="imagePreviewVisible" :footer="null" @cancel="imagePreviewVisible = false">
      <img alt="图片错误" style="width: 100%" :src="imagePreviewUrl" />
    </a-modal>
    <!-- 复核结果模态框 -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { http } from '@renderer/http';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import InteractionTable from './InteractionTable.vue';
import InteractionMultiTable from './InteractionMultiTable.vue';
import FalseRouseTable from './FalseRouseTable.vue';
import { RollbackOutlined } from '@ant-design/icons-vue';
const route = useRoute();
const router = useRouter();
const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);


const testResults = ref([]);
const videoUrl = ref('');
const videoPlayer = ref(null);
const imagePreviewVisible = ref(false);
const imagePreviewUrl = ref('');
const reviewModalVisible = ref(false);
const reviewResult = ref('');
const currentRecord = ref(null);
const currentType = ref('');

const columns = [
  { title: '时间', dataIndex: 'time', key: 'time', align: 'center' },
  { title: '语料场景', dataIndex: 'scene', key: 'scene', align: 'center' },
  { title: '语料文本', dataIndex: 'text', key: 'text', align: 'center' },
  { title: '语音转文字识别结果', dataIndex: 'asr_result', key: 'asr_result', align: 'center' },
  { title: 'OCR 图片', dataIndex: 'ocr_pic_url', key: 'ocr_pic_url', align: 'center' },
  { title: 'OCR 结果', dataIndex: 'ocr_result', key: 'ocr_result', align: 'center' },
  { title: 'OCR 准确率', dataIndex: 'ocr_accuracy_rate', key: 'ocr_accuracy_rate', align: 'center' },
  { title: '测试结果', dataIndex: 'result', key: 'result', align: 'center' },
  { title: '结果图片', dataIndex: 'image', key: 'image', align: 'center' },
  { title: '得分', dataIndex: 'score', key: 'score', align: 'center' },
  { title: '响应时间', dataIndex: 'response_time', key: 'response_time', align: 'center' },
  { title: '预期结果', dataIndex: 'expect_result', key: 'expect_result', align: 'center' },
  { title: '操作', key: 'operation', align: 'center' },
];

onMounted(async () => {
  const resultId = route.params.resultId;
  const response = (await http.post('/test_project/get_test_info', {
    project_id: projectDetail.value.project_id,
    turn_id: localStorage.getItem('turn_id'),
    plan_id: localStorage.getItem('plan_id'),
    type: localStorage.getItem('result_type'),

  })).data;
  testResults.value = response.result_list || [];
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    videoUrl.value = urlCustomed + `static/${projectDetail.value.project_id}_${localStorage.getItem('turn_id')}.mp4`;
  } else {
    videoUrl.value = `http://127.0.0.1:8080/static/${projectDetail.value.project_id}_${localStorage.getItem('turn_id')}.mp4`;
  }

  if (videoUrl.value) {
    initVideoPlayer();
  }

  // 监听 showpic 事件
  window.electron.ipcRenderer.on('showpic', (event, { data }) => {
    const blob = new Blob([data], { type: 'image/jpeg' });
    const url = URL.createObjectURL(blob);
    imagePreviewUrl.value = url;
    imagePreviewVisible.value = true;
  });
  console.log(localStorage.getItem('result_type'));
  currentType.value = localStorage.getItem('result_type');
});

onUnmounted(() => {
  if (videoPlayer.value) {
    const player = videojs(videoPlayer.value);
    if (player) {
      player.dispose();
    }
  }
  window.electron.ipcRenderer.removeAllListeners('showpic');
});
const initVideoPlayer = async () => {

  if (videoPlayer.value) {
    const player = videojs(videoPlayer.value, {
      autoplay: true,
      controls: true,
      innerWidth: 480,
      innerHeight: 270,
      preload: 'auto',
      sources: [
        {
          src: videoUrl.value,
          type: 'video/mp4',
        },
      ],
      fluid: false, // 使视频播放器自适应容器大小
    });

    player.ready(() => {
      console.log('Player is ready');
    });
  }
};

const previewImage = (filePath) => {
  window.electron.ipcRenderer.send('getImage', filePath);
};



const seekVideo = (seconds) => {
  if (videoPlayer.value) {
    const player = videojs(videoPlayer.value);
    player.currentTime(seconds);
  }
};

const goToExecution = () => {
  router.push('/execution');
};

const showReviewModal = (record) => {
  currentRecord.value = record;
  reviewModalVisible.value = true;
};

</script>

<style scoped>
.review-card,
.video-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 300px;
  /* 设置最小高度 */
  margin: 10px;
}

.review-header,
.video-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.review-table {
  width: 100%;
  table-layout: fixed;
}

.review-table th,
.review-table td {
  padding: 12px;
  text-align: center;
}

.review-table th {
  background-color: #fafafa;
  font-weight: bold;
}

.review-table tr:nth-child(even) {
  background-color: #fafafa;
}

.review-table tr:hover {
  background-color: #f0f5ff;
}

.video-js {
  width: 100%;
  height: auto;
}

.font {
  font-family: 'Microsoft YaHei';
}

.hover-shadow {
  padding: 0.2rem;
  border-radius: 15%;
}
.hover-shadow:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 灰色阴影 */
  transition: box-shadow 0.3s ease; /* 添加过渡效果 */
  background-color: #eee;
  color: #222;
}
</style>