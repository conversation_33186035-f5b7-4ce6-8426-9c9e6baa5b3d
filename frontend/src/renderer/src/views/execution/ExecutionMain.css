/* 全局样式优化 */
.test-plan-card,
.test-progress-card,
.test-results-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 300px;
}

.test-progress-card {
  min-width: 25rem;
}
.log-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 340px;
  /* 设置最小高度 */
}

.test-plan-header,
.test-results-header,
.log-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.test-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.status-text {
  margin-left: 1rem;
  font-size: 16px;
  font-weight: bold;
}

.test-results-table {
  width: 100%;
  table-layout: fixed;
}

.test-results-table th,
.test-results-table td {
  padding: 12px;
  text-align: center;
}

.test-results-table th {
  background-color: #fafafa;
  font-weight: bold;
}

.test-results-table tr:nth-child(even) {
  background-color: #fafafa;
}

.test-results-table tr:hover {
  background-color: #f0f5ff;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 14px;
  color: #333;
}

.ant-pagination.ant-pagination-mini.ant-table-pagination.ant-table-pagination-right {
  display: none !important;
}