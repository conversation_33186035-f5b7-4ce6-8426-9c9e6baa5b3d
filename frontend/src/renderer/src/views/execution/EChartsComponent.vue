<template>
    <v-chart class="chart" :option="option" autoresize />
  </template>
  
  <script setup>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
  } from 'echarts/components';
  import VChart, { THEME_KEY } from 'vue-echarts';
  import { ref, watch, computed } from 'vue';
  
  use([
    Canvas<PERSON>enderer,
    LineChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
  ]);

  
  // 定义 props
  const props = defineProps({
    data: {
      type: Array,
      required: true,
    },
  });
  
  // 使用 computed 动态生成 option
  const option = computed(() => ({
    title: {
      text: 'Signal Value Over Time',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: 'Time: {b0}<br />Signal Value: {c0}',
    },
    legend: {
      data: ['Signal Value'],
      left: 'left',
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.time),
      name: 'Time',
    },
    yAxis: {
      type: 'value',
      name: 'Signal Value',
    },
    series: [
      {
        name: 'Signal Value',
        type: 'line',
        data: props.data.map(item => item.signal_value),
        smooth: true,
        emphasis: {
          focus: 'series',
        },
      },
    ],
  }));
  
  </script>
  
  <style scoped>
  .chart {
    height: 100vh;
  }
  </style>