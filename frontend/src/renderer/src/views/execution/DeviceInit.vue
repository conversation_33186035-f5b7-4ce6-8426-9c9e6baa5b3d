<template>
    <div>初始化状态: <a-tag :color="isInit ? 'green' : 'pink'">{{ isInit ? '已初始化' : '未初始化' }}</a-tag></div>
    <a-button type="primary" @click="showDeviceInitModal" style="margin-right: 10px">
        <template #icon>
            <ToolOutlined />
        </template>
        设备初始化</a-button>

    <a-modal v-model:visible="visible" title="设备初始化引导" :footer="null" destroyOnClose width="50vw"
        :afterClose="afterClose" :maskClosable="false">
        <div v-show="step === 1">
            <h2>唤醒词</h2>
            <a-input v-model:value="wakeWord" placeholder="请输入文本" />
            <h2 style="margin-top: 0.5rem;">试音语料</h2>
            <p>输入长响应（请选择一项）</p>
            <a-select v-model:value="longResponse" placeholder="车机长响应（请选择一项）" style="width: 100%">
                <a-select-option v-for="option in longResponseOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                </a-select-option>
            </a-select>
            <p>输入长指令（请选择一项）</p>
            <a-select v-model:value="longCommand" placeholder="输入长指令（请选择一项）" style="width: 100%">
                <a-select-option v-for="option in longCommandOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                </a-select-option>
            </a-select>
            <h2>退出指令</h2>
            <a-input v-model:value="exit_corpus" placeholder="请输入退出指令" />
            <div style="text-align: right; margin-top: 16px;">
                <a-button type="primary" @click="handleNext">下一步</a-button>
            </div>
        </div>

        <div v-show="step === 2">
            <p v-if="status === 'playing'" class="blinking">播音中...</p>
            <a-alert show-icon v-else-if="status === 'failed'" message="有异常" description="初始化失败, 请重新选择语料" type="error" />
            <p v-else>正在初始化...</p>
        </div>

        <div v-show="step === 3">
            <p>车机响应音频:</p>
            <div v-for="(audio, index) in audioUrls" :key="index">
                <audio :src="audio" controls></audio>
            </div>
            <p>车机唤醒区域</p>
            <el-image style="width: 100px; height: 100px" :src="imageUrl" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                :preview-src-list="[imageUrl]" :initial-index="4" fit="cover" />
            <div style="display: flex; justify-content: end;">
                <a-button type="primary" @click="afterClose">重新初始化</a-button>
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, toRef } from 'vue';
import { http } from '@renderer/http';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ToolOutlined } from '@ant-design/icons-vue';
const props = defineProps({
    project_id: {
        type: String,
        required: true,
    },
});

const project_id = toRef(props, 'project_id');

const visible = ref(false);
const wakeWord = ref('');
const longResponse = ref('');
const longCommand = ref('');
const step = ref(1);
const status = ref('');
const audioUrls = ref<string[]>([]);
const imageUrl = ref('');
const exit_corpus = ref('');

const afterClose = () => {
    step.value = 1;
    wakeWord.value = '';
    status.value = '';
    audioUrls.value = [];
    imageUrl.value = '';
}

const longResponseOptions = [
    { value: '如何使用蓝牙连接车辆和手机', label: '如何使用蓝牙连接车辆和手机' },
    { value: '如何启用儿童安全锁', label: '如何启用儿童安全锁' },
    { value: '如何调节后视镜', label: '如何调节后视镜' },
];

const longCommandOptions = [
    { value: '如果车内温度高于30度且湿度大于60%，风速调最大，温度设为20度，同时开启除湿功能', label: '如果车内温度高于30度且湿度大于60%，风速调最大，温度设为20度，同时开启除湿功能' },
    { value: '世界上最早的汽车是谁发明的', label: '世界上最早的汽车是谁发明的' },
    { value: '三十功名尘与土，八千里路云和月，是岳飞哪首词中的内容', label: '三十功名尘与土，八千里路云和月，是岳飞哪首词中的内容' },
];

const isInit = ref(false);

const showDeviceInitModal = async () => {
    try {
        // 检查项目初始化状态
        const response = (await http.post('/test_project/init_get', { project_id: project_id.value })).data;
        if (response.image_url) {
            imageUrl.value = getStaticPictureUrl(response.image_url);
            step.value = 3;
            isInit.value = true;
            ElMessageBox.confirm('设备已初始化，请查看结果', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                callback: () => {
                    visible.value = true;
                },
            });
        } else {
            // 如果 image_url 为空，则显示模态框并停留在第一步
            visible.value = true;
        }
    } catch (error) {
        ElMessage.error('获取项目初始化状态失败');
    }
};

const handleNext = async () => {
    const params = {
        rouse_corpus: wakeWord.value,
        long_response_corpus: longResponse.value,
        long_order_corpus: longCommand.value,
        exit_corpus: exit_corpus.value,
        project_id: project_id.value
    };

    try {
        status.value = 'playing';
        step.value = 2;
        const response = (await http.post('/test_project/init', params));
        if (response.status !== 0) {
            ElMessage.error('初始化失败，请重试');
            status.value = 'failed';
            return;
        }
        audioUrls.value = response.data.audio_urls.map(getStaticAudioUrl);
        imageUrl.value = getStaticPictureUrl(response.data.image_url);
        isInit.value = true;
        step.value = 3;
    } catch (error) {
        ElMessage.error('发送数据失败，请重试');
        status.value = 'failed';
    }
};

const getStaticAudioUrl = (url) => {
  if (process.env.NODE_ENV === 'production') {
    let urlCustomed = localStorage.getItem('serverPort');
    return urlCustomed + 'mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
  } else {
    return 'http://127.0.0.1:8080/mic_static/' + ((url || '').replaceAll('\\', '/').split('mic_audio')?.[1] || '').replace('.pcm', '.wav');
  }
}

const getStaticPictureUrl = (url) => {
    if (process.env.NODE_ENV === 'production') {
        let urlCustomed = localStorage.getItem('serverPort');
        return urlCustomed + 'photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    } else {
        return 'http://127.0.0.1:8080/photo/' + ((url || '').replaceAll('\\', '/').split('photo')?.[1] || '');
    }
}

onMounted(async () => {
    // 默认选中第一项
    longResponse.value = longResponseOptions[0].value;
    longCommand.value = longCommandOptions[0].value;
    const response = (await http.post('/test_project/init_get', { project_id: project_id.value })).data;
    if (response.image_url) {
        isInit.value = true;
    }
});
</script>

<style scoped>
.blinking {
    animation: blinker 3s linear infinite;
    font-size: 24px;
    /* 增加字体大小 */
    font-family: 'Arial', sans-serif;
    /* 使用艺术字体 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    /* 添加文本阴影 */
    padding: 10px;
    /* 增加内边距 */
    background-color: #f0f0f0;
    /* 设置背景颜色 */
    display: inline-block;
    /* 使边框和内边距生效 */
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}
</style>