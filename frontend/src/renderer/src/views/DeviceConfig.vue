<template>
  <div
    style="display: flex; justify-content: space-between; height: 2.5rem; background-color: #f3f3f3; align-items: center;">
    <!-- 面包屑导航 -->
    <a-breadcrumb style="margin: 1rem; margin-bottom: 1.2rem;">
      <a-breadcrumb-item>设备配置</a-breadcrumb-item>
    </a-breadcrumb>
  </div>
  <div style="margin: 2rem;">
    <!-- 上方tab -->
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" tab="数据采集">
        <!-- 下方配置项 -->
        <a-card style="box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); height: auto;">
          <div>
            <strong>抓拍</strong>
            <a-form :model="device_config" layout="inline">
              <a-form-item label="抓拍频率（张/秒）" name="camera_interval" :rules="[{ required: true, message: '请输入抓拍频率' }]">
                <a-input v-model:value="device_config.camera_interval" />
              </a-form-item>
              <a-form-item label="抓拍次数" name="camera_times" :rules="[{ required: true, message: '请输入抓拍次数' }]">
                <a-input v-model:value="device_config.camera_times" />
              </a-form-item>
              <a-form-item label="抓拍等待时间（秒）" name="camera_start_wait"
                :rules="[{ required: true, message: '请输入抓拍开始等待时间' }]">
                <a-input v-model:value="device_config.camera_start_wait" />
              </a-form-item>
              <a-form-item label="结果照片间隔（秒）" name="result_photo_interval"
                :rules="[{ required: true, message: '请输入结果照片间隔' }]">
                <a-input v-model:value="device_config.result_photo_interval" />
              </a-form-item>
              <a-form-item label="结果照片差异率（%）" name="result_photo_diff_rate"
                :rules="[{ required: true, message: '请输入结果照片差异率' }]">
                <a-input v-model:value="device_config.result_photo_diff_rate" />
              </a-form-item>
              <a-form-item label="结果开始等待时间（秒）" name="result_start_wait"
                :rules="[{ required: true, message: '请输入结果开始等待时间' }]">
                <a-input v-model:value="device_config.result_start_wait" />
              </a-form-item>
              <a-form-item label="结果照片抓拍超时时间（秒）" name="result_timeout"
                :rules="[{ required: true, message: '请输入结果照片抓拍超时时间' }]">
                <a-input v-model:value="device_config.result_timeout" />
              </a-form-item>
            </a-form>
          </div>
          <div style="margin-top: 20px;">
            <strong>录像</strong>
            <a-form :model="device_config" layout="inline">
              <a-form-item label="分辨率 宽(px)" name="video_width" :rules="[{ required: true, message: '请输入分辨率宽度' }]">
                <a-input v-model:value="device_config.video_width" />
              </a-form-item>
              <a-form-item label="分辨率 高(px)" name="video_height" :rules="[{ required: true, message: '请输入分辨率高度' }]">
                <a-input v-model:value="device_config.video_height" />
              </a-form-item>
              <a-form-item label="帧率（帧/秒）" name="video_frame_rate" :rules="[{ required: true, message: '请输入帧率' }]">
                <a-input v-model:value="device_config.video_frame_rate" />
              </a-form-item>
              <a-form-item label="录音结束等待时间（秒）" name="record_end_wait_time" :rules="[{ required: true, message: '请输入录音结束等待时间' }]">
                <a-input v-model:value="device_config.record_end_wait_time" />
              </a-form-item>
            </a-form>
          </div>
          <div style="margin-top: 20px;">
            <strong>线程</strong>
            <a-form :model="device_config" layout="inline">
              <a-form-item label="模型判断并发线程数" name="result_judge_thread"
                :rules="[{ required: true, message: '请输入模型判断并发线程数' }]">
                <a-input v-model:value="device_config.result_judge_thread" />
              </a-form-item>
            </a-form>
          </div>
          <a-button type="primary" @click="submit_config" style="margin-top: 20px;">提交配置</a-button>
        </a-card>
      </a-tab-pane>
    </a-tabs>

    <!-- 新增的测试语料和干扰语料输出设备配置 -->
    <a-card style="box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); margin-top: 2rem;">
      <div>
        <strong>测试语料输出设备配置</strong>
        <a-form :model="test_corpus_config" layout="inline">
          <a-form-item label="输出设备" name="test_corpus_device">
            <a-select v-model:value="test_corpus_config.id" style="width: 400px;"
              :disabled="test_corpus_config.is_default">
              <a-select-option v-for="device in device_list" :key="device.id" :value="device.id">
                <a-tooltip>
                  <template #title>
                    {{ device.name }} ({{ device.api_name }}) -- 采样率:{{ device.samplerate }}
                  </template>
                  {{ device.name }} ({{ device.api_name }}) -- 采样率:{{ device.samplerate }}
                </a-tooltip>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="音量增益" name="test_corpus_volume">
            <a-input-number v-model:value="test_corpus_config.volume" :min="0" :max="30" :step="0.1" />
          </a-form-item>
          <a-form-item label="使用默认设备" name="test_corpus_is_default">
            <a-switch v-model:checked="test_corpus_config.is_default" />
          </a-form-item>
          <a-button type="primary" @click="set_test_corpus_device">确定</a-button>
        </a-form>
      </div>
      <div style="margin-top: 20px;">
        <strong>干扰语料输出设备配置</strong>
        <a-form :model="disturb_corpus_config" layout="inline">
          <a-form-item label="输出设备" name="disturb_corpus_device">
            <a-select v-model:value="disturb_corpus_config.id" style="width: 400px;"
              :disabled="disturb_corpus_config.is_default">
              <a-select-option v-for="device in device_list" :key="device.id" :value="device.id">
                <a-tooltip>
                  <template #title>
                    {{ device.name }} ({{ device.api_name }}) -- 采样率:{{ device.samplerate }}
                  </template>
                  {{ device.name }} ({{ device.api_name }}) -- 采样率:{{ device.samplerate }}
                </a-tooltip>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="音量增益" name="disturb_corpus_volume">
            <a-input-number v-model:value="disturb_corpus_config.volume" :min="0" :max="30" :step="0.1" />
          </a-form-item>
          <a-form-item label="使用默认设备" name="disturb_corpus_is_default">
            <a-switch v-model:checked="disturb_corpus_config.is_default" />
          </a-form-item>
          <a-button type="primary" @click="set_disturb_corpus_device">确定</a-button>
        </a-form>
      </div>

      <!-- 在 "数据采集" tab 面板中，线程配置的下方添加 -->
      <div style="margin-top: 20px;">
        <strong>车机视频输入设备配置</strong>
        <a-form :model="video_device_config" layout="inline">
          <a-form-item label="车机视频设备">
            <a-select v-model:value="video_device_config.id" style="width: 400px;" placeholder="请选择车机视频设备">
              <a-select-option v-for="device in video_device_list" :key="device.id" :value="device.id">
                {{ device.name }} (ID: {{ device.id }})
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-button type="primary" @click="set_video_device">确定</a-button>
        </a-form>
      </div>

      <!-- 舱内视频设备配置 -->
      <div style="margin-top: 20px;">
        <strong>座舱视频输入设备配置</strong>
        <a-form :model="cabin_video_device_config" layout="inline">
          <a-form-item label="座舱视频设备">
            <a-select v-model:value="cabin_video_device_config.id" style="width: 400px;" placeholder="请选择座舱视频设备">
              <a-select-option v-for="device in video_device_list" :key="device.id" :value="device.id">
                {{ device.name }} (ID: {{ device.id }})
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-button type="primary" @click="set_cabin_video_device">确定</a-button>
        </a-form>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { http } from '@renderer/http';

// 设备配置数据
const device_config = ref({
  camera_interval: '',
  camera_times: '',
  camera_start_wait: '',
  result_judge_thread: '',
  result_photo_interval: '',
  result_photo_diff_rate: '',
  result_start_wait: '',
  result_timeout: '',
  video_width: '',
  video_height: '',
  video_frame_rate: '',
  record_end_wait_time: '',
});

// 测试语料输出设备配置
const test_corpus_config = ref({
  id: null,
  volume: 1.0,
  is_default: false,
  samplerate: 0
});

// 干扰语料输出设备配置
const disturb_corpus_config = ref({
  id: null,
  volume: 1.0,
  is_default: false,
  samplerate: 0
});

// 视频设备配置数据
const video_device_config = ref({
  id: null,
  name: ''
});

// 舱内视频设备配置数据
const cabin_video_device_config = ref({
  id: null,
  name: ''
});

// 视频设备列表（从 /get_video_list 接口获取）
const video_device_list = ref([]);

// 获取视频设备列表
const get_video_device_list = async () => {
  try {
    const response = (await http.post('/device/get_video_list')).data;
    video_device_list.value = response.list || [];
  } catch (error) {
    message.error('获取视频设备列表失败');
    console.error(error);
  }
};

// 获取当前选中的视频设备
const get_current_video_device = async () => {
  try {
    const response = (await http.post('/device/get_video_device')).data;
    video_device_config.value = {
      id: response.id,
      name: response.name
    };
  } catch (error) {
    message.error('获取当前视频设备失败');
    console.error(error);
  }
};

// 设置视频设备
const set_video_device = async () => {
  try {
    const response = await http.post('/device/set_video_device', {
      id: video_device_config.value.id,
      name: video_device_list.value.find(d => d.id === video_device_config.value.id)?.name || ''
    });
    if (response.status === 0) {
      message.success('视频设备配置已保存');
    }
  } catch (error) {
    message.error('保存视频设备配置失败');
    console.error(error);
  }
};

// 获取当前选中的舱内视频设备
const get_current_cabin_video_device = async () => {
  try {
    const response = (await http.post('/device/get_cabin_video_device')).data;
    cabin_video_device_config.value = {
      id: response.id,
      name: response.name
    };
  } catch (error) {
    message.error('获取当前舱内视频设备失败');
    console.error(error);
  }
};

// 设置舱内视频设备
const set_cabin_video_device = async () => {
  try {
    const response = await http.post('/device/set_cabin_video_device', {
      id: cabin_video_device_config.value.id,
      name: video_device_list.value.find(d => d.id === cabin_video_device_config.value.id)?.name || ''
    });
    if (response.status === 0) {
      message.success('舱内视频设备配置已保存');
    }
  } catch (error) {
    message.error('保存舱内视频设备配置失败');
    console.error(error);
  }
};

// 在 onMounted 中调用初始化
onMounted(() => {
  get_video_device_list();
  get_current_video_device();
  get_current_cabin_video_device();
  // 保留原有的初始化调用...
});

// 设备列表
const device_list = ref([]);

// 获取设备配置
const get_device_config = async () => {
  try {
    const response = (await http.post('/device/config_get')).data;
    const data = response;
    device_config.value = {
      camera_interval: Number(data.camera_interval),
      camera_times: Number(data.camera_times),
      camera_start_wait: Number(data.camera_start_wait),
      result_judge_thread: Number(data.result_judge_thread),
      result_photo_interval: Number(data.result_photo_interval),
      result_photo_diff_rate: Number(data.result_photo_diff_rate) * 100,
      result_start_wait: Number(data.result_start_wait),
      result_timeout: Number(data.result_timeout || 30),
      video_width: Number(data.video_width),
      video_height: Number(data.video_height),
      video_frame_rate: Number(data.video_frame_rate || 30),
      record_end_wait_time: Number(data.record_end_wait_time || 0),
    };
  } catch (error) {
    message.error('获取设备配置失败');
    console.error(error);
  }
};

// 获取设备列表
const get_device_list = async () => {
  try {
    const response = (await http.post('/device/get_device_list')).data;
    device_list.value = response.list;
  } catch (error) {
    message.error('获取设备列表失败');
    console.error(error);
  }
};

// 设置测试语料输出设备
const set_test_corpus_device = async () => {
  try {
    const response = (await http.post('/device/set_corpus_device', {
      corpus: 'test_corpus',
      id: test_corpus_config.value.id,
      volume: test_corpus_config.value.volume,
      is_default: test_corpus_config.value.is_default,
    }));
    if (response.status === 0) {
      message.success('测试语料输出设备配置成功');
    }
  } catch (error) {
    message.error('测试语料输出设备配置失败');
    console.error(error);
  }
};

// 设置干扰语料输出设备
const set_disturb_corpus_device = async () => {
  try {
    const response = (await http.post('/device/set_corpus_device', {
      corpus: 'disturb_corpus',
      id: disturb_corpus_config.value.id,
      volume: disturb_corpus_config.value.volume,
      is_default: disturb_corpus_config.value.is_default,
    }));
    if (response.status === 0) {
      message.success('干扰语料输出设备配置成功');
    }
  } catch (error) {
    message.error('干扰语料输出设备配置失败');
    console.error(error);
  }
};

// 提交配置
const submit_config = async () => {
  try {
    const response = (await http.post('/device/config_set', {
      camera_interval: Number(device_config.value.camera_interval),
      camera_times: Number(device_config.value.camera_times),
      camera_start_wait: Number(device_config.value.camera_start_wait),
      result_judge_thread: Number(device_config.value.result_judge_thread),
      result_photo_interval: Number(device_config.value.result_photo_interval),
      result_photo_diff_rate: Number(device_config.value.result_photo_diff_rate) / 100,
      result_start_wait: Number(device_config.value.result_start_wait),
      result_timeout: Number(device_config.value.result_timeout),
      video_width: Number(device_config.value.video_width),
      video_height: Number(device_config.value.video_height),
      video_frame_rate: Number(device_config.value.video_frame_rate),
      record_end_wait_time: Number(device_config.value.record_end_wait_time),
    }));
    if (response.status === 0) {
      message.success('配置提交成功');
    }
  } catch (error) {
    message.error('配置提交失败');
    console.error(error);
  }
};

// 获取语料输出设备配置
const get_corpus_device_config = async () => {
  try {
    const response = (await http.post('/device/get_corpus_device')).data;
    const data = response;
    test_corpus_config.value = {
      id: data.test_corpus.id,
      volume: data.test_corpus.volume,
      is_default: data.test_corpus.is_default,
      samplerate: data.test_corpus.samplerate
    };
    disturb_corpus_config.value = {
      id: data.disturb_corpus.id,
      volume: data.disturb_corpus.volume,
      is_default: data.disturb_corpus.is_default,
      samplerate: data.disturb_corpus.samplerate
    };
  } catch (error) {
    message.error('获取语料输出设备配置失败');
    console.error(error);
  }
};

// 组件挂载后调用获取设备配置、设备列表和语料输出设备配置
onMounted(() => {
  get_device_config();
  get_device_list();
  get_corpus_device_config();
});
</script>

<style scoped>
/* 自定义样式 */
.ant-card {
  margin-top: 10px;
}

.ant-form-item {
  margin-bottom: 10px;
}
</style>