<template>
  <div class="log-view-container">
    <div class="search-bar-container">
      <div class="search-bar">
        <el-input
          v-model="searchParams.keyword"
          placeholder="输入关键字搜索日志"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
      <div class="controls-container">
        <div class="sort-control">
          <span class="control-label">排序：</span>
          <el-radio-group v-model="searchParams.order" size="small" @change="handleSearch">
            <el-radio-button label="asc">正序</el-radio-button>
            <el-radio-button label="desc">倒序</el-radio-button>
          </el-radio-group>
        </div>

        <div class="auto-refresh-container">
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
            @change="handleAutoRefreshChange"
          />
          <span v-if="autoRefresh" class="refresh-interval">
            刷新间隔：
            <el-select v-model="refreshInterval" size="small" @change="handleIntervalChange">
              <el-option :value="5" label="5秒" />
              <el-option :value="10" label="10秒" />
              <el-option :value="30" label="30秒" />
              <el-option :value="60" label="1分钟" />
            </el-select>
          </span>
        </div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="logData"
      style="width: 100%"
      border
      stripe
      height="calc(100vh - 200px)"
    >
      <el-table-column prop="timestamp" label="时间" width="180">
        <template #header>
          <div class="timestamp-header" @click="toggleSortOrder">
            时间
            <el-icon v-if="searchParams.order === 'desc'" class="sort-icon"><ArrowDown /></el-icon>
            <el-icon v-else class="sort-icon"><ArrowUp /></el-icon>
          </div>
        </template>
        <template #default="scope">
          {{ formatTimestamp(scope.row.timestamp) }}
        </template>
      </el-table-column>
      <el-table-column label="日志级别" width="100">
        <template #default="scope">
          <el-tag
            :type="getLogLevelType(scope.row.content)"
            size="small"
          >
            {{ getLogLevel(scope.row.content) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="日志内容">
        <template #default="scope">
          <pre class="log-content">{{ scope.row.content }}</pre>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="searchParams.page"
        v-model:page-size="searchParams.page_size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { http } from '@renderer/http';
import { ElMessage } from 'element-plus';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

// 定义日志数据类型
interface LogEntry {
  timestamp: string;
  content: string;
}

// 搜索参数
const searchParams = reactive({
  keyword: '',
  order: 'desc',
  page: 1,
  page_size: 20
});

// 数据和加载状态
const logData = ref<LogEntry[]>([]);
const total = ref(0);
const loading = ref(false);

// 自动刷新相关
const autoRefresh = ref(false);
const refreshInterval = ref(10); // 默认10秒
let refreshTimer: number | null = null;

// 格式化时间戳
const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return '';
  try {
    // 处理ISO格式的时间字符串
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      // 如果无法解析为日期，尝试从日志内容中提取时间
      return timestamp;
    }

    // 格式化为本地日期时间字符串
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    // 如果解析失败，直接返回原始字符串
    return timestamp;
  }
};

// 获取日志数据
const fetchLogData = async () => {
  loading.value = true;
  try {
    const response = await http.post('/log/list', searchParams);
    if (response.status === 0) {
      logData.value = response.data.list;
      total.value = response.data.total;
    } else {
      ElMessage.error('获取日志数据失败');
    }
  } catch (error) {
    console.error('获取日志数据出错:', error);
    ElMessage.error('获取日志数据出错');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.page = 1;
  fetchLogData();
};

// 切换排序顺序
const toggleSortOrder = () => {
  searchParams.order = searchParams.order === 'desc' ? 'asc' : 'desc';
  searchParams.page = 1; // 切换排序时重置到第一页
  fetchLogData();
};

// 分页大小变化
const handleSizeChange = (newSize: number) => {
  searchParams.page_size = newSize;
  fetchLogData();
};

// 页码变化
const handleCurrentChange = (newPage: number) => {
  searchParams.page = newPage;
  fetchLogData();
};

// 从日志内容中提取日志级别
const getLogLevel = (content: string): string => {
  if (!content) return 'INFO';

  // 尝试匹配日志级别
  const levelMatch = content.match(/- (DEBUG|INFO|WARNING|ERROR|FATAL|CRITICAL|TRACE) -/i);
  if (levelMatch && levelMatch[1]) {
    return levelMatch[1].toUpperCase();
  }

  return 'INFO'; // 默认级别
};

// 根据日志级别返回对应的标签类型
const getLogLevelType = (content: string): string => {
  const level = getLogLevel(content);

  switch (level) {
    case 'ERROR':
    case 'FATAL':
    case 'CRITICAL':
      return 'danger';
    case 'WARNING':
    case 'WARN':
      return 'warning';
    case 'DEBUG':
      return 'info';
    case 'TRACE':
      return 'info';
    default:
      return 'success'; // INFO
  }
};

// 处理自动刷新开关变化
const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 处理刷新间隔变化
const handleIntervalChange = () => {
  if (autoRefresh.value) {
    // 重新启动定时器，使用新的间隔时间
    stopAutoRefresh();
    startAutoRefresh();
  }
};

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh(); // 先清除可能存在的定时器

  // 设置新的定时器，间隔时间为用户选择的秒数
  refreshTimer = window.setInterval(() => {
    fetchLogData();
  }, refreshInterval.value * 1000);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer !== null) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchLogData();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.log-view-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 20px;
}

.search-bar {
  flex: 1;
  max-width: 500px;
}

.controls-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sort-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-label {
  font-size: 14px;
  color: #606266;
}

.auto-refresh-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-interval {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.log-content {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 3px solid #ddd;
}

.timestamp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.timestamp-header:hover {
  background-color: #f0f9ff;
}

.sort-icon {
  margin-left: 5px;
  color: #409EFF;
}
</style>
