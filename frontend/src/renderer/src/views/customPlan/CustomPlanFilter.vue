<template>
    <div style="margin-bottom: 1rem;">
      <a-select v-model:value="filterItem.speaker" placeholder="发声人" style="width: 120px; margin-right: 1rem;" @change="handleSpeakerChange" allowClear>
      <a-select-option value="男声">男声</a-select-option>
      <a-select-option value="女声">女声</a-select-option>
    </a-select>
    <a-select v-model:value="filterItem.language" placeholder="语种" style="width: 120px; margin-right: 1rem;" allowClear>
      <a-select-option v-for="option in languageOptions" :key="option.value" :value="option.value">
        {{ option.label }}
      </a-select-option>
    </a-select>
    <a-input v-model:value="filterItem.expect_result" style="width: 140px;" allowClear placeholder="请输入预期结果">

    </a-input>
    </div>

  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, toRef } from 'vue';
  
  const props = defineProps({
    filterItem: {
      type: Object,
    },
  });
  
  const languageOptions = ref([]);
  
  const handleSpeakerChange = (value) => {
    languageOptions.value = getLanguageOptions(value);
    if (languageOptions.value.length > 0) {
      // 使用响应式引用更新 filterItem.language
      props.filterItem.language = languageOptions.value[0].value;
    }
  };
  
  const getLanguageOptions = (speaker) => {
    if (speaker === '男声') {
      return [
        { value: '男声1', label: '男声1' },
        { value: '男声2', label: '男声2' },
        { value: '男声3', label: '男声3' },
        { value: '男声4', label: '男声4' },
        { value: '男声5', label: '男声5' },
        { value: '男声6', label: '男声6' },
        { value: '童声', label: '童声' },
        { value: '东北话', label: '东北话' },
        { value: '天津话', label: '天津话' },
      ];
    } else if (speaker === '女声') {
      return [
        { value: '女声1', label: '女声1' },
        { value: '女声2', label: '女声2' },
        { value: '女声3', label: '女声3' },
        { value: '女声4', label: '女声4' },
        { value: '女声5', label: '女声5' },
        { value: '女声6', label: '女声6' },
        { value: '女声7', label: '女声7' },
        { value: '童声', label: '童声' },
        { value: '四川话', label: '四川话' },
        { value: '粤语', label: '粤语' },
        { value: '东北话', label: '东北话' },
      ];
    } else {
      return [];
    }
  };

  </script>
  
  <style scoped>
  </style>