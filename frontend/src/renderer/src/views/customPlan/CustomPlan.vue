<template>
  <a-spin :spinning="mainLoading">
    <!-- 面包屑导航 -->
    <div
      style="display: flex; justify-content: space-between; height: 2.5rem; background-color: #f3f3f3; align-items: center; margin-bottom: 0.75rem;">
      <a-breadcrumb style="margin: 1rem;">
        <a-breadcrumb-item>
          <a @click="goToProject">测试项目</a>
        </a-breadcrumb-item>
        <a-breadcrumb-item>方案配置</a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <div style="display: flex; height: calc(100vh - 9rem);">
      <!-- 测评方案 -->
      <div style="flex: 0.75; padding: 5px; height: 100%;">
        <TestPlan :project_id="projectDetail.project_id" :selectedKeys="selectedKeys" @planChange="handlePlanChange" />
      </div>

      <!-- 选择播放配置 -->
      <div style="flex: 0.75; padding: 5px; height: 100%;" v-show="selectedPlanId">
        <PlaybackConfig v-model="selectedPlayConfigId" :project_id="projectDetail.project_id"
          @rouseSceneSelected="onRouseSceneSelected" />
      </div>

      <!-- 选择语料和方案内容 -->
      <div style="flex: 2; padding: 5px; height: 100%;" v-show="selectedPlanId">
        <a-card style="height: 100%; overflow: auto;">
          <template #title>
            <ScheduleFilled style="margin-right: 0.4rem;" />
            方案内容
          </template>
          <!-- 选择语料 -->
          <div>
            <a-button type="primary" @click="showCorpusModal" style="margin-bottom: 20px;">配置语料</a-button>
          </div>

          <!-- 方案内容 -->
          <div style="margin-top: 40px;">
            <h3>唤醒语料</h3>
            <a-table :columns="rouseCorpusColumns" :data-source="selectedRouseCorpus || []" row-key="corpus_id"
              :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'audio_url'">
                  {{ record?.audio_url }}
                </template>
              </template>
            </a-table>

            <h3 style="margin-top: 20px;" v-if="!isRouseRef">测试语料</h3>
            <div class="flex flex-end" v-if="!isRouseRef">
              <a-button type="primary" @click="reverseTestCorpusList"
                style="margin-bottom: 20px; margin-left: 10px;">反转语料顺序</a-button>
            </div>

            <a-table :columns="columns" :data-source="selectedTestCorpusList || []" row-key="corpus_id"
              :pagination="{ pageSize: 5 }" v-if="!isRouseRef">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'audio_url'">
                  {{ record?.audio_url }}
                </template>
              </template>
              <template #expandedRowRender="{ record }" :rowExpandable="record?.type === 'multi'">
                <a-table v-if="record.type === 'multi'" :columns="subColumns" :data-source="record?.corpusItems || []"
                  row-key="aud_id" :pagination="false">
                </a-table>
              </template>
            </a-table>
            <h3>干扰语料</h3>
            <a-table :columns="disturbCorpusColumns" :data-source="selectedDisturbCorpusList || []" row-key="corpus_id"
              :pagination="false">
            </a-table>
            <h3>背景噪声</h3>
            <a-table :columns="noiseCorpusColumns" :data-source="selectedNoiseCorpusList || []" row-key="corpus_id"
              :pagination="false">
            </a-table>
          </div>
        </a-card>
      </div>

      <!-- 配置语料弹窗 -->
      <a-modal title="配置语料" :visible="corpusModalVisible" @ok="handleCorpusModalOk" @cancel="handleCorpusModalCancel"
        okText="确定" cancelText="取消" width="70%">
        <a-tabs default-active-key="1" @change="handleLoading">
          <a-tab-pane key="1" tab="唤醒语料">
            <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
            <a-spin :spinning="loading">
              <a-transfer show-search :filter-option="filterOption" :locale="{
                searchPlaceholder: '请输入 文本/标签/发声人/语种'
              }" :dataSource="filteredRouseCorpusList" :list-style="{
                width: '50%',
                height: '100%',
                'max-height': '60vh',
                'overflow': 'auto'
              }" :titles="['可用语料', '已选语料']" :targetKeys="rouseCorpusKey || []" :render="renderItem"
                :pagination="{ pageSize: 100 }" @change="handleRouseTransferChange" />
            </a-spin>
          </a-tab-pane>
          <a-tab-pane key="2" tab="测试语料" v-if="!isRouseRef">
            <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
            <a-spin :spinning="loading">
              <a-transfer show-search :filter-option="filterOption" :locale="{
                searchPlaceholder: '请输入 文本/标签/发声人/语种'
              }" :dataSource="filteredTestCorpusList" :list-style="{
                width: '50%',
                height: '100%',
                'max-height': '60vh',
                'overflow': 'auto'
              }" :titles="['可用语料', '已选语料']" :targetKeys="testCorpusKeys" :render="renderItem"
                :pagination="{ pageSize: 100 }" @change="handleTransferChange" showSelectAll />
            </a-spin>
          </a-tab-pane>
          <a-tab-pane key="3" tab="干扰语料">
            <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
            <a-spin :spinning="loading">
              <a-transfer show-search :filter-option="filterOption" :locale="{
                searchPlaceholder: '请输入 文本/标签/发声人/语种'
              }" :dataSource="filteredDisturbCorpusList" :list-style="{
                width: '50%',
                height: '100%',
                'max-height': '60vh',
                'overflow': 'auto'
              }" :titles="['可用语料', '已选语料']" :targetKeys="disturbCorpusKeys || []"
                :render="item => `${item.text} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`"
                :pagination="{ pageSize: 100 }" @change="handleDisturbTransferChange" />
            </a-spin>
          </a-tab-pane>
          <a-tab-pane key="4" tab="背景噪声">
            <CustomPlanFilter :filterItem="filterItem"></CustomPlanFilter>
            <a-spin :spinning="loading">
              <a-transfer show-search :filter-option="filterOption" :locale="{
                searchPlaceholder: '请输入 文本/标签/发声人/语种'
              }" :dataSource="filteredNoiseCorpusList" :list-style="{
                width: '50%',
                height: '100%',
                'max-height': '60vh',
                'overflow': 'auto'
              }" :titles="['可用语料', '已选语料']" :targetKeys="noiseCorpusKeys || []"
                :render="item => `${item.text} - 时长: ${item.audio_duration || 0}s `"
                :pagination="{ pageSize: 100 }" @change="handleNoiseTransferChange" />
            </a-spin>
          </a-tab-pane>
        </a-tabs>
      </a-modal>

      <!-- 固定底部栏 -->
      <div
        style="position: fixed; bottom: 0; left: 0; width: 100%; background-color: white; padding: 10px; border-top: 1px solid #e8e8e8; display: flex; justify-content: flex-end;"
        v-show="selectedPlanId">
        <a-button type="primary" @click="handleReturn">返回</a-button>
        <a-button type="primary" @click="handleSave" style="margin-left: 10px;">保存</a-button>
        <a-button type="primary" @click="handleSaveAndExecute" style="margin-left: 10px;">保存并执行</a-button>
      </div>
    </div>
  </a-spin>
</template>

<script setup lang="tsx">
import { ref, onMounted, watch, computed, reactive } from 'vue';
import { http } from '@renderer/http';
import { useProjectStore } from '@renderer/stores/useProject';
import { storeToRefs } from 'pinia';
import TestPlan from './TestPlan.vue'; // 引入 TestPlan 组件
import PlaybackConfig from './PlaybackConfig.vue'; // 引入 PlaybackConfig 组件
import { ElMessage, ElMessageBox } from 'element-plus'; // 引入 ElMessageBox
import { useRouter } from 'vue-router'; // 引入 useRouter
import CustomPlanFilter from './CustomPlanFilter.vue';
import { ScheduleFilled } from '@ant-design/icons-vue';

const router = useRouter(); // 初始化 router
const loading = ref(false);
const mainLoading = ref(false);

const handleLoading = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 340)
}

const filterItem = reactive({
  test_type: undefined,
  test_scenario: undefined,
  language: undefined,
  evaluation_metric: undefined,
  speaker: undefined,
  car_function: undefined,
  expect_result: undefined,
});

const female_voice_map = {
  '1': '女声1',
  '2': '女声2',
  '3': '女声3',
  '4': '女声4',
  '5': '女声5',
  '6': '女声6',
  '7': '女声7',
  '8': '童声',
  '9': '四川话',
  '10': '粤语',
  '11': '东北话',
};

const renderItem = (item) => {
  return (
    <div style={{ width: '100%' }}>
      <div style={{ width: '100%' }} title={`${item.text || item.corpus_name || item.corpus_id} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`}>
        {/* 普通语料/多轮对话语料标签 */}
        <div style={{
          border: '1px solid #4CAF50',
          display: 'inline-block',
          fontSize: '12px',
          borderRadius: '12px',
          padding: '4px 8px',
          marginRight: '0.3rem',
          backgroundColor: '#E8F5E9',
          color: '#2E7D32',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          fontWeight: '500'
        }}>
          {item.type === 'multi' ? '多轮对话语料' : '普通语料'}
        </div>
        {`${item.text || item.corpus_name || item.corpus_id} - 时长: ${item.audio_duration || 0}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`}
        <pre hidden={item.type !== 'multi'}>
          {(item.corpusItems || []).map(x => x.text).join(', ')}
        </pre>
      </div>
      <div hidden={item.type === 'multi'} style={{ marginTop: '4px', display: 'flex', alignItems: 'center', width: '100%' }} title={item.expect_result}>
        {/* 预期结果标签 */}
        <div style={{
          border: '1px solid #2196F3',
          display: 'inline-flex',
          fontSize: '12px',
          borderRadius: '12px',
          padding: '4px 8px',
          marginRight: '0.3rem',
          backgroundColor: '#E3F2FD',
          color: '#1565C0',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          fontWeight: '500',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          预期结果
        </div>
        <pre style="display: inline-block">
          {item.expect_result}
        </pre>

      </div>
    </div>
  );
};

const male_voice_map = {
  '1': '男声1',
  '2': '男声2',
  '3': '男声3',
  '4': '男声4',
  '5': '男声5',
  '6': '男声6',
  '7': '童声',
  '8': '东北话',
  '9': '天津话',
};

const projectStore = useProjectStore();
const { projectDetail } = storeToRefs(projectStore);

const selectedKeys = ref<string[]>([]);
const selectedPlanId = ref<string | null>('');
const selectedPlayConfigId = ref<string | null>(null); // 新增的 selectedPlayConfigId

const treeData = ref([
  {
    title: '测试语料',
    key: 'test-corpus',
    children: [],
  },
  {
    title: '唤醒语料',
    key: 'rouse-corpus',
    children: [],
  },
]);

const checkedKeys = ref([[], []]);
const rouseCorpusKey = ref([]);
const testCorpusKeys = ref([]);
const noiseCorpusKeys = ref([]);

const testCorpusList = ref([]);
const rouseCorpusList = ref([]);
const noiseCorpusList = ref([]);

const selectedRouseCorpus = ref([]);
const selectedTestCorpusList = ref([]);
const isRouseRef = ref(true);
const currentScene = ref('rouse');

// 新增变量
const disturbCorpusList = ref([]);
const disturbCorpusKeys = ref([]);
const selectedDisturbCorpusList = ref([]);
const selectedNoiseCorpusList = ref([])

const filterOption = (inputValue: string, option: any) => {
  if (option.corpusItems && option.corpusItems.some(x => x.text.includes(inputValue))) {
    return true;
  }
  return (option.label || '').indexOf(inputValue) > -1 || (option.text || '').indexOf(inputValue) > -1 || option.language.indexOf(inputValue) > -1 || option.speaker?.indexOf(inputValue) > -1;
};

// 计算属性：过滤后的唤醒语料列表
const filteredRouseCorpusList = computed(() => {
  return rouseCorpusList.value.filter(item => {
    return (
      (!filterItem.language || item.language === filterItem.language) &&
      (!filterItem.speaker || item.speaker === filterItem.speaker) &&
      (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
    );
  });
});

// 计算属性：过滤后的测试语料列表
const filteredTestCorpusList = computed(() => {
  return testCorpusList.value.filter(item => {
    return (
      (!filterItem.language || item.language === filterItem.language) &&
      (!filterItem.speaker || item.speaker === filterItem.speaker) &&
      (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
    );
  });
});

// 计算属性：过滤后的干扰语料列表
const filteredDisturbCorpusList = computed(() => {
  return disturbCorpusList.value.filter(item => {
    return (
      (!filterItem.language || item.language === filterItem.language) &&
      (!filterItem.speaker || item.speaker === filterItem.speaker) &&
      (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
    );
  });
});

const filteredNoiseCorpusList = computed(() => {
  return noiseCorpusList.value.filter(item => {
    return (
      (!filterItem.language || item.language === filterItem.language) &&
      (!filterItem.speaker || item.speaker === filterItem.speaker) &&
      (!filterItem.expect_result || item.expect_result?.includes(filterItem.expect_result))
    );
  });
});

const getIndexOptions = (length) => {
  return Array.from({ length }, (_, i) => ({ value: i + 1, label: i + 1 }));
};

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ text, record, index }) => (
      <a-select
        value={record.index}
        onChange={(value) => handleIndexChange(record, value)}
        style="width: 100px"
        options={getIndexOptions(selectedTestCorpusList.value.length)}
      >
      </a-select>
    ),
  },
  { title: '语料id', dataIndex: 'corpus_id' },
  { title: '语料文本', dataIndex: 'text' },
  { title: '音频时长(s)', dataIndex: 'audio_duration' },
];

const subColumns = [
  { title: '语料文本', dataIndex: 'text', key: 'text' },
  { title: '音频时长', dataIndex: 'audio_duration', key: 'audio_duration' },
  { title: '预期结果', dataIndex: 'expect_result', key: 'expect_result' },
  { title: '操作', key: 'action' }
];

const isMounted = ref(false);

const corpusModalVisible = ref(false); // 控制配置语料弹窗显示隐藏

const rouseCorpusColumns = [
  { title: '语料id', dataIndex: 'corpus_id' },
  { title: '语料文本', dataIndex: 'text' },
  { title: '音频时长(s)', dataIndex: 'audio_duration' },
];

const noiseCorpusColumns = [
  { title: '语料id', dataIndex: 'corpus_id' },
  { title: '语料文本', dataIndex: 'text' },
  { title: '音频时长(s)', dataIndex: 'audio_duration' },
];


const rouseCorpusData = computed(() => {
  if (Array.isArray(selectedRouseCorpus.value)) {
    return selectedRouseCorpus.value;
  } else if (selectedRouseCorpus.value) {
    return [selectedRouseCorpus.value];
  } else {
    return [];
  }
});

onMounted(async () => {
  if (projectDetail.value.project_id) {
    // 从这里查询接口获取treeData的初始值
    mainLoading.value = true;
    await fetchTreeData();
    await getPlanDetail(selectedPlanId.value);
    mainLoading.value = false;
    isMounted.value = true;
  }
});

const fetchTreeData = async () => {
  // 获取测试语料列表
  testCorpusList.value = (await http.post('/corpus/get_test_corpus_list', { is_multi: false })).data.list;

  // 获取唤醒语料列表
  rouseCorpusList.value = (await http.post('/corpus/get_rouse_corpus_list', {})).data.list;

  disturbCorpusList.value = (await http.post('/corpus/get_disturb_corpus_list', {})).data.list;

  noiseCorpusList.value = (await http.post('/corpus/get_background_noise_list', {})).data.list;

  // 获取多轮语料列表
  let multiCorpusList = (await http.post('/multi-corpus/list', { label: "" })).data.list;

  multiCorpusList = multiCorpusList.map(x => {
    x.type = "multi";
    return x;
  })
  // 将多轮语料列表拼接到 testCorpusList 中
  testCorpusList.value = testCorpusList.value.concat(multiCorpusList);

  // 处理测试语料列表
  treeData.value[0].children = testCorpusList.value.map(item => {
    item.key = `test-corpus-${item.corpus_id}`;
    if (typeof item.speaker === 'string') {
      item.speaker = item.speaker === 'male' ? '男声' : '女声';
      item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
    }
    return {
      title: item.text,
      key: `test-corpus-${item.corpus_id}`,
    }
  });

  // 处理唤醒语料列表
  treeData.value[1].children = rouseCorpusList.value.map(item => {
    if (typeof item.speaker === 'string') {
      item.speaker = item.speaker === 'male' ? '男声' : '女声';
      item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
    }
    item.key = `rouse-corpus-${item.corpus_id}`;
    item.label = `${item.text || item.corpus_id} - 时长: ${item.audio_duration || '0'}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`
    return {
      title: item.text,
      key: `rouse-corpus-${item.corpus_id}`,
      label: item.label,
    }
  });

  noiseCorpusList.value.map(item => {
    if (typeof item.speaker === 'string') {
      item.speaker = item.speaker === 'male' ? '男声' : '女声';
      item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
    }
    item.key = `noise-corpus-${item.corpus_id}`;
    item.label = `${item.text || item.corpus_id} - 时长: ${item.audio_duration || '0'}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`
    return {
      title: item.text,
      key: `noise-corpus-${item.corpus_id}`,
      label: item.label,
    }
  });

  disturbCorpusList.value.map(item => {
    item.key = `disturb-corpus-${item.corpus_id}`;
    item.label = `${item.text || item.corpus_id} - 时长: ${item.audio_duration || '0'}s - 发声人: ${item.speaker} - 语种: ${item.language} - 标签: ${item.label}`;
    if (typeof item.speaker === 'string') {
      item.speaker = item.speaker === 'male' ? '男声' : '女声';
      item.language = item.speaker === '男声' ? male_voice_map[item.language] || '- -' : female_voice_map[item.language] || '- -';
    }
    return {
      title: item.text,
      key: `disturb-corpus-${item.corpus_id}`,
      label: item.label,
    }
  })
};

const handlePlanChange = async (planId: string) => {
  selectedPlanId.value = planId;
  if (isMounted.value === true) {
    // 调用 getPlanDetail 方法
    await getPlanDetail(planId);
  } else {
    // donoting
  }
};

const getPlanDetail = async (planId: string) => {
  const payload = {
    project_id: projectDetail.value.project_id,
    plan_id: planId,
  };
  const result = (await http.post('/test_project/get_plan_detail', payload)).data;
  if (!result.rouseCorpusList) {
    result.rouseCorpusList = [];
  }
  const response = (await http.post('/play_config/get_play_config_list', {})).data.list ;

  const rouseConfigs = response.filter(config => config.type === 'rouse');
  const interactionConfigs = response.filter(config => config.type === 'interaction');

  testCorpusKeys.value = [...(result.testCorpusList || []).map(id => `test-corpus-${id}`)];
  console.log(testCorpusKeys);

  disturbCorpusKeys.value = [...(result.disturbCorpusList || []).map(id => `disturb-corpus-${id}`)];

  noiseCorpusKeys.value = [...(result.backgroundNoiseList || []).map(id => `noise-corpus-${id}`)];

  selectedTestCorpusList.value = (testCorpusList.value || [])
    .filter(item => (result?.testCorpusList || []).includes(item.corpus_id))
    .sort((a, b) => {
      const indexA = (result?.testCorpusList || []).indexOf(a.corpus_id);
      const indexB = (result?.testCorpusList || []).indexOf(b.corpus_id);
      return indexA - indexB;
    }).map((item, index) => ({
      ...item,
      index: index + 1, // 初始化 index 字段
    }));

  selectedDisturbCorpusList.value = (disturbCorpusList.value || []).filter(item => (result?.disturbCorpusList || []).includes(item.corpus_id)).map((item, index) => ({
    ...item,
    index: index + 1, // 初始化 index 字段
  }));

  selectedNoiseCorpusList.value = (noiseCorpusList.value || []).filter(item => (result?.backgroundNoiseList || []).includes(item.corpus_id)).map((item, index) => ({
    ...item,
    index: index + 1, // 初始化 index 字段
  }));

  // 更新 selectedPlayConfigId
  selectedPlayConfigId.value = result.play_config_id;

  // 根据选中的播放配置类型设置
  const selectedPlayConfig = treeData.value.flatMap(category => category.children).find(config => config.key.split('-')[1] === result.play_config_id);

  const isRouse = rouseConfigs.some(x => x.play_config_id === result.play_config_id)
  if (isRouse) {
    rouseCorpusKey.value = result.rouseCorpusList.map(id => `rouse-corpus-${id}`);
    selectedRouseCorpus.value = [...(rouseCorpusList.value || []).filter(item => result.rouseCorpusList.includes(item.corpus_id))];

  } else {
    rouseCorpusKey.value = result.rouseCorpusList.length > 0 ? [`rouse-corpus-${result.rouseCorpusList[0]}`] : [];
    selectedRouseCorpus.value = [(rouseCorpusList.value || []).find(item => item.corpus_id === result.rouseCorpusList[0])];
  }

};

const onRouseSceneSelected = (scene: string) => {
  console.log(scene);
  if (scene === 'rouse') {
    isRouseRef.value = true; // 切换为多选
  } else {
    isRouseRef.value = false; // 切换为单选
  }
  currentScene.value = scene;
};

// 配置语料弹窗相关逻辑
const showCorpusModal = () => {
  corpusModalVisible.value = true;
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 500);
};

const handleCorpusModalOk = () => {
  corpusModalVisible.value = false;

  // 更新唤醒语料
  selectedRouseCorpus.value = rouseCorpusList.value.filter(item => (rouseCorpusKey.value || []).includes(`rouse-corpus-${item.corpus_id}`));

  // 更新测试语料，保持原有顺序
  const currentTestCorpusIds = selectedTestCorpusList.value.map(item => item.corpus_id);
  const currentTestCorpusOrder = {};

  // 保存当前测试语料的顺序信息
  selectedTestCorpusList.value.forEach(item => {
    currentTestCorpusOrder[item.corpus_id] = item.index;
  });

  // 获取新选择的测试语料ID列表
  const newTestCorpusIds = testCorpusKeys.value.map(key => key.replace('test-corpus-', ''));

  // 筛选出新选择的测试语料
  const newSelectedTestCorpusList = testCorpusList.value.filter(item =>
    newTestCorpusIds.includes(item.corpus_id)
  );

  // 对新选择的测试语料进行排序
  // 1. 保持原有顺序的语料
  // 2. 新增的语料添加到末尾
  newSelectedTestCorpusList.sort((a, b) => {
    const aInOldList = currentTestCorpusIds.includes(a.corpus_id);
    const bInOldList = currentTestCorpusIds.includes(b.corpus_id);

    if (aInOldList && bInOldList) {
      // 两个都在原列表中，按原来的顺序排序
      return currentTestCorpusOrder[a.corpus_id] - currentTestCorpusOrder[b.corpus_id];
    } else if (aInOldList) {
      // 只有a在原列表中，a排在前面
      return -1;
    } else if (bInOldList) {
      // 只有b在原列表中，b排在前面
      return 1;
    } else {
      // 两个都是新增的，保持它们在testCorpusList中的相对顺序
      return 0;
    }
  });

  // 更新索引
  selectedTestCorpusList.value = newSelectedTestCorpusList.map((item, index) => ({
    ...item,
    index: index + 1, // 初始化 index 字段
  }));

  // 更新干扰语料
  selectedDisturbCorpusList.value = disturbCorpusList.value.filter(item =>
    (disturbCorpusKeys.value || []).includes(`disturb-corpus-${item.corpus_id}`)
  );

  // 更新背景噪声
  selectedNoiseCorpusList.value = noiseCorpusList.value.filter(item =>
    (noiseCorpusKeys.value || []).includes(`noise-corpus-${item.corpus_id}`)
  );
};

const handleCorpusModalCancel = () => {
  corpusModalVisible.value = false;
};

const handleTransferChange = (nextTargetKeys) => {
  testCorpusKeys.value = nextTargetKeys;
};

const handleRouseTransferChange = (nextTargetKeys) => {
  rouseCorpusKey.value = nextTargetKeys;
};

const handleDisturbTransferChange = (nextTargetKeys) => {
  disturbCorpusKeys.value = nextTargetKeys;
};

const handleNoiseTransferChange = (nextTargetKeys) => {
  noiseCorpusKeys.value = nextTargetKeys;
};

const disturbCorpusColumns = [
  { title: '语料id', dataIndex: 'corpus_id' },
  { title: '语料文本', dataIndex: 'text' },
  { title: '音频时长(s)', dataIndex: 'audio_duration' },
];

const handleSave = async () => {
  if (!selectedPlanId.value) {
    ElMessageBox.alert('请选择或新建方案', '提示', {
      confirmButtonText: '确定',
    });
    return;
  }

  if (!selectedPlayConfigId.value) {
    ElMessageBox.alert('请选择或新建播放配置', '提示', {
      confirmButtonText: '确定',
    });
    return;
  }

  // 按照当前显示顺序（index字段）获取测试语料ID列表
  const sortedTestCorpusList = [...selectedTestCorpusList.value].sort((a, b) => a.index - b.index);
  const new_testCorpusList = sortedTestCorpusList.map(x => x.corpus_id);

  const new_rouseCorpusList = rouseCorpusData.value.map(x => x.corpus_id);
  const new_disturbCorpusList = selectedDisturbCorpusList.value.map(x => x.corpus_id);
  const new_noiseCorpusList = selectedNoiseCorpusList.value.map(x => x.corpus_id);

  const payload = {
    project_id: projectDetail.value.project_id,
    plan_id: selectedPlanId.value,
    testCorpusList: new_testCorpusList,
    rouseCorpusList: new_rouseCorpusList,
    disturbCorpusList: new_disturbCorpusList,
    backgroundNoiseList: new_noiseCorpusList,
    play_config_id: selectedPlayConfigId.value,
  };

  console.log('保存的测试语料顺序:', new_testCorpusList);

  const res = (await http.post('/test_project/save_plan_detail', payload));
  if (res.status === 0) {
    ElMessage.success('保存成功');
  } else {
    ElMessage.error('保存失败');
  }
};

const handleSaveAndExecute = async () => {
  await handleSave();

  // 弹窗提示用户是否前往项目执行页面
  ElMessageBox.confirm('保存成功，是否前往项目执行页面？', '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    type: 'info',
  }).then(() => {
    // 用户选择“是”，导航到项目执行页面
    router.push('/execution');
  }).catch(() => {
    // 用户选择“否”，不做任何操作
  });
};

const goToProject = () => {
  router.push('/project');
};

const playAudio = (record) => {
  const audioPath = record.audio_url;
  if (!audioPath) {
    ElMessage.warning('音频路径不存在');
    return;
  }
  const newPath = audioPath.replaceAll('\\', '/').split('/backend')?.[1];
  const audio = new Audio(newPath);
  audio.play();
};

const handleIndexChange = (item, newIndex) => {
  const oldIndex = item.index;

  if (newIndex === oldIndex) return;

  // 找到要移动的项目
  const movedItem = selectedTestCorpusList.value.find(i => i.index === oldIndex);
  if (!movedItem) return;

  // 从原位置移除
  selectedTestCorpusList.value = selectedTestCorpusList.value.filter(i => i.index !== oldIndex);

  // 插入到新位置
  selectedTestCorpusList.value.splice(newIndex - 1, 0, { ...movedItem, index: newIndex });

  // 更新所有项目的 index
  selectedTestCorpusList.value.forEach((item, index) => {
    item.index = index + 1;
  });
};

const reverseTestCorpusList = () => {
  selectedTestCorpusList.value.reverse();
  selectedTestCorpusList.value.forEach((item, index) => {
    item.index = index + 1;
  });
};

const handleReturn = () => {
  router.push('/project');
}


watch(
  filterItem, // 监听的对象
  (val) => {


  },
  { deep: true } // 开启深度监听
);
</script>

<style scoped>
/* Add your custom styles here */
h3 {
  margin-top: 20px;
}

</style>