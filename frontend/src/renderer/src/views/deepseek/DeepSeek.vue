<template>
    <a-spin :spinning="loading" tip="加载中...">
        <div class="container">
            <!-- 较大的输入框 -->
            <textarea v-model="userInput" placeholder="请输入内容..." class="input-box"></textarea>
            <br />
            <button @click="callDeepSeekAPI" class="submit-button">问问 DeepSeek</button>
        </div>
                    <!-- 展示返回的 content -->
                    <div v-if="response" class="response-box">
                <h3>响应内容:</h3>
                <pre>{{ response }}</pre>
            </div>

            <!-- 错误信息 -->
            <div v-if="error" class="error-box">
                <h3>错误信息:</h3>
                <pre>{{ error }}</pre>
            </div>
    </a-spin>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { defineComponent } from 'vue';
import { Spin } from 'ant-design-vue';

// 注册 Ant Design Vue 的 Spin 组件
defineComponent({
    components: {
        ASpin: Spin,
    },
});

const userInput = ref(''); // 用户输入的内容
const response = ref(null); // 存储返回的 content
const error = ref(null); // 存储错误信息
const loading = ref(false); // 控制 loading 状态

const callDeepSeekAPI = async () => {
    // 设置 loading 为 true
    loading.value = true;

    const url = "/v1/infers/937cabe5-d673-47f1-9e7c-2b4de0600431/v1/chat/completions";
    const apiKey = 'S337kq_A2VyJGcxdl6gF-jcav10q8oTjSzwVwOSywR3rvxs2bDnQPSNgrxAXL2Dj3FcSt7YUxWIvOREu04jfvA';

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const data = {
        model: "DeepSeek-R1-Distill-Qwen-32B",
        max_tokens: 2000,
        messages: [
            { role: "user", content: userInput.value || "你好" }, // 使用用户输入的内容，如果为空则默认发送 "你好"
        ],
        stream: false,
        temperature: 1.0,
    };

    try {
        const resp = await axios.post(url, data, { headers });
        // 仅展示返回的 content 字段
        response.value = resp.data.choices[0].message.content;
        error.value = null;
    } catch (err) {
        error.value = err.response ? err.response.data : err.message;
        response.value = null;
    } finally {
        // 无论成功还是失败，都将 loading 设置为 false
        loading.value = false;
    }
};
</script>

<style scoped>
.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    text-align: center;
}

.input-box {
    width: 100%;
    height: 150px;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-bottom: 20px;
    resize: vertical;
}

.submit-button {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submit-button:hover {
    background-color: #40a9ff;
}

.response-box, .error-box {
    margin-top: 20px;
    padding: 15px;
    border-radius: 5px;
    background-color: #f4f4f4;
}

.response-box h3, .error-box h3 {
    margin-bottom: 10px;
    font-size: 18px;
    color: #333;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
    font-size: 14px;
    color: #333;
}
</style>