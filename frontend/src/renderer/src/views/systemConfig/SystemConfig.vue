<template>
  <div style="margin: 1rem">
    <a-card title="远程服务器设置" style="width: 400px;">
      <a-form :model="form" @finish="onFinish">
        <a-form-item label="服务器端口" name="serverPort">
          <a-tooltip title="请输入服务器地址与端口，例如 http://127.0.0.1:8080/" placement="right">
            <a-input v-model:value="form.serverPort" placeholder="请输入服务器地址与端口, 例如http://127.0.0.1:8080/" style="width: 100%;"></a-input>
          </a-tooltip>
        </a-form-item>
        <a-form-item style="text-align: right;">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card title="颜色选择" style="margin-top: 1rem;">
      <div style="display: flex;gap: 2rem">
        <div>
          <h1 :style="{ color: colorPrimary}">系统主题颜色</h1>
          <v-color-picker v-model="colorPrimary" mode="hex" @update:model-value="updateColorPrimary"></v-color-picker>
        </div>
        <div>
          <a-button :style="{background: buttonColorPrimary, color: 'white'}">按钮颜色</a-button>
          <div :style="{ height: '1.75rem'}"> </div>
          <v-color-picker v-model="buttonColorPrimary" mode="hex" @update:model-value="updateButtonColorPrimary"></v-color-picker>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { ElMessage } from 'element-plus';
import { useThemeStore } from '../../themeStore'; // 引入 Pinia Store

const themeStore = useThemeStore();

const colorPrimary = ref(themeStore.colorPrimary);
const buttonColorPrimary = ref(themeStore.buttonColorPrimary);

const updateColorPrimary = (color: string) => {
  console.log(color);
  themeStore.setColorPrimary(color);
};

const updateButtonColorPrimary = (color: string) => {
  console.log(color);
  themeStore.setButtonColorPrimary(color);
};

// 定义表单数据
const form = reactive({
  serverPort: localStorage.getItem('serverPort') || '',
});

// 表单提交处理函数
const onFinish = (values: any) => {
  const serverPort = form.serverPort;
  console.log(serverPort);
  // 校验格式：必须以http或https开头，且包含端口号
  const urlPattern = /^https?:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|localhost)(:\d+)?\//;
  if (!urlPattern.test(serverPort)) {
    message.error('服务器地址与端口格式不正确，请输入正确的格式，例如http://***********:8080/');
    return;
  }

  // 存储到localStorage
  localStorage.setItem('serverPort', serverPort);
  message.success('配置已保存');
  if (process.env.NODE_ENV !== 'production') {
    ElMessage.warning('当前是开发环境， 配置此项重启后生效，请使用npm run build:mac 或npm run build:win 获取可执行文件')
  } else {
    ElMessage.info('配置成功，需要重新启动应用, 5秒后即将自动重启');
    setTimeout(() => {
      window.electron.ipcRenderer.send('restart-app');
    })
  }
};
</script>

<style scoped>
@keyframes shine {
  0% {
    box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow: 0 0 30px 10px rgba(255, 255, 255, 1);
  }
  100% {
    box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
  }
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ant-card-head {
  border-bottom: 1px solid #e8e8e8;
}

.ant-card-body {
  padding: 24px;
}

.ant-form-item {
  margin-bottom: 24px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

.ant-form-item-label {
  font-weight: bold;
}

.ant-input {
  width: 100%;
}

.ant-btn-primary {
  float: right;
}

.color-picker-container {
  margin: 1rem;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.color-picker-container h1 {
  margin-bottom: 1rem;
}
</style>