<template>
  <div class="nav-container">
    <div class="nav-mid">
      <div class="nav-items">
        <img :src="logo" style="width: 8rem; height: 2.5rem" class="logo" />
        <div class="app-name">VTS</div>
        <div v-for="item in navItems" :key="item.path" :class="route.path === item.path ? 'active' : ''" class="nav-item" @click="goToPath(item.path)">
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="close-button" @click="minWindow">
      <span>-</span>
    </div>
    <div class="close-button" @click="closeWindow">
      <span>×</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useThemeStore } from '../../themeStore'; // 根据实际路径调整
import { useElectronVar } from '@renderer/stores/useElectronVarStore';
import { onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import logo from './logo.svg';

const router = useRouter();
const route = useRoute();
const themeStore = useThemeStore();
const { platform } = useElectronVar();

// 导航项
const navItems = [
  { name: '测试项目', path: '/project' },
  { name: '设备配置', path: '/device-config' },
  { name: '语料库', path: '/corpus' },
  { name: '播放配置', path: '/playConfig'},
  { name: '总线配置', path: '/dbcConfig'},
  { name: '系统设置', path: '/systemConfig' },
  { name: '系统日志', path: '/log/list' },
];

// 路由跳转
const goToPath = (path: string) => {
  router.push(path);
};

// 关闭窗口
const closeWindow = () => {
  window.electron.ipcRenderer.send('app-quit');
};

// 最小化窗口
const minWindow = () => {
  window.electron.ipcRenderer.send('app-minimize');
};

// 监听主题颜色变化
watch(
  () => themeStore.colorPrimary,
  (newColor) => {
    document.documentElement.style.setProperty('--color-primary', newColor);
  }
);

watch(
  () => themeStore.buttonColorPrimary,
  (newColor) => {
    document.documentElement.style.setProperty('--color-button-primary', newColor);
  }
);

// 初始化时设置主题颜色
onMounted(() => {
  document.documentElement.style.setProperty('--color-primary', themeStore.colorPrimary);
  document.documentElement.style.setProperty('--color-button-primary', themeStore.buttonColorPrimary);
});
</script>

<style lang="scss" scoped>
:root {
  --color-primary: #db4aeb;
  --text-color: #FFFFFF; /* 字体颜色固定为白色 */
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 62px;
  box-sizing: border-box;
  background: var(--color-primary); /* 使用主题颜色 */
  position: relative;
  overflow: hidden;
  -webkit-app-region: drag;
  font-family: 微软雅黑;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(96, 96, 96, 0.1), rgba(128, 128, 128, 0.05) 50%, transparent 50%);
    z-index: 1;
    pointer-events: none;
  }

  .nav-mid {
    width: 100%;
    overflow: hidden;
    padding: 0 48px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  .nav-items {
    display: flex;
    gap: 20px;
    position: relative;
    z-index: 2;
    align-items: center;
    margin-left: 3rem;

    .logo {
      height: 100%;
      width: 3rem;
      margin-right: -10px; /* 调整Logo和应用名称之间的间距 */
    }

    .app-name {
      font-family: '宋体', ; /* 修改字体 */
      font-size: 1.6rem;
      background: rgb(239, 147, 54); /* 固定灰色渐变 */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-right: 20px; /* 调整应用名称和导航项之间的间距 */
      color: var(--text-color); /* 字体颜色固定为白色 */
    }

    .nav-item {
      cursor: pointer;
      color: var(--text-color); /* 字体颜色固定为白色 */
      padding: 8px 12px;
      border-radius: 4px;
      transition: background 0.3s ease, color 0.3s ease;
      position: relative;
      -webkit-app-region: no-drag;

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--color-primary); /* 使用主题颜色 */
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.1); /* 使用主题颜色 */

        &::before {
          transform: scaleX(1);
        }
      }

      &.active {
        color: var(--text-color); /* 字体颜色固定为白色 */
        background: rgba(0, 0, 0, 0.05); /* 使用主题颜色 */

        &::before {
          transform: scaleX(1);
        }
      }
    }
  }

  .close-button {
    cursor: pointer;
    color: var(--text-color); /* 字体颜色固定为白色 */
    padding: 8px 12px;
    border-radius: 4px;
    transition: background 0.3s ease, color 0.3s ease, transform 0.3s ease;
    position: relative;
    -webkit-app-region: no-drag;
    border: 1px solid transparent; /* 添加透明边框 */

    &:hover {
      background: rgba(0, 0, 0, 0.1); /* 使用主题颜色 */
      border-color: var(--color-primary); /* 使用主题颜色 */
      transform: scale(1.05); /* 悬浮时稍微放大 */
    }

    span {
      font-size: 18px;
      font-weight: bold;
    }
  }
}
</style>