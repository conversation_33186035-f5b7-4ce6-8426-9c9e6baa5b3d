<template>
    <div class="config-container">
        <a-row :gutter="24" style="height: 100%;">
            <a-col :span="11" style="height: 100%; display: flex; flex-direction: column;">
                <a-card title="DBC文件列表" :style="{ flex: selectedFile ? '1' : '1 1 auto', marginBottom: '20px' }">
                    <template #extra class="test-results-header">
                        <el-upload ref="uploadRef" :auto-upload="false" :on-change="onBeforeUploadFile"
                            v-model:file-list="fileList" accept="*" :show-file-list="false">
                            <template #trigger>
                                <div
                                    style="float: right; transform: scale(0.85); margin-top: 0.45rem; margin-right: -1rem;">
                                    <a-button>上传DBC文件</a-button>
                                </div>

                            </template>
                        </el-upload>
                    </template>
                    <div class="file-list">
                        <a-menu v-if="dbcFileList.length > 0" mode="vertical" @select="handleMenuSelect">
                            <a-menu-item v-for="(file, index) in dbcFileList" :key="index">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ file.name }}</span>
                                    <a-button type="text" @click.stop="deleteDbcFile(file.file_id, index)"
                                        style="color: red; margin-left: 10px;">
                                        <template #icon>
                                            <delete-outlined />
                                        </template>
                                    </a-button>
                                </div>
                            </a-menu-item>
                        </a-menu>
                    </div>
                </a-card>
                <a-card title="DBC 文件定义" v-if="selectedFile && !checkable" style="flex: 1; margin-top: 20px;">
                    <definition-table v-if="selectedDefinition" :definition="selectedDefinition" />
                    <a-empty v-else description="暂无定义信息" />
                </a-card>
                <a-card title="信号条件配置" v-if="checkable && signalData.length">
                    <div style="display: flex; height:100%; max-height: 350px; overflow: auto;">
                        <!-- 左边展示 signalData 的 name -->
                        <a-menu style="width: 150px; border-right: 1px solid #e8e8e8;" mode="vertical"
                            @select="handleSignalSelect">
                            <a-menu-item v-for="(signal, index) in signalData" :key="index">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <a-tooltip>
                                        <template #title>{{ signal.title }}</template>
                                        <span style="max-width: 100px; overflow: hidden; text-overflow: ellipsis;">{{
                                            signal.title
                                        }}</span>
                                    </a-tooltip>

                                    <a-button type="text" @click.stop="deleteSignal(index)" style="color: red;">
                                        <template #icon>
                                            <delete-outlined />
                                        </template>
                                    </a-button>
                                </div>
                            </a-menu-item>
                        </a-menu>

                        <!-- 右边展示信号的条件配置 -->
                        <div style="flex: 1; margin-left: 20px; max-height: 300px; overflow-x: auto;">
                            <div v-for="(signal, signalIndex) in signalData" :key="signalIndex"
                                v-show="selectedSignalIndex === signalIndex">
                                <div v-for="(value, valueIndex) in signal.values" :key="valueIndex"
                                    style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <!-- 关系选择框 -->
                                    <a-select v-model:value="value.relation" style="width: 80px; margin-right: 10px;">
                                        <a-select-option value="=">=</a-select-option>
                                        <a-select-option value="<">
                                            < </a-select-option>
                                                <a-select-option value=">">></a-select-option>
                                                <a-select-option value="<=">
                                                    <=</a-select-option>
                                                        <a-select-option value=">=">
                                                            >= </a-select-option>
                                                        <a-select-option value="!=">!=</a-select-option>
                                    </a-select>

                                    <!-- 数字输入框 -->
                                    <a-input-number v-model:value="value.value" style="flex: 1; margin-right: 10px;" />

                                    <!-- 添加按钮 (+) -->
                                    <a-button @click="addValue(signalIndex)" style="margin-right: 10px;">
                                        +
                                    </a-button>

                                    <!-- 删除按钮 (-) -->
                                    <a-button @click="removeValue(signalIndex, valueIndex)"
                                        :disabled="signal.values.length === 1">
                                        -
                                    </a-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </a-card>
            </a-col>
            <a-col :span="13" style="height: 100%;">
                <a-card title="文件内容" :bordered="false" style="height: 100%;">
                    <tree-view :tree-data="treeData" @node-selected="handleNodeSelected" :checkable="checkable"
                        :checkedKeys="localCheckedKeys" @update:checkedKeys="(value) => localCheckedKeys = value" />
                </a-card>
            </a-col>
        </a-row>
    </div>
</template>

<script setup lang="jsx">
import { ref, onMounted, watch } from 'vue';
import TreeView from './TreeView.vue';
import { ElMessage } from 'element-plus';
import { http } from '@renderer/http';
import DefinitionTable from './DefinitionTable.vue';
import { DeleteOutlined } from '@ant-design/icons-vue'; // 引入删除图标
import { defineExpose } from 'vue';

const uploadUrl = '/can/upload_dbc_file/'; // 统一使用一个 URL
const headers = {
    'Content-Type': 'multipart/form-data',
};

const props = defineProps({
    checkable: {
        type: Boolean,
    },
    updateData: {
        type: Function,
    }
});

const localCheckedKeys = ref([]);
const fileCheckedKeys = ref({}); // 存储每个文件的 checkedKeys

const getSignalDataFromTree = (signalKeys, treeData) => {
    const result = [];

    // 递归遍历树形数据
    const traverse = (nodes) => {
        for (const node of nodes) {
            // 如果当前节点的 key 在 signalKeys 中，则将其添加到结果中
            if (signalKeys.includes(node.key)) {
                result.push(node);
            }
            // 如果有子节点，递归遍历子节点
            if (node.children && node.children.length > 0) {
                traverse(node.children);
            }
        }
    };

    // 从根节点开始遍历
    traverse(treeData);
    return result;
};

const fileList = ref([]);
const dbcFileList = ref([]);
const treeData = ref([]);
const selectedDefinition = ref(null);
const selectedFile = ref(null);

const handleUploadError = (error) => {
    ElMessage.error('文件上传失败');
};

const handleMenuSelect = ({ key }) => {
    const selected = dbcFileList.value[key];
    if (selected) {
        selectFile(selected);
    }
};

onMounted(() => {
    getDbcFileList();
});

const onBeforeUploadFile = async (file) => {
    const formData = new FormData();
    formData.append('info', JSON.stringify({ category: 'dbc' }));
    formData.append('file', file.raw);
    try {
        const response = await http.post(uploadUrl, formData);
        getDbcFileList();
    } catch (error) {
        console.error("Upload failed: ", error);
        handleUploadError(error);
    }
};

const getDbcFileList = async () => {
    const response = await http.post('/can/get_dbc_filelist', {}); // 根据实际接口调整
    if (response.status === 0) { // 根据实际响应结构调整
        dbcFileList.value = response.data.list; // 假设返回的数据在 files 字段
    } else {
        ElMessage.error('获取文件列表失败');
    }
};

const getFileContent = async (fileId) => {
    try {
        const response = await http.post('/can/get_file_content', { file_id: fileId });
        if (response.status === 0) {
            treeData.value = transformToTreeData(response.data);
        }
    } catch (error) {
        ElMessage.error('获取文件内容失败');
        console.error("Get file content failed:", error);
        // Mock数据示例
        treeData.value = transformToTreeData({
            name: '示例DBC',
            protocol: 'CAN',
            comment: '示例文件',
            messages: [
                {
                    name: '报文1',
                    type: '标准帧',
                    dlc: 8,
                    id: '0x101',
                    cycle: 100,
                    comment: '示例报文',
                    signals: [
                        {
                            name: '信号1',
                            length: 16,
                            byte_order: 'Intel',
                            data_type: '无符号',
                            initial: 0,
                            min: 0,
                            max: 255,
                            unit: 'rpm',
                            factor: 0.1,
                            offset: 0,
                            start_bit: 16,
                            comment: '示例信号',
                            signal_id: 'sig_1' // 添加 signal_id
                        }
                    ]
                }
            ],
            nodes: ['节点1', '节点2'],
            signals: [
                {
                    name: '信号3',
                    length: 16,
                    byte_order: 'Intel',
                    data_type: '无符号',
                    initial: 0,
                    min: 0,
                    max: 255,
                    unit: 'rpm',
                    factor: 0.1,
                    offset: 0,
                    start_bit: 16,
                    comment: '示例信号',
                    signal_id: 'sig_3' // 添加 signal_id
                }
            ]
        });
    }
};

const transformToTreeData = (content) => {
    console.log(content);
    const getChildren = () => {
        if (!props.checkable) {
            return [
                // 报文层级
                {
                    title: `报文(${content.messages.length})`,
                    key: 'messages',
                    children: content.messages.map(msg => ({
                        title: msg.name,
                        key: msg.frame_id, // 使用 frame_id 作为 key
                        type: 'message',
                        definition: { ...msg }, // 直接使用 msg 的拷贝
                        children: msg.signals?.map((sig, sigIndex) => ({
                            title: sig.name,
                            key: sig.signal_id, // 使用 frame_id 和 signal name 作为 key
                            type: 'signal',
                            signal_id: sig.signal_id, // 添加 signal_id
                            definition: { ...sig } // 直接使用 sig 的拷贝
                        }))
                    }))
                }
            ];
        } else {
            return [{
                title: `报文(${content.messages.length})`,
                key: 'messages',
                children: content.messages.map(msg => ({
                    title: msg.name,
                    key: `msg_${msg.frame_id}`, // 使用 frame_id 作为 key
                    type: 'message',
                    definition: { ...msg }, // 直接使用 msg 的拷贝
                    children: msg.signals?.map((sig, sigIndex) => ({
                        title: sig.name,
                        key: sig.signal_id, // 使用 frame_id 和 signal name 作为 key
                        type: 'signal',
                        signal_id: sig.signal_id, // 添加 signal_id
                        definition: { ...sig } // 直接使用 sig 的拷贝
                    }))
                }))
            }];
        }
    };

    return [
        {
            title: content.name,
            key: 'root',
            type: 'dbc',
            definition: {
                '名称': content.name,
                '协议': content.protocol,
                '注释': content.comment
            },
            children: getChildren()
        }
    ];
};

const selectFile = async (file) => {
    selectedFile.value = file;

    // 更新 localCheckedKeys
    if (fileCheckedKeys.value[file.file_id]) {
        localCheckedKeys.value = fileCheckedKeys.value[file.file_id];
    } else {
        localCheckedKeys.value = [];
    }

    await getFileContent(file.file_id);
};

const deleteDbcFile = async (fileId, index) => {
    try {
        const response = await http.post('/can/delete_dbc_file', { file_id: fileId });
        if (response.status === 0) {
            ElMessage.success('文件删除成功');
            // 更新文件列表
            dbcFileList.value.splice(index, 1);
            // 如果删除的是当前选中文件，清空相关状态
            if (selectedFile.value?.file_id === fileId) {
                selectedFile.value = null;
                treeData.value = [];
                selectedDefinition.value = null;
            }
        } else {
            ElMessage.error(response.message || '文件删除失败');
        }
    } catch (error) {
        console.error("Delete failed:", error);
        ElMessage.error('删除请求发送失败');
    }
};

const handleNodeSelected = (node) => {
    console.log(node);
    if (node.definition) {
        selectedDefinition.value = node.definition;
    }
};

const signalData = ref([]);

// 监听本地 checkedKeys 变化并通知父组件
watch(localCheckedKeys, (newVal) => {
    // 过滤出以 "sig" 开头的 key
    const signalKeys = newVal;

    // 获取对应的数据
    let unfilteredValue = getSignalDataFromTree(signalKeys, treeData.value);
    signalData.value = unfilteredValue.filter(x => x.type === 'signal');
    for (const signal of signalData.value) {
        if (!signal.values) {
            signal.values = [{ relation: '=', value: 0 }]
        }
    }
    if (props.updateData) {
        props.updateData(signalData.value);
    }
    // 输出结果（或进行其他操作）
    console.log('Filtered signal keys:', signalKeys);
    console.log('Filtered signal data:', signalData);

    // 更新 fileCheckedKeys
    if (selectedFile.value) {
        fileCheckedKeys.value[selectedFile.value.file_id] = newVal;
    }
}, { deep: true });

const selectedSignalIndex = ref(0);

const handleSignalSelect = ({ key }) => {
    selectedSignalIndex.value = key;

    // 检查当前选中的信号是否已经有 values 数组
    if (!signalData.value[key].values) {
        // 如果没有，则初始化一个默认的条件行
        signalData.value[key].values = [{ relation: '=', value: 0 }];
        if (props.updateData) {
            props.updateData(signalData.value);
        }
    }
};

const addValue = (signalIndex) => {
    if (!signalData.value[signalIndex].values) {
        signalData.value[signalIndex].values = [];
    }
    signalData.value[signalIndex].values.push({ relation: '=', value: 0 });
    if (props.updateData) {
        props.updateData(signalData.value);
    }
};

const removeValue = (signalIndex, valueIndex) => {
    signalData.value[signalIndex].values.splice(valueIndex, 1);
    if (props.updateData) {
        props.updateData(signalData.value);
    }
};

const deleteSignal = (index) => {
    // 从 signalData 中移除对应的信号
    const deletedSignal = signalData.value.splice(index, 1)[0];

    // 更新 localCheckedKeys，移除对应的 key
    localCheckedKeys.value = localCheckedKeys.value.filter(key => key !== deletedSignal.key);

    // 如果删除的是当前选中的信号，重置 selectedSignalIndex
    if (selectedSignalIndex.value === index) {
        selectedSignalIndex.value = 0;
    }
    if (props.updateData) {
        props.updateData(signalData.value);
    }
};

const setSignalData = (newSignalData) => {
    signalData.value = newSignalData;
    if (props.updateData) {
        props.updateData(signalData.value);
    }
};

// 暴露 setSignalData 函数给父组件
defineExpose({
    setSignalData
});
</script>


<style scoped>
.config-container {
    margin: 20px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    height: calc(100vh - 12rem);
}

.file-list {
    margin-top: 20px;
}

.file-list ul {
    list-style-type: none;
    padding: 0;
}

.file-list li {
    cursor: pointer;
    padding: 5px 0;
    border-bottom: 1px solid #e8e8e8;
}

.file-list li:last-child {
    border-bottom: none;
}
</style>