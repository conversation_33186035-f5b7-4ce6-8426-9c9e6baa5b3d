<template>
  <a-tabs default-active-key="dbc-config" @change="handleTabChange" style="margin: 20px;">
    <a-tab-pane key="dbc-config" tab="DBC 文件配置">
      <DbcConfig />
    </a-tab-pane>
    <a-tab-pane key="channel-config" tab="通道配置">
      <ChannelConfig />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="jsx">
import { defineProps } from 'vue';
import DbcConfig from './DbcConfig.vue';
import ChannelConfig from './ChannelConfig.vue';

const handleTabChange = (key) => {
  console.log(`当前选中的标签是: ${key}`);
};
</script>

<style scoped>
/* 您可以在这里添加样式 */
</style>