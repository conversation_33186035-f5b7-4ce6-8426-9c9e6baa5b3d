<!-- TreeView.vue -->
<template>
  <div>
    <a-tree
      :tree-data="treeData"
      :default-expand-all="true"
      @select="onSelect"
      :checkable="checkable"
      :checkedKeys="checkedKeys"
      @check="onCheck"，
      defaultExpandAll
      v-if="treeData.length"
      style="width: 100%; max-height: 60vh; overflow: auto;"
    >
      <template #title="{ title, key, dataRef }">
        <span :title="title" class="tree-node-title">
          <component 
            :is="getIconComponent(dataRef)" 
            :style="{ color: getIconColor(dataRef), marginRight: '8px' }"
          />
          {{ title }}
        </span>
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="jsx">
import { defineProps, defineEmits, onMounted } from 'vue';
import {
  FileTextOutlined,    // 报文子项
  UserOutlined,        // 节点子项
  RadarChartOutlined,  // 信号子项
  ClusterOutlined,     // 报文父级
  ApartmentOutlined,   // 节点父级
  AlertOutlined        // 信号父级
} from '@ant-design/icons-vue';

const props = defineProps({
  treeData: Array,
  checkable: Boolean,
  checkedKeys: Array // 接收父组件传递的 checkedKeys
});

const emits = defineEmits(['node-selected', 'update:checkedKeys'])

// 其他代码...

const onCheck = (checkedKeys) => {
  // 触发 update:checkedKeys 事件，更新父组件的值
  emits('update:checkedKeys', checkedKeys);
};

const iconMap = {
  // 父级图标
  messages: ClusterOutlined,
  nodes: ApartmentOutlined,
  signals: AlertOutlined,
  independent_signals: AlertOutlined,
  
  // 子级图标
  message: FileTextOutlined,
  node: UserOutlined,
  signal: RadarChartOutlined,
  independent_signal: RadarChartOutlined
};

const colorMap = {
  // 父级颜色
  messages: '#1890ff',    // 蓝色
  nodes: '#52c41a',       // 绿色
  signals: '#fa8c16',     // 橙色
  independent_signals: '#fa8c16',
  
  // 子级颜色
  message: '#40a9ff',     // 浅蓝
  node: '#73d13d',        // 浅绿
  signal: '#ffa940',      // 浅橙
  independent_signal: '#ffa940'
};

const getIconComponent = (dataRef) => {
  return iconMap[dataRef.type] || 
         iconMap[dataRef.key.split('_')[0]] || 
         FileTextOutlined;
};

const getIconColor = (dataRef) => {
  return colorMap[dataRef.type] || 
         colorMap[dataRef.key.split('_')[0]] || 
         '#bfbfbf';
};

const onSelect = (selectedKeys, { node }) => {
  if (node.dataRef?.definition) {
    emits('node-selected', node.dataRef);
  }
};
</script>

<style scoped>
.tree-node-title {
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>