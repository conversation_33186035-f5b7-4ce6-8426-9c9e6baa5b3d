<template>
  <div class="config-container">
    <a-row :gutter="24">
      <!-- 左侧通道树 -->
      <a-col :span="4">
        <a-card title="可用通道" style="height: 100%">
          <!-- 在 #extra 插槽中添加初始化按钮 -->
          <template #extra>
            <a-button type="primary" size="small" @click="initChannels" :loading="initializing">
              初始化
            </a-button>
          </template>
          <a-tree 
            v-model:checkedKeys="checkedKeys" 
            :tree-data="channels" 
            :field-names="{ key: 'channel_id', title: 'channel_name' }"
            checkable 
            @select="handleNodeSelect"
          >
            <template #title="record">
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span>{{ record.channel_name }}</span>
                <div v-if="record.isLeaf" style="margin-left: 8px;">
                  <a-space>
                    <a-tooltip title="启用通道">
                      <PlayCircleOutlined :style="{ color: themeStore.buttonColorPrimary}" @click="handleEnable(record.channel_id)"/>
                    </a-tooltip>
                    <a-tooltip title="禁用通道">
                      <StopOutlined :style="{ color: themeStore.buttonColorPrimary}" @click="handleDisable(record.channel_id)"/>
                    </a-tooltip>
                  </a-space>
                </div>
              </div>
            </template>
          </a-tree>
        </a-card>
      </a-col>

      <!-- 右侧配置表单 -->
      <a-col :span="20">
        <a-form :model="formState" layout="vertical">
          <!-- 模式选择 和 关联DBC文件 各占一行的一半 -->
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="模式">
                <a-select v-model:value="formState.mode" @change="handleModeChange">
                  <a-select-option value="CAN">CAN</a-select-option>
                  <a-select-option value="ISO CAN FD">ISO CAN FD</a-select-option>
                  <a-select-option value="NON ISO CAN FD">NON ISO CAN FD</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6" :offset="2">
              <a-form-item label="关联DBC文件">
                <a-select v-model:value="formState.dbc_file_id" :loading="loadingDbcList" @focus="getDbcFileList">
                  <a-select-option v-for="file in dbcFileList" :key="file.file_id" :value="file.file_id">
                    {{ file.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6" :offset="2">
              <a-form-item label="电阻是否启动">
                  <a-switch v-model:checked="formState.enable_resistor" />
              </a-form-item>
            </a-col>
          </a-row>

          <template v-if="formState.mode === 'CAN'">
            <a-card title="CAN 模式配置" style="margin-bottom: 16px;">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="Bit Rate">
                    <a-select v-model:value="formState.bitrate">
                      <a-select-option v-for="rate in canBitRates" :key="rate" :value="rate">
                        {{ rate }} kbit/s
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </template>

          <!-- CAN FD模式配置 -->
          <template v-else>
            <!-- 仲裁段配置 和 数据段配置 各占一行的一半 -->
            <a-row :gutter="16">
              <a-col :span="12">
                <div class="segment-config">
                  <h3>仲裁段配置</h3>
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="Bit Rate">
                        <a-select v-model:value="formState.arbitration.bitrate">
                          <a-select-option v-for="rate in arbitrationBitRates" :key="rate" :value="rate">
                            {{ rate }} kbit/s
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="segment-config">
                  <h3>数据段配置</h3>
                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="Bit Rate">
                        <a-select v-model:value="formState.data.bitrate">
                          <a-select-option v-for="rate in dataBitRates" :key="rate" :value="rate">
                            {{ rate }} kbit/s
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </a-col>
            </a-row>
          </template>

          <div style="display: flex; justify-content: flex-end;">
            <a-button type="primary" @click="saveConfig">下发配置</a-button>
          </div>
        </a-form>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="jsx">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { http } from '@renderer/http';
import { PlayCircleOutlined, StopOutlined } from '@ant-design/icons-vue';
import { useThemeStore } from '../../themeStore';
const themeStore = useThemeStore();
const dbcFileList = ref([]);
const loadingDbcList = ref(false);
const formState = reactive({
  mode: 'CAN',
  dbc_file_id: null,
  bitrate: 500,
  enable_resistor: false, // 新增字段
  arbitration: {
    bitrate: 500,
  },
  data: {
    bitrate: 500,
  }
});

const canBitRates = [50, 100, 125, 200, 250, 500, 800]; // CAN 波特率选项
const arbitrationBitRates = [50, 100, 125, 200, 250, 500]; // 仲裁段波特率选项
const dataBitRates = [200, 250, 500, 800, 1000, 2000]; // 数据段波特率选项

const checkedKeys = ref([]); // 勾选的节点
const channels = ref([]); // 通道数据
const loadingChannels = ref(false);
const initializing = ref(false); // 初始化按钮的加载状态

// 初始化通道
const initChannels = async () => {
  try {
    initializing.value = true;
    const res = await http.post('/can/init_channel');
    if (res.status === 0) {
      ElMessage.success('通道初始化成功');
      // 初始化成功后获取通道列表
      getChannels();
    } else {
      ElMessage.error('通道初始化失败');
    }
  } catch (error) {
    console.error("初始化通道失败:", error);
    ElMessage.error('初始化通道失败，请检查网络连接');
  } finally {
    initializing.value = false;
  }
};

// 获取通道列表
const getChannels = async () => {
  try {
    loadingChannels.value = true;
    const res = await http.post('/can/get_channel_list');
    if (res.status === 0) {
      channels.value = res.data.list;
    }
  } catch (error) {
    console.error("获取通道列表失败:", error);
    // Mock 数据
    channels.value = [
      { channel_id: 1, channel_name: 'CAN1' },
      { channel_id: 2, channel_name: 'CAN2' },
      { channel_id: 3, channel_name: 'CAN3' }
    ];
    ElMessage.error('获取通道列表失败，已使用 Mock 数据');
  } finally {
    loadingChannels.value = false;
  }
};

// 点击节点标签时加载配置
const handleNodeSelect = (selectedKeys, { node }) => {
  const channel = channels.value.find((c) => c.channel_id === node.key);
  if (channel) {
    loadChannelConfig(channel);
  }
};

// 加载通道配置
const loadChannelConfig = async (channel) => {
  try {
    const res = await http.post('/can/get_channel_config', { channel_id: channel.channel_id });
    if (res.status === 0) {
      // 更新表单数据
      const config = res.data;
      formState.mode = config.mode || 'CAN';
      formState.dbc_file_id = config.dbc_file_id || null;
      formState.bitrate = config.bitrate || 500;
      formState.enable_resistor = config.enable_resistor || false; // 加载电阻状态
      formState.arbitration = config.arbitration || {
        bitrate: 500,
      };
      formState.data = config.data || {
        bitrate: 500,
      };
      ElMessage.success('通道配置加载成功');
    } else {
      ElMessage.error('通道配置加载失败');
    }
  } catch (error) {
    console.error("加载通道配置失败:", error);
    ElMessage.error('加载通道配置失败，请检查网络连接');
  }
};

// 保存配置（更新多个勾选的节点）
const saveConfig = async () => {
  if (checkedKeys.value.length === 0) {
    ElMessage.error('请选择要下发配置的通道');
    return;
  }
  try {
    const res = await http.post('/can/save_channel_config', {
      channel_ids: checkedKeys.value,
      config: formState
    });
    if (res.status === 0) {
      ElMessage.success('配置保存成功');
    }
  } catch (error) {
    ElMessage.error('配置保存失败');
  }
};

// 初始化加载
onMounted(() => {
  getDbcFileList();
  getChannels();
});

const getDbcFileList = async () => {
  try {
    loadingDbcList.value = true;
    const res = await http.post('/can/get_dbc_filelist', {});
    if (res.status === 0) {
      dbcFileList.value = res.data?.list;
    }
  } catch (error) {
    console.error("获取 DBC 文件列表失败:", error);
    // Mock 数据
    dbcFileList.value = [
      { file_id: '1', channel_name: 'SolarDummy CAN1' },
      { file_id: '2', channel_name: 'SolarDummy CNN' }
    ];
    ElMessage.error('获取 DBC 文件列表失败，已使用 Mock 数据');
  } finally {
    loadingDbcList.value = false;
  }
};

const handleEnable = async (channelId) => {
  try {
    const res = await http.post('/can/enable_channel', { channel_id: channelId });
    if (res.status === 0) {
      ElMessage.success(`通道 ${channelId} 启用成功`);
      // 更新通道状态
      updateChannelStatus(channelId, true);
    } else {
      ElMessage.error(res.error_msg || '启用失败');
    }
  } catch (error) {
    ElMessage.error(`启用失败: ${error.message}`);
  }
};

// 禁用通道 
const handleDisable = async (channelId) => {
  try {
    const res = await http.post('/can/disable_channel', { channel_id: channelId });
    if (res.status === 0) {
      ElMessage.success(`通道 ${channelId} 禁用成功`);
      // 更新通道状态
      updateChannelStatus(channelId, false);
    } else {
      ElMessage.error(res.error_msg || '禁用失败');
    }
  } catch (error) {
    ElMessage.error(`禁用失败: ${error.message}`);
  }
};

// 更新通道状态显示
const updateChannelStatus = (channelId, isActive) => {
  channels.value = channels.value.map(channel => {
    if (channel.channel_id === channelId) {
      return { 
        ...channel,
        style: isActive ? { color: 'green' } : { color: 'red' }
      };
    }
    return channel;
  });
};
</script>

<style scoped>
.config-container {
  margin: 20px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.segment-config {
  margin: 16px 0;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.segment-config h3 {
  margin-bottom: 12px;
  color: rgba(0, 0, 0, 0.85);
}

.action-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-left: 1px solid #f0f0f0;
}

.ant-input-number {
  width: 100%;
}
</style>