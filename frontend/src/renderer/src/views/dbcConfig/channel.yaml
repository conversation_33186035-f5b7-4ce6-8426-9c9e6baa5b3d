# 接口文档
api:
  # 获取通道列表
  get_channel_list:
    url: /channel/get_channel_list
    method: POST
    description: 获取所有可用通道的列表
    response:
      success: boolean  # 请求是否成功
      channels:         # 通道列表
        - id: integer   # 通道ID
          name: string  # 通道名称

  # 获取通道配置
  get_channel_config:
    url: /channel/get_channel_config
    method: POST
    description: 获取指定通道的配置信息
    request:
      channel_id: integer  # 通道ID
    response:
      success: boolean     # 请求是否成功
      config:              # 通道配置信息
        mode: string       # 模式（CAN、ISO CAN FD、NON ISO CAN FD）
        dbf_file_id: string # 关联的DBC文件ID
        bitrate: integer    # 比特率（仅CAN模式）
        sampling_point: float # 采样点（仅CAN模式）
        sjw: integer        # SJW（仅CAN模式）
        arbitration:        # 仲裁段配置（CAN FD模式）
          bitrate: integer  # 比特率
          sampling_point: float # 采样点
          sjw: integer      # SJW
        data:              # 数据段配置（CAN FD模式）
          bitrate: integer  # 比特率
          sampling_point: float # 采样点
          sjw: integer      # SJW

  # 保存通道配置
  save_config:
    url: /channel/save_config
    method: POST
    description: 保存指定通道的配置信息
    request:
      channel_ids: array   # 通道ID列表
      config:              # 配置信息
        mode: string       # 模式（CAN、ISO CAN FD、NON ISO CAN FD）
        dbf_file_id: string # 关联的DBC文件ID
        bitrate: integer    # 比特率（仅CAN模式）
        sampling_point: float # 采样点（仅CAN模式）
        sjw: integer        # SJW（仅CAN模式）
        arbitration:        # 仲裁段配置（CAN FD模式）
          bitrate: integer  # 比特率
          sampling_point: float # 采样点
          sjw: integer      # SJW
        data:              # 数据段配置（CAN FD模式）
          bitrate: integer  # 比特率
          sampling_point: float # 采样点
          sjw: integer      # SJW
    response:
      success: boolean     # 请求是否成功

  # 获取DBC文件列表
  get_dbc_filelist:
    url: /dbc/get_dbc_filelist
    method: POST
    description: 获取所有可用的DBC文件列表
    response:
      success: boolean     # 请求是否成功
      files:               # DBC文件列表
        - file_id: string  # 文件ID
          name: string     # 文件名称