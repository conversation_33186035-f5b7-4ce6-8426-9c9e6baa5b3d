<!-- DefinitionTable.vue -->
<template>
    <div class="definition-table">
      <table>
        <tbody>
          <tr v-for="(value, key) in definition" :key="key">
            <td class="label-cell">{{ key }}</td>
            <td class="value-cell">{{ value || '无' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </template>
  
  <script setup>
  defineProps({
    definition: {
      type: Object,
      required: true,
      default: () => ({})
    }
  });
  </script>
  
  <style scoped>
  .definition-table {
    margin: 12px 0;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
  }
  
  td {
    padding: 0px 12px;
    border: 1px solid #e8e8e8;
    word-break: break-word;
  }
  
  .label-cell {
    width: 120px;
    background-color: #fafafa;
    font-weight: 500;
  }
  
  .value-cell {
    background-color: #fff;
  }
  </style>