api:
  # 上传 DBC 文件
  upload_dbc_file:
    url: /dbc/upload_dbc_file
    method: POST
    description: 上传 DBC 文件
    request:
      info: string  # 文件信息（JSON 格式，如 `{ category: 'dbc' }`）
      file: file    # 上传的文件
    response:
      success: boolean  # 请求是否成功

  # 获取 DBC 文件列表
  get_dbc_filelist:
    url: /dbc/get_dbc_filelist
    method: POST
    description: 获取所有 DBC 文件的列表
    response:
      success: boolean  # 请求是否成功
      files:            # DBC 文件列表
        - file_id: string  # 文件 ID
          name: string     # 文件名称

  # 获取 DBC 文件内容
  get_file_content:
    url: /dbc/get_file_content
    method: POST
    description: 获取指定 DBC 文件的内容
    request:
      file_id: string  # 文件 ID
    response:
      success: boolean  # 请求是否成功
      content:          # 文件内容
        name: string    # 文件名称
        protocol: string # 协议类型
        comment: string  # 文件注释
        messages:        # 报文列表
          - name: string # 报文名称
            type: string # 报文类型（如标准帧）
            dlc: integer # 数据长度
            id: string   # 报文 ID
            cycle: integer # 周期
            comment: string # 报文注释
            signals:      # 信号列表
              - name: string # 信号名称
                length: integer # 信号长度
                byte_order: string # 字节序
                data_type: string # 数据类型
                initial: integer  # 初始值
                min: integer      # 最小值
                max: integer      # 最大值
                unit: string      # 单位
                factor: float     # 放大因子
                offset: float     # 偏移量
                start_bit: integer # 起始位
                comment: string   # 信号注释
        nodes:           # 节点列表
          - string       # 节点名称
        signals:         # 独立信号列表
          - name: string # 信号名称
            length: integer # 信号长度
            byte_order: string # 字节序
            data_type: string # 数据类型
            initial: integer  # 初始值
            min: integer      # 最小值
            max: integer      # 最大值
            unit: string      # 单位
            factor: float     # 放大因子
            offset: float     # 偏移量
            start_bit: integer # 起始位
            comment: string   # 信号注释

# 页面功能
page:
  # 文件列表
  file_list:
    description: 显示所有 DBC 文件的列表
    actions:
      - upload_file:  # 上传 DBC 文件
          description: 用户点击上传按钮后，选择文件并上传
          triggers: onBeforeUploadFile
      - select_file:  # 选择文件
          description: 用户点击文件列表中的某一项，加载文件内容
          triggers: handleMenuSelect

  # 文件内容
  file_content:
    description: 显示选中的 DBC 文件内容
    components:
      - tree_view:  # 树形视图
          description: 以树形结构展示文件内容（如报文、信号、节点等）
          triggers: handleNodeSelected
      - definition_table:  # 定义表格
          description: 显示选中节点的详细信息（如报文或信号的属性）

  # 初始化
  initialization:
    description: 页面加载时初始化数据
    actions:
      - get_dbc_filelist:  # 获取 DBC 文件列表
          description: 加载所有 DBC 文件的列表
          triggers: onMounted

# 数据结构
data_structure:
  # DBC 文件列表
  dbc_file_list:
    - file_id: string  # 文件 ID
      name: string     # 文件名称

  # DBC 文件内容
  dbc_file_content:
    name: string       # 文件名称
    protocol: string   # 协议类型
    comment: string    # 文件注释
    messages:          # 报文列表
      - name: string   # 报文名称
        type: string   # 报文类型
        dlc: integer   # 数据长度
        id: string     # 报文 ID
        cycle: integer # 周期
        comment: string # 报文注释
        signals:       # 信号列表
          - name: string # 信号名称
            length: integer # 信号长度
            byte_order: string # 字节序
            data_type: string # 数据类型
            initial: integer  # 初始值
            min: integer      # 最小值
            max: integer      # 最大值
            unit: string      # 单位
            factor: float     # 放大因子
            offset: float     # 偏移量
            start_bit: integer # 起始位
            comment: string   # 信号注释
    nodes:             # 节点列表
      - string         # 节点名称
    signals:           # 独立信号列表
      - name: string   # 信号名称
        length: integer # 信号长度
        byte_order: string # 字节序
        data_type: string # 数据类型
        initial: integer  # 初始值
        min: integer      # 最小值
        max: integer      # 最大值
        unit: string      # 单位
        factor: float     # 放大因子
        offset: float     # 偏移量
        start_bit: integer # 起始位
        comment: string   # 信号注释