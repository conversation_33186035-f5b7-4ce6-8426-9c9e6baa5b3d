<template>
    <div
    style="display: flex; justify-content: space-between; height: 2.5rem; background-color: #f3f3f3; align-items: center;">
    <a-breadcrumb style="margin: 1rem;">
      <a-breadcrumb-item>语料库</a-breadcrumb-item>
    </a-breadcrumb>
  </div>
  <div style="margin: 20px;">
    <a-radio-group v-model:value="activeTab" @change="handleTabChange" button-style="solid">
      <a-radio-button button-style="solid" value="test">测试语料</a-radio-button>
      <a-radio-button button-style="solid" value="wake">唤醒语料</a-radio-button>
      <a-radio-button button-style="solid" value="interference">干扰语料</a-radio-button>
      <a-radio-button button-style="solid" value="background">背景噪声</a-radio-button>
      <!-- <a-radio-button value="multi">多轮对话语料</a-radio-button> -->
    </a-radio-group>

    <!-- 根据选择的值动态显示相应的内容 -->
    <test-corpus-main v-if="activeTab === 'test'"></test-corpus-main>
    <wake-corpus v-if="activeTab === 'wake'"></wake-corpus>
    <interference-corpus v-if="activeTab === 'interference'"></interference-corpus>
    <background-noise v-if="activeTab === 'background'"></background-noise>
    <!-- <multi-corpus v-if="activeTab === 'multi'"></multi-corpus> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TestCorpusMain from './TestCorpusMain.vue';
import WakeCorpus from './WakeCorpus.vue';
import MultiCorpus from './MultiCorpus.vue';
import InterferenceCorpus from './InterferenceCorpus.vue';
import BackgroundNoise from './BackgroundNoise.vue'

const activeTab = ref('test');

const handleTabChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  activeTab.value = target.value;
};
</script>

<style scoped>
/* 你可以在这里添加一些样式 */
</style>