<template>
  <div
    v-if="visible"
    class="audio-player-modal"
    :style="{ top: position.top + 'px', left: position.left + 'px' }"
    @mousedown="startDrag"
  >
    <div class="audio-player-content">
      <audio ref="audioPlayer" :src="audioUrl" controls></audio>
      <button class="close-button" @click="closeModal">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  audioUrl: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:visible']);

const audioPlayer = ref<HTMLAudioElement | null>(null);
const position = ref({ top: 40, left: window.innerWidth - 500 }); // 初始位置
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });

const closeModal = () => {
  emit('update:visible', false);
};

const startDrag = (event: MouseEvent) => {
  isDragging.value = true;
  dragStart.value = {
    x: event.clientX - position.value.left,
    y: event.clientY - position.value.top,
  };

  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', stopDrag);
};

const drag = (event: MouseEvent) => {
  if (isDragging.value) {
    position.value.left = event.clientX - dragStart.value.x;
    position.value.top = event.clientY - dragStart.value.y;
  }
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', drag);
  document.removeEventListener('mouseup', stopDrag);
};

// 监听visible的变化，当变为true时，在下一个tick播放音频
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (audioPlayer.value) {
        audioPlayer.value.play().catch(error => {
          console.error('无法播放音频:', error);
        });
      }
    });
  }
});

watch(() => props.audioUrl, (newVal) => {
  if (newVal) {
    nextTick(() => {
      if (audioPlayer.value) {
        audioPlayer.value.play().catch(error => {
          console.error('无法播放音频:', error);
        });
      }
    });
  }
});
</script>

<style scoped>
.audio-player-modal {
  position: fixed;
  z-index: 1000;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: grab;
}

.audio-player-content {
  display: flex;
  align-items: center;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin-left: 10px;
  color: #999;
}

.close-button:hover {
  color: #333;
}
</style>