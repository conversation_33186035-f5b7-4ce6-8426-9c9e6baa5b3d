<template>
    <a-tabs v-model:activeKey="activeTab" @change="handleTabChange" style="margin: 1rem"> 
        <a-tab-pane key="test" tab="单次对话">
            <!-- 测试语料内容 -->
            <test-corpus></test-corpus>
        </a-tab-pane>
        <a-tab-pane key="multi" tab="连续对话">
            <multi-corpus></multi-corpus>
        </a-tab-pane>
    </a-tabs>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TestCorpus from './TestCorpus.vue'
import MultiCorpus from './MultiCorpus.vue';
const activeTab = ref('test');
const handleTabChange = (key: string) => {
    activeTab.value = key;
  };
</script>

<style scoped></style>