<template>
    <div class="editable-tag-list">
      <!-- 渲染标签列表 -->
      <a-tag
        v-for="(tag, index) in tags"
        :key="index"
        closable 
        @close="handleTagClose(index)"
        @click="handleTagEdit(index)"
      >
        {{ tag }}
      </a-tag>
  
      <!-- 新增标签的输入框 -->
      <a-input
        v-if="inputVisible"
        ref="inputRef"
        v-model:value="inputValue"
        size="small"
        style="width: 78px;"
        @blur="handleInputConfirm"
        @pressEnter="handleInputConfirm"
      />
      <a-button
        v-else
        type="dashed"
        size="small"
        @click="showInput"
      >
        + 新增标签
      </a-button>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, watch, nextTick } from 'vue';
  
  const props = defineProps({
    value: {
      type: Array as () => string[],
      required: true,
    },
  });
  
  const emit = defineEmits(['update:value']);
  
  const tags = ref<string[]>([]);
  const inputVisible = ref(false);
  const inputValue = ref('');
  const inputRef = ref<HTMLInputElement | null>(null);
  
  // 监听父组件传递的 value 变化
  watch(
    () => props.value,
    (newValue) => {
      tags.value = newValue;
    },
    { immediate: true }
  );
  
  // 删除标签
  const handleTagClose = (index: number) => {
    tags.value.splice(index, 1);
    emit('update:value', tags.value);
  };
  
  // 编辑标签
  const handleTagEdit = (index: number) => {
    inputValue.value = tags.value[index];
    showInput();
    tags.value.splice(index, 1);
  };
  
  // 显示输入框
  const showInput = () => {
    inputVisible.value = true;
    nextTick(() => {
      inputRef.value?.focus();
    });
  };
  
  // 确认输入
  const handleInputConfirm = () => {
    if (inputValue.value && !tags.value.includes(inputValue.value)) {
      tags.value.push(inputValue.value);
      emit('update:value', tags.value);
    }
    inputVisible.value = false;
    inputValue.value = '';
  };
  </script>
  
  <style scoped>
  .editable-tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  </style>