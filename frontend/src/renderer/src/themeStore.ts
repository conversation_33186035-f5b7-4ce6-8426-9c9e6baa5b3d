import { defineStore } from 'pinia';

export const useThemeStore = defineStore('theme', {
  state: () => ({
    colorPrimary: localStorage.getItem('colorPrimary') || '#db4aeb',
    buttonColorPrimary: localStorage.getItem('buttonColorPrimary') || '#8090f8',
  }),
  actions: {
    setColorPrimary(color: string) {
      this.colorPrimary = color;
      localStorage.setItem('colorPrimary', color);
    },
    setButtonColorPrimary(color: string) {
      this.buttonColorPrimary = color;
      localStorage.setItem('buttonColorPrimary', color);
    },
  },
});