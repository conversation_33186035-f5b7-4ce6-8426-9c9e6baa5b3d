import { defineStore } from 'pinia';

export const usePageStore = defineStore('page', {
  state: () => ({
    page: 1,
    page_size: 10,
    result: '',
    sign: '',
    total: 0,
  }),
  actions: {
    setPage(page: number) {
      this.page = page;
    },
    setPageSize(page_size: number) {
      this.page_size = page_size;
    },
    setResult(result: string) {
      this.result = result;
    },
    setSign(sign: string) {
      this.sign = sign;
    },
    setTotal(total: number) {
      this.total = total;
    },
  },
});