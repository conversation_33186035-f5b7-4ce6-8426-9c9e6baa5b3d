# CVAtest

## Backend
```shell
cd backend
pip install -r requirements.txt
python main.py
```
### 打包
打包命令
```shell
cd backend
nuitka --mingw64 --standalone --enable-plugin=tk-inter --module-parameter=torch-disable-jit=no main.py
```
打包注意事项：
1. numpy库在conda环境打包要用conda安装，如果用pip安装了可以先pip uninstall
pip uninstall numpy
conda install numpy==1.26.4
2. 注释dspy库的Google库导入
C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\dspy\__init__.py
C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\dsp\modules\__init__.py
3. 把缺失的文件手动移到打包目录下
```shell
cd backend/main.dist
mkdir app/config/
mkdir pypinyin
mkdir silero_vad\data
mkdir aliyunsdkcore\data
mkdir paddleocr/ppocr/utils

cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\pypinyin\pinyin_dict.json .\pypinyin\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\pypinyin\phrases_dict.json .\pypinyin\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\silero_vad\data\silero_vad.jit .\silero_vad\data\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\aliyunsdkcore\data\retry_config.json .\aliyunsdkcore\data\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\aliyunsdkcore\data\timeout_config.json .\aliyunsdkcore\data\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\aliyunsdkcore\data\endpoints.json .\aliyunsdkcore\data\
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\paddleocr\ppocr\utils\ppocr_keys_v1.txt paddleocr/ppocr/utils
cp C:\Users\<USER>\miniconda3\envs\python396\Lib\site-packages\litellm\llms\tokenizers\anthropic_tokenizer.json ./
```
## Frontend
安装node.js 18.14的版本

配置npm源
cd frontend
npm install pnpm -g
配置pnpm源
pnpm install
npm run dev

如果出现错误 electron未安装
1.确保连接vpn
2.删除node_modules
3.pnpm store prune
4.查看d盘下面有没有
5.npm cache clean --force

4.pnpm install


6.npm run dev
