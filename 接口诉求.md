---一个项目下面有多个方案 一个方案对应一组语料---

1. 创建测评方案

/test_project/create_plan
payload:
{
    project_id: 'JYyfaL'
	plan_name: ‘aaa’
}


2. 修改测评方案
/test_project/update_plan
payload:
{
    plan_id: 'mdJflI',
    plan_name: 'bbb'
}

3. 查询测评方案列表
/test_project/get_plan_list
payload:
{}
return:
[{ plan_id: 'mdJflI', plan_name: 'bbb'}]

4. 删除测评方案
/test_project/delete_plan
payload:
{ 
    plan_id: 'mdJflI'
}

----- 查询和配置方案 -----
5. /test_project/get_plan_detail
payload:
{
    project_id: 'JYyfaL',
    plan_id: 'mdJflI'
}

return:
返回对应语料的id和play_config_id就行 因为这里只是作为选中key使用 不需要返回全部的
{
    "testCorpusList": [
        "W0iwxc"
    ],
    "rouseCorpusList": [
        "qJMFm1"
    ],
    "disturbCorpusList": [
        "qVB1i0"
    ],
    "backgroundNoiseList": [
        "MHNhtS"
    ],
    "play_config_id": 'WylcDt'
}

6. /test_project/save_plan_detail
当前方案选择的语料id 存起来就可以 和上面是一对
payload:
{
    project_id: 'JYyfaL',
    play_config_id: 'WylcDt'
    plan_id: 'mdJflI',
    "testCorpusList": [
        "W0iwxc"
    ],
    "rouseCorpusList": [
        "qJMFm1"
    ],
    "disturbCorpusList": [
        "qVB1i0"
    ],
    "backgroundNoiseList": [
        "MHNhtS"
    ]
}
----- 项目执行 -----
7. /test_project/start_test
根据项目下的所有方案内容 开始执行测试 每个方案都跑一遍 生成测试进度和结果列表
payload:
{
    project_id: 'JYyfaL',
}

8. /test_project/get_test_info
查询该项目 当前（最近一次执行）的测试信息 包括测试进度 结果列表 以及日志（实现多少是多少，但是可以先提供简单的接口，比如返回空list）
payload:
{
    project_id: 'JYyfaL',
}

return:
{
progress: 25, // 测试进度25%,
    status: 'progressing' // 'progressing' 'stopped' 'completed'
    result_list: [{
        result_id: '12345'
        time: '2024-09-09 14:54:51', 生成这条结果的时间
        scene: 'wake-up-test', // 唤醒测试
        corpus_id: '123456', // 语料id
        text: '你好，小迪', // 语料文本
        result: 'pass', // 'pass' 或者 'not_pass'
        ocr_pic_url: '/83901270498712.png',
        ocr_result: '你好，小迪' // ocr识别文字的结果,
        response_time: 200 // 测试的响应时间 毫秒 不好搞可以先留空,
        audio_url: 'record_1212_xxxx.wav'
    }],
    log: '2024-09-09 14:15:51 拨打电话给小王\n 2024-09-09 14:54:51 你好小迪' // 日志可以先不做,
    video_url: '2024-0909-测试过程录像.url',
}

/test_project/stop_test
停止该项目的测试 把status置为 ‘stopped’ （暂时不搞暂停执行的逻辑 中止了就只能重新开始）
payload:
{
    project_id: 'JYyfaL',
}

/test_project/result_update
结果复核接口 可以修改测试结果为通过或者不通过 支持批量复核 所以传的是result_ids
payload:
{
    project_id: 'JYyfaL',
    result_ids: ['12345'],
    result: 'pass'
}

## 下面这两个易茂早上说可以先不搞

/report/generate_report
生成测试报告
payload: 
{
    project_id: '123',
    project_name: '语音测试项目',
    report_name: '报告名称1',
    report_number: 'RE8217081297401823', // 报告编号
    test_agency: '东风', //检测单位
    test_indicators: [
        "interactionSuccessRate", // 交互成功率
        "averageResponseTime", // 平均响应时间
        "continuousConversationSuccessRate", // 连续对话成功率
        "multiCommandInterleavedExecutionSuccessRate", // 多指令交叉执行成功率
        "fuzzyCommandInteractionSuccessRate", // 模糊指令交互成功率
        "wakeUpSuccessRate", // 唤醒成功率
        "speechRecognitionSuccessRate", // 语音识别成功率
        "falseWakeUpRate", // 误唤醒率
        "wakeUpTime" // 唤醒时间
    ];
}
return:
{
    report_url: '/reports/语音测试项目_20200909112233报告.pdf'
}


/report/get_report_list
测试报告列表 每当完整执行完一个测试 需要插入一条测试报告记录
payload:
{
    project_name: 'aaa' // 模糊匹配测试项目名称aaa
}

return:
{
    data: [{
        report_id: '123',
        project_id: 'JYyfaL',
        project_name: '语音测试项目',
        report_status: 'generated', // 已生成: generated 未生成 not_generated
        review_status: 'reviewed', // 已复核
        report_url: '/reports/语音测试项目_20200909112233报告.pdf' // 如果未生成则为空字符串
        report_name: '报告名称1',
        report_number: 'RE8217081297401823', // 报告编号
        test_agency: '东风', //检测单位
        test_indicators: [
            "interactionSuccessRate", // 交互成功率
            "averageResponseTime", // 平均响应时间
            "continuousConversationSuccessRate", // 连续对话成功率
            "multiCommandInterleavedExecutionSuccessRate", // 多指令交叉执行成功率
            "fuzzyCommandInteractionSuccessRate", // 模糊指令交互成功率
            "wakeUpSuccessRate", // 唤醒成功率
            "speechRecognitionSuccessRate", // 语音识别成功率
            "falseWakeUpRate", // 误唤醒率
            "wakeUpTime" // 唤醒时间
        ];
    }]
}

## 播放配置

/play_config/create_play_config
添加一个空的播放配置

payload:
{
    config_name: '播放配置1',
    description: '配置描述',
    type: 'rouse', // 唤醒 'rouse' 交互（非唤醒） 'interaction'
}

/play_config/update_play_config
更新播放配置
payload:
{
    play_config_id: '12345',
    config_name: '播放配置2',
    description: '配置描述2',
    configs: [      
      { type: '开始', config: { circle: '3', channel: 'channel2', gain: -10, // 循环3次 } },  
      { type: '嵌入唤醒', config: { wakeUpWait: 500, frequency: 'first', frequency_interval: 3 } }, // frequency: 'first' 'every' 'interval'
      { type: '播放背景噪声', config: {   } },
      { type: '播放干扰音', config: {  } },
      { type: '播放语料', config: { repeat: 3 } },
      { type: '等待', config: { duration: 1000 } },
    ]
}

总共有六种不同的配置 每种配置有不同的表单属性 具体见播放配置的原型或页面

/play_config/delete_play_config
删除播放配置
payload:
{
    play_config_id: 'aaa'
}

/play_config/get_play_config_list
获取播放配置列表
payload:
{
    play_config_id: 'aaa'  选填
}
return: [{
    play_config_id: '12345',
    config_name: '播放配置2',
    description: '配置描述2',
    configs: [      
      { type: '开始', config: { circle: '3' // 循环3次 } },  
      { type: '嵌入唤醒', config: { wakeUpWait: 500, frequency: 'first', frequency_interval: 3 , channel: 'channel2', gain: -10} }, // frequency: 'first' 'every' 'interval'
      { type: '播放背景噪声', config: { channel: 'channel2', gain: -10 } },
      { type: '播放干扰音', config: { channel: 'channel2', gain: -10} },
      { type: '播放语料', config: { repeat: 3, channel: 'channel2', gain: -10 } },
      { type: '等待', config: { duration: 1000 } },
    ]
}]
